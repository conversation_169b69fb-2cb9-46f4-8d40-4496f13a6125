<template>
  <div class="choose_card">
    <center>请选择您要登录的司机卡：</center>
    <van-cell-group>
      <van-cell
        v-for="(item, index) in list"
        :key="index"
        @click="activedIndex = index"
        title="单元格"
        :class="activedIndex == index ? 'actived' : ''"
        label="描述信息"
      />
    </van-cell-group>
    <van-button type="warning" class="btn btnBc btnFixed" size="large">登录</van-button>
  </div>
</template>

<script>
export default {
  name: "chooseCard",
  components: {},
  props: {},
  data() {
    return {
      activedIndex: 0,
      // list:[...new Array(10).keys()]
      list: Array(5),
    };
  },
  watch: {},
  computed: {},
  methods: {},
  created() {},
  mounted() {},
};
</script>
<style lang="scss" scoped>
.choose_card {
  background: #ffffff;
  height: 100vh;
  width: 100vw;
  overflow: scroll;
  center {
    margin: 30px auto;
  }
  /deep/ .van-cell {
    background: #a39f9f;
    margin: 10px 0;
    text-indent: 30px;
  }
  /deep/ .van-cell__title {
    text-align: left;
    color: #ffffff;
  }
  /deep/ .van-cell__label {
    text-align: left;
    color: #ffffff;
  }
  .actived {
    background: #f59a23;
  }

}
</style>