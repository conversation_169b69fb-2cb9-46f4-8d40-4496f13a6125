<template>
  <div>
    <nav-bar title="安全验证" />
    <div class="login-card">
      <van-form @submit="onSubmit" validate-first label-width="70" :show-error-message="false" :show-error="false">
        <van-field v-model="formatPhone" label="手机号" clearable readonly placeholder="请输入" maxlength="11" center>
          <template slot="left-icon">
            <img src="@/assets/images/winter/login_phone.png" class="icon" />
          </template>
        </van-field>
        <van-field v-model="user.captcha" label="图片验证码" clearable placeholder="请输入" center :rules="[{ validator: validateCaptcha, trigger: 'onBlur' }]">
          <template slot="left-icon">
            <img src="@/assets/images/winter/login_yanzhengma.png" class="icon" />
          </template>
          <template #button>
            <div @click="changeCode">
              <v-sidentify :identifyCode="picCode"></v-sidentify>
            </div>
          </template>
        </van-field>

        <van-field v-model="user.smsCode" label="验证码" icon-prefix="my-icon" maxlength="4" clearable placeholder="请输入" center :rules="[{ validator: validateSms, trigger: 'onBlur' }]">
          <template slot="left-icon">
            <img src="@/assets/images/winter/login_password.png" class="icon" />
          </template>
          <template slot="button">
            <van-button size="small" style="width:80px" :color="$setting.themeColor" :loading="smsBtnLoading" type="warning" plain :disabled="!showSend" @click.prevent="checkAndSend">
              <div v-show="showSend" v-if="0 === clickNum">发送验证码</div>
              <div v-show="showSend" v-else>再次发送</div>
              <span v-show="!showSend">{{ count }}&nbsp;s</span>
            </van-button>
          </template>
        </van-field>

        <!-- 错误提示 -->
        <div class="err-tips">{{ errMsg }}</div>
        <!-- 登录按钮 -->
        <div class="btn-wrap">
          <van-button block round :color="$setting.themeColor" :loading="btnLoading" loading-text="登录中..." :disabled="isDisabled" size="large" native-type="submit">提&nbsp;交</van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>
<script>
import { Toast } from "vant";
import { mapState, mapMutations } from "vuex";
import navBar from "@/components/navBar/NavHeader";
import { storage, generateUUID } from "Utils/common/";
import Sidentify from "@/components/code/drawCodePic.vue"; // 画验证码组件
import filter from "@/filters/index.js";
import { sendCode, getCaptchaText, isPhoneExist, bindUserEquipment, getOpenCardList } from "./_service";
import { recordJsLog } from "@/utils/recordJsLog";

const TIME_COUNT = 59; //更改倒计时时间

export default {
  components: { navBar, "v-sidentify": Sidentify },
  data() {
    return {
      showFooter: true,
      btnLoading: false, // 按钮loading组织穿透
      smsBtnLoading: false,
      user: {
        phone: storage.ss.get("phone"),
        smsCode: "", // 短信验证码
        captcha: "", // 图形验证码
        type: this.$setting.APP_TYPE, // 1 单位 2司机
      },
      uuid2: "",
      picCode: "", // 图形验证码,传到画图的子组件,首次加载会被覆盖掉

      showSend: true, // 初始启用按钮
      clickNum: 0, // 点击发送验证码次数
      count: "", //初始化次数
      errMsg: "",
      // 设备编号
      deviceName: storage.ss.get("deviceInfo") ? storage.ss.get("deviceInfo").deviceId : "1", //避免在电脑网页开发时  没有唤起sdk造成无deviceInfo而报错
    };
  },
  computed: {
    formatPhone() {
      return filter.formatPhone(this.user.phone);
    },
    isDisabled() {
      return this.user.phone && this.user.smsCode && this.user.captcha ? false : true;
    },
  },
  created() {
    // 延时解决初次加载不出来问题
    setTimeout(() => {
      this.changeCode();
    }, 500);
  },
  mounted() {
    window.onRegister = this.onRegister;
    window.onHasCards2Reapply = this.onHasCards2Reapply; //迁移
  },
  methods: {
    ...mapMutations(["isQianYi_fn"]),

    /**
     * @description: 原生端SDK注册回调(userId会用于APP激活SDK，需监听回调onRegister)
     * @param {*} res
     * @return {*}
     */

    onRegister(res) {
      recordJsLog("onRegister", res);

      console.log("SafeChecking onRegister", JSON.stringify(res));
      if (res.error == 0) {
        // 调用原生迁移判断
        recordJsLog('hasCards2Reapply');

        this.$cppeiBridge("hasCards2Reapply", {});
      } else {
        this.$dialog
          .alert({
            title: "SDK注册失败,请重新登录",
            message: res.error + "-" + res.message,
          })
          .then(() => {
            this.reloadLogin();
          });
      }
    },

    /**
     * @description: onHasCards2Reapply 返回值为true 表示需要迁移
     * @param {*} obj
     * @return {*}
     */

    onHasCards2Reapply(obj) {
      recordJsLog("onHasCards2Reapply", obj);

      console.log("SafeChecking onHasCards2Reapply:" + JSON.stringify(obj));
      // error成功为0, 20000001-注册已失效需重新登录
      // error为0时原生端的register和判断是否迁移都没问题，这时直接通过result判断是否需要迁移。error不为0时通过message给出提示
      if (obj.error === 0) {
        if (obj.result === true) {
          this.isQianYi_fn(1);
          // 调用迁移判断需要迁移时更新状态为1
          recordJsLog("saveReapplyStatus", { reapplyStatus: 1 });

          this.$cppeiBridge("saveReapplyStatus", { reapplyStatus: 1 });
          this.loginCallback();
        } else {
          //  无错误，且待迁移判断是false，说明不需要迁移
          this.isQianYi_fn(2);
          recordJsLog("saveReapplyStatus", { reapplyStatus: 2 });

          this.$cppeiBridge("saveReapplyStatus", { reapplyStatus: 2 });
          this.loginCallback();
        }
      } else if (obj.error == "20000001") {
        this.$Dialog
          .alert({
            title: "提示",
            message: obj.error + "-" + "注册已失效需重新登录",
          })
          .then(() => {
            this.reloadLogin();
          });
      } else {
        this.$Dialog.alert({
          title: "提示",
          message: obj.error + "-" + obj.message,
        });
      }
    },

    /**
     * @description: 表单失焦判断
     * @return {*}
     */
    validateCaptcha(val) {
      if (!val) {
        this.errMsg = "*图片验证码不能为空";
        return false;
      } else {
        this.errMsg = "";
        return true;
      }
    },
    validateSms(val) {
      if (!val) {
        this.errMsg = "*短信验证码不能为空";
        return false;
      } else {
        if (val.length != 4) {
          this.errMsg = "*短信验证码错误";
          return false;
        } else {
          this.errMsg = "";
          return true;
        }
      }
    },

    /**
     * @description: 注册迁移回调完成后进行登录成功后的业务处理
     * @return {*}
     */

    loginCallback() {
      this.btnLoading = false;
      const defaultCompanyCode = storage.ss.get("defaultCompanyCode");
      if (!defaultCompanyCode) {
        // 跳到实名认证
        this.$router.push({
          path: "/identityCardOCR",
        });
      } else {
        // 已开通,未激活
        this.getOpenCard({ phone: this.user.phone, type: this.user.type });
      }
    },

    /**
     * @description: 重新登录
     * @return {*}
     */

    reloadLogin() {
      this.$cppeiBridge("clearToken", {});
      storage.ss.remove("apptoken");
      storage.ss.clear();
      this.$router.replace({
        path: "/loginCenter",
      });
    },

    /**
     * @description: 刷新验证码
     * @return {*}
     */

    changeCode() {
      var uuid = `${generateUUID()}`;
      this.uuid2 = uuid;
      // 调用后台接口
      getCaptchaText({
        uuid: uuid
      })
        .then((res) => {
          this.picCode = res.data;
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 校验&发短信
     * @return {*}
     */

    checkAndSend() {
      if ("" === this.user.captcha) {
        this.errMsg = "* 请填写图片验证码";
        return;
      }
      
      this.errMsg = "";
      // 发送验证码
      this.checkAndSendCode();
    },

    /**
     * @description: 校验后发送验证码
     * @return {*}
     */

    checkAndSendCode() {
      // 验证是否有权限
      isPhoneExist({
        phone: this.user.phone,
        type: this.user.type,
      })
        .then((res) => {
          if (1001 === res.infoCode) {
            this.$router.push({ path: "/noAuth" }); // 跳到未授权提示页面
          } else {
            this.sendCodeByPhone(); // 发送短信验证码
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 发送短信验证码
     * @return {*}
     */

    sendCodeByPhone() {
      this.smsBtnLoading = true;
      // 发送短信验证码
      sendCode({
        phone: this.user.phone,
        captcha: this.user.captcha,
        type: this.user.type,
        deviceName: storage.ss.get("deviceInfo")?.deviceId ? storage.ss.get("deviceInfo")?.deviceId : "1",
        uuid: this.uuid2,
      })
        .then((res) => {
          if (!res.value) {
            setTimeout(() => {
              this.changeCode();
            }, 500);
            Toast(res.info);
          } else {
            this.countdown(); // 倒计时60秒
            setTimeout(() => {
              // 必须延时关闭弹窗
              Toast(`验证码已发送至: ${this.user.phone.substring(0, 3)}****${this.user.phone.substring(7, 11)}`);
            }, 300);
          }
          this.smsBtnLoading = false;
        })
        .catch((err) => {
          setTimeout(() => {
            this.changeCode();
          }, 500);
          this.smsBtnLoading = false;
          console.log(err);
        });
    },

    /**
     * @description: 倒计时
     * @return {*}
     */

    countdown() {
      this.clickNum++;
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.showSend = false;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.showSend = true;
            clearInterval(this.timer); // 清除定时器
            this.timer = null;
          }
        }, 1000);
      }
    },

    /**
     * @description: 登录
     * @return {*}
     */

    onSubmit() {
      this.btnLoading = true;
      bindUserEquipment({
        apptoken: storage.ss.get("apptoken"),
        equipmentNo: storage.ss.get("deviceInfo")?.deviceId ? storage.ss.get("deviceInfo")?.deviceId : "1",
        phone: this.user.phone,
        smsCode: this.user.smsCode,
        type: this.user.type,
        uuid: this.uuid2,
        captcha: this.user.captcha,
      })
        .then((res) => {
          if (!res.value) {
            setTimeout(() => {
              this.changeCode();
            }, 500);
            Toast(res.info);
          } else {
            // 调用原生保存
            recordJsLog("saveInfo", {
              token: storage.ss.get("apptoken"),
              userId: storage.ss.get("userId"),
            });
            
            this.$cppeiBridge("saveInfo", {
              token: storage.ss.get("apptoken"),
              userId: storage.ss.get("userId"),
            });
          }
        })
        .catch((err) => {
          console.log(err);
          this.btnLoading = false;
        });
    },

    /**
     * @description: 获取已开通未激活的卡片
     * @param {*} params
     * @return {*}
     */

    getOpenCard(params) {
      getOpenCardList(params)
        .then((res) => {
          if (res.infoCode === 1) {
            const defIsOpen = res.data.some((item) => item.companyCode == storage.ss.get("defaultCompanyCode"));
            if (defIsOpen) {
              this.$router.replace({
                path: "/chooseEnterprise",
                query: {
                  isActive: 2,
                },
              });
            } else {
              // 跳到首页
              this.$router.replace({ path: "/gasstationmap" });
            }
          } else {
            Toast(res.info);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.login-card {
  background: #fff;
  margin: 0 20px;
  margin-top: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding-bottom: 80px;
  .icon {
    width: 27.5px;
    height: 29.5px;
    vertical-align: sub;
  }
  .err-tips {
    color: #ee0a24;
    // line-height: 1.4;
    padding: 30px 30px 0;
    text-align: left;
    min-height: 20px;
    font-size: 24px;
  }
  .btn-wrap {
    padding: 0 20px;
    position: relative;
    bottom: -48px;
    // bottom: 20px;
    .van-button--disabled {
      // 按钮透明度
      // opacity: 1;
    }
  }
  /deep/ .van-cell {
    background: none;
    #pic-code {
      width: 100px;
      height: 32.5px;
    }
  }
}
</style>
