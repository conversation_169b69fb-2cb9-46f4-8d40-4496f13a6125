<template>
  <div class="device-management">
    <nav-bar title="登录设备管理" />
    <!-- 列表 -->
    <van-pull-refresh v-model="isLoading" @refresh="onRefresh">
      <div class="container">
        <div class="no-record" v-if="!hasData">无登录设备信息</div>
        <template v-else>
          <van-list
            v-model="loading"
            :finished="finished"
            finished-text="- 没有更多了 -"
            @load="onLoad"
            :immediate-check="false"
            :offset="50"
          >
            <div
              class="device-list"
              v-for="item in deviceList"
              :key="item.loginTime"
            >
              <div class="order-item">
                <span>
                  {{
                    null === item.phoneName ? "未知设备" : item.phoneName
                  }}</span
                >
              </div>
              <div class="order-time">
                <span
                  >{{ item.loginTime }}
                  {{ null === item.city ? "未知地点" : item.city }}</span
                >
              </div>
            </div>
          </van-list>
        </template>
      </div>
    </van-pull-refresh>
  </div>
</template>

<script>
import navBar from "@/components/navBar/NavHeader";
import { getEquipments } from "./_service/";
import { storage } from "Utils/common/";

export default {
  props: {},
  components: { navBar },
  data() {
    return {
      // 设备编号
      deviceName: storage.ss.get("deviceInfo")
        ? storage.ss.get("deviceInfo").deviceId
        : "1",
      deviceList: [],
      // 每页条数
      pageSize: 20,
      page: 1, //当前页
      loading: false, // 当loading为true时，转圈圈
      finished: false, // 数据是否请求结束，结束会先显示- 没有更多了 -
      hasData: true, // 如果没有数据，显示暂无数据
      isLoading: false, // 下拉的加载图案
    };
  },
  computed: {},
  created() {
    this.onRefresh();
  },
  activated() {
    this.onRefresh();
  },
  mounted() {},
  watch: {},
  methods: {
    // 列表加载
    onLoad() {
      setTimeout(() => {
        this.getListByPage();
        this.loading = true;
      }, 300);
    },
    onRefresh() {
      setTimeout(() => {
        // 重新初始化这些属性
        this.isLoading = false;
        this.deviceList = [];
        this.page = 1;
        this.loading = false;
        this.finished = false;
        this.hasData = true;
        // 查询数据
        this.getListByPage();
      }, 300);
    },
    // 分页查询数据
    getListByPage() {
      getEquipments({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        phone: storage.ss.get("phone"),
        userid: storage.ss.get("userId"),
        channel: this.$setting.APP_TYPE + "",
        // 当前页
        curPage: this.page,
        // 每页大小
        pageSize: this.pageSize,
      })
        .then((res) => {
          if (res.infoCode == 1) {
            // 追加数据
            this.deviceList = this.deviceList.concat(res.data.items);
            // 加载状态结束
            this.loading = false;
            // 如果没有数据，显示暂无数据
            if (this.deviceList.length == 0 && this.page == 1) {
              this.hasData = false;
            }
            // 页码加1
            this.page++;
            // 如果加载完毕，显示没有更多了
            if (res.data.items.length == 0) {
              this.finished = true;
            }
            if (res.data.items.length < 20) {
              this.finished = true;
            }
          } else {
            if(this.deviceList.length == 0) {
              this.hasData = false;
            }
            this.finished = true;
          }
        }).catch((err) => {
          this.finished = true
          this.loading = false;
          this.refreshing = false;
        });
    },
  },
};
</script>

<style scoped lang="scss">
.device-management {
  /deep/ .van-pull-refresh {
    // 控制中间滚动
    height: 635px;
    @include useScrollByBrowser(0);
    .container {
      .no-record {
        background-color: #fff;
        padding: 30px 0;
        margin-top: 2px;
      }
      .van-list {
        .device-list {
          height: auto;
          margin-bottom: 2px;
          padding: 10px;
          background-color: #fff;
          .order-item,
          .order-time {
            height: 25px;
            display: flex;
            justify-content: space-between;
          }
        }
      }
    }
  }
}
</style>
