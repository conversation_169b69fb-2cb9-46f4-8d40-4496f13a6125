<template>
  <div class="open-new-card">
    <nav-bar title="开通新的司机卡" />
    <div class="choose">请选择您要开通的企业:</div>
    <div class="company-list" v-if="companyList.length">
      <div v-for="(item, index) in companyList" :key="index">
        <p :class="checkedList.includes(item) ? 'actived' : ''" @click="checkListIndex(item)">{{ item.companyName }} - {{ item.companyCode }} - {{ item.name }}</p>
      </div>
    </div>
    <van-empty v-else description="暂无数据" />

    <van-button :disabled="isDisabled" type="warning" class="btn btnBc btnFixed" size="large" @click="openCards" v-if="companyList.length">
      {{ count == 0 ? "开通" : count + "s" }}
    </van-button>
  </div>
</template>

<script>
import navBar from "@/components/navBar/NavHeader";
import { getUnActivatedList, doOpenCards, changeState } from "./_service/";
import { storage } from "Utils/common/";

import { Toast } from "vant";
import filter from "@/filters/index.js";
import { recordJsLog } from "@/utils/recordJsLog";

//更改倒计时时间
const TIME_COUNT = 29;

export default {
  props: {},
  components: {
    navBar,
  },
  data() {
    return {
      companyList: [],
      checkedList: [],
      // 电子卡卡号数组
      cardAsns: [],
      // 企业编号数组
      // companyCodes: [],
      driverInfo: storage.ss.get("driverInfo"),
      // 激活次数,2次激活后仍然不成功,跳到我的界面
      activeNum: 0,
      isDisabled: false,
      timer: null, // 定时器
      count: 0, //倒计时秒
    };
  },
  computed: {
    companyCodeList: function() {
      var companyList = [];
      for (var i = 0; i < this.checkedList.length; i++) {
        var ele = this.checkedList[i];
        companyList.push(ele.companyCode);
      }
      return companyList;
    },
  },
  created() {
    this.driverInfo = storage.ss.get("driverInfo");
    this.loadData();
  },
  activated() {
    this.driverInfo = storage.ss.get("driverInfo");
  },
  mounted() {
    // 在window上挂载和Native统一的方法名，onLocalAuthentication（如在mounted中进行挂载）
    // 右侧this.onLocalAuthentication为Vue端具体实现接收的方法
    window.onActivateCards = this.onActivateCards;
  },
  // 在组件生命周期结束的时候销毁。
  //   destroyed() { 
  //     console.log('OpenNewCard beforeDestroy销毁：onActivateCards');
  //     window.removeEventListener('onActivateCards', this.onActivateCards())
  //     console.log(window.onActivateCards);
  //   },
  watch: {},
  methods: {
    /**
     * @description:  注册卡sdk回调
     * @param {*} params
     * @return {*}
     */
    onActivateCards(params) {
      recordJsLog('onActivateCards', params);
      console.log(params.route + ' ==> onActivateCards ====>', JSON.stringify(params));

      if(params.route === 'openNewCard'){
        //激活卡片后禁用button，SDK回调后消失
        // this.isDisabled = false;
        params.successCards = params.successCards ? params.successCards : [];
        params.failedCards = params.failedCards ? params.failedCards : [];

        if (params.successCards.length > 0) {
          // 调用后台设置激活状态
          this.changeState(params.successCards);

          // 部分激活失败
          if (params.failedCards.length > 0) {
            this.$Dialog
              .alert({
                title: "下列卡片设置支付密码失败,请稍后在【我的】界面重新设置",
                message: filter.errorMsg(params.failedCards), // 修改错误提示信息格式 - add by yl - 2022.5.5
              })
              .then(() => {
                this.$router.go(-1);
              });
          } else {
            this.$toast("设置支付密码成功");
            setTimeout(() => {
              this.$router.go(-1);
            }, 1200);
          }
        } else {
          this.activeNum++;
          this.$Dialog
            .alert({
              title: "设置支付密码失败",
              message: filter.errorMsg(params.failedCards) + "请点击【确认】再次设置", // 修改错误提示信息格式 - add by yl - 2022.5.5
            })
            .then(() => {
              // sdk激活
              this.sdkActive();
            });
        }
      }
    },
    /**
     * @description: 点击选择卡片
     * @param {*} item
     * @return {*}
     */
    checkListIndex(item) {
      if (this.checkedList.includes(item)) {
        this.checkedList.splice(this.checkedList.indexOf(item), 1);
      } else {
        if (this.checkedList.length < 5) {
          this.checkedList.push(item);
        } else {
          Toast("最多可选择5个企业!");
        }
      }
    },

    /**
     * @description: 调用后台设置激活状态
     * @param {*} params
     * @return {*}
     */
    changeState(params) {
      changeState({
        phone: storage.ss.get("phone"),
        companyCodeList: Array.isArray(this.companyCodeList) ? this.companyCodeList : this.companyCodeList.split(","),
        cardNoList: params,
        type: this.$setting.APP_TYPE,
      })
        .then((res) => {
          if (res.infoCode !== 1) {
            Toast(res.info);
          }
        })
        .catch((err) => {
          Toast(err);
        });
    },

    /**
     * @description: 获取未激活企业列表
     * @return {*}
     */
    loadData() {
      getUnActivatedList({
        curPage: 1,
        pageSize: 10,
        phone: storage.ss.get("phone"),
        type: this.$setting.APP_TYPE,
      })
        .then((res) => {
          this.companyList = res.data;
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 开卡
     * @return {*}
     */
    openCards() {
      if (this.companyCodeList.length < 1) {
        Toast("至少选择1个企业!");
        return;
      }

      // 判断当前认证所属司机信息与激活司机信息是否一致
      const everyResult = this.checkedList.every( item => item.idCard === storage.ss.get("driverInfo").idNo)
      console.log('OpenNewCard 判断当前认证所属司机信息与激活司机信息是否一致:'+ everyResult);
      if(!everyResult){
        this.$Dialog.alert({
          title: "提示",
          message: "当前认证司机信息与激活司机信息不一致，请重新选择！",
        })
        return
      }
      if (this.activeNum > 3) {
        this.logOutDialog();
        return;
      }

      // 倒计时30秒
      this.countdown();

      doOpenCards({
        authToken: "",
        companyCodeList: Array.isArray(this.companyCodeList) ? this.companyCodeList : this.companyCodeList.split(","),
        idCard: this.driverInfo.idNo,
        phone: this.driverInfo.phone,
        type: this.$setting.APP_TYPE,
        userId: 1,
      })
        .then((res) => {
          if (res.infoCode === 1) {
            // 获取卡号
            if (res.data?.driverList) {
              for (let e of res.data?.driverList) {
                this.cardAsns.push(e.cardAsn);
              }
            }
            // 调用原生
            if (this.cardAsns.length > 0) {
              // sdk激活
              this.sdkActive();
            } else {
              this.$Dialog.alert({
                title: "温馨提示",
                message: "无电子卡号,无法进行激活",
              });
            }
          } else {
            this.$Dialog.alert({
              title: "温馨提示",
              message: res.info,
            });
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 失败-给出提示，3次或5次后弹出建议重新登录
     * @return {*}
     */
    logOutDialog() {
      this.$Dialog
        .confirm({
          title: "提示",
          message: "电子卡多次激活失败，建议重新登录",
        })
        .then(() => {
          this.$cppeiBridge("clearToken", {});
          storage.ss.remove("apptoken");
          storage.ss.clear();
          this.$router.replace({
            path: "/loginCenter",
          });
        })
        .catch(() => {});
    },

    /**
     * @description: 调用sdk激活
     * @return {*}
     */
    sdkActive() {
      // 激活卡片后禁用button，SDK回调后消失
      this.isDisabled = true;
      recordJsLog('activateCards', {
        pans: this.cardAsns,
        route: 'openNewCard'
      });

      this.$cppeiBridge("activateCards", {
        pans: this.cardAsns,
        route: 'openNewCard'
      });
    },

    /**
     * @description: 倒计时60秒
     * @return {*}
     */
    countdown() {
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.isDisabled = true;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.isDisabled = false;
            clearInterval(this.timer); // 清除定时器
            this.timer = null;
          }
        }, 1000);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.open-new-card {
  height: auto;

  .choose {
    text-align: left;
    margin: 30px 0 30px 25px;
  }

  .company-list {
    // 控制中间滚动
    @include useScrollByBrowser(0);
    height: 104vw;
    padding: 0 25px 25px 25px;

    p {
      height: 50px; // 100
      line-height: 50px;

      .actived {
        background-color: #ff9200;
      }

      // .no {
      // }
      background-color: #a39f9f;
      color: white;
    }
  }

  /deep/ .van-button {
    background-color: rgba(255, 146, 0, 100%) !important;
    font-size: 16px;
    color: white;
    width: 95%;
    margin-left: 10px;
  }
}
</style>
