<!--
 * @Author: Yongon
 * @Date: 2022-07-18 16:15:24
 * @LastEditors: ltw <EMAIL>
 * @LastEditTime: 2024-01-17 15:11:53
 * @Description: 首页
-->
<template>
  <div class="container">
    <!-- 顶部企业切换 -->
    <change-company ref="changeCompanyRef" @updateInfo="GetUserCardInfo" />

    <!-- 百度地图 -->
    <!-- v-if="oilStationList.length > 0" -->
    <!-- <baidu-map v-if="oilStationList.length > 0" :center="oilStationList" :currentOilStation="currentOilStation" /> -->
    <baidu-map v-if="Object.keys(currentOilStation).length > 0" :center="oilStationList" :currentOilStation="currentOilStation" />

    <!------------- 预授权加油部分  --------------->
    <van-popup v-if="addoiltype == 1" class="popupoilpre" v-model="popupVisible" :overlay="false" position="bottom" round :style="{ bottom: '0px' }">
      <!-- 预授权加油操作部分 -->
      <oil-select v-if="!showPreInfo" @oilSelectFunction="oilSelectFunction" :oilStationList="oilStationList" :currentOilStation="currentOilStation" :oilType="oilType" :oilTypeNum="oilTypeNum" :amountMoeny="amountMoeny" :cardInfo="cardInfo" />

      <!-- 预授权加油订单信息展示部分 -->
      <oil-code v-else @oilCodeCallback="oilCodeCallback" :preOrderStationInfo="preOrderStationInfo" :fishPreOrderInfo="fishPreOrderInfo" />
    </van-popup>

    <!------------- 不下车加油部分  --------------->
    <!-- <van-popup v-if="addoiltype == 2 && currentOilStation" class="popupoilpay" v-model="popupVisible" :overlay="false" position="bottom" round :style="{ bottom: bottomHeight + 'px' }"> -->
    <!-- 不下车加油操作部分 -->
    <!-- <oil-noCard :oilStationList="oilStationList" :currentOilStation="currentOilStation" :oilType="oilType" :oilTypeNum="oilTypeNum" :amountMoeny="amountMoeny" :cardInfo="cardInfo" /> -->
    <!-- </van-popup> -->

    <!-- 迁移验证码 -->
    <transfer ref="transferHomeRef"/>
  </div>
</template>

<script>
import { storage, compareVersions } from "Utils/common/";
import { mapState, mapMutations } from "vuex";
import eventBus from "./_service/eventbus.js";
import gasStationMixin from "./mixins/mixin.js";

import Transfer from "@/components/transfer/index.vue";
import changeCompany from "./component/changeCompany.vue";
import baiduMap from "./component/baiduMap.vue";
import oilSelect from "./component/oilSelect";
import oilCode from "./component/oilCode";
import oilNoCard from "./component/oilNoCard";

import { GetUserCardInfo, PreAuthOrderQuery } from "./_service";
import { useGetLocation } from '@/utils/useGetLocation';
import { recordJsLog } from "@/utils/recordJsLog";
import { payRiskManagement } from "Utils/payRiskManagement";

export default {
  name: "gasstationmap",
  mixins: [gasStationMixin],
  components: {
    changeCompany,
    Transfer,
    baiduMap,
    oilSelect,
    oilCode,
    oilNoCard,
  },
  data() {
    return {
      showPreInfo: false, //是否显示与加油信息 即 下单后显示
      showkeyboard: false, //显示键盘与否
      cardInfo: {}, //加油卡信息
      // showLicense: false, //显示车牌与否
      amountMoeny: "", //价格
      oilTypeNum: 0,
      oilType: _const.oilType,
      oilAmountNum: 0,
      oilAmount: [300, 500, 1000, 1500],
      showModel: true,
      preOrderInfo: {}, //预授权下单信息
      fishPreOrderInfo: {}, //完成预下单信息
      ifPollingBoole: true, //是否轮询
      pollingTimes: 0, //轮循次数
      countDownVal: "", //预下单成功后倒计时
      preOrderStationInfo: {}, //预下单的加油站信息
      checkOilStation: false, //是否切换油站 防止切换油站回来再次请求GetNavigationList
      timer: null,
      popupVisible: true, // 底部弹窗，默认直接展示
      gunNoarr: "", // 油品对应的枪号数据
      gunNo: "", // 手动输入的枪号
      oilGunNum: 0, // 选中的枪号下标,10000为手动输入油枪号
      bottomHeight: 60, // 首页的加油与底部的安全距离

      // app监测相关
      hiddenProperty: null,
      visibilityChangeEvent: null,
      isDisabled: false,
      payRiskState: null, // 支付风控状态
      orderPayData: {}, // 下单数据，用于支付
      orderData: {}, // 订单数据
    };
  },
  computed: {
    ...mapState(["rstParams", "deviceInfo", "isQianYi"]),
  },
  watch: {
    isQianYi() {
      console.log("home watch, isQianYi: " + this.isQianYi);
      this.has2Reapply();
    },
  },
  created() {
    console.log("home created");
  },
  mounted() {
    console.log("home mounted");

    // 计算更换加油站的事件 在activated中触发，每切换一次就会多执行一次事件
    eventBus.$on("currentOilPos", (data) => {
      // console.log("eventBus currentOilPos:" + JSON.stringify(data));
      this.checkOilStation = true;
      this.currentOilStation = data;
      console.log("this.oilStationList.length:" + this.oilStationList.length);
      // this.getOilCodeListByStationCode();
      if (this.checkOilStation) {
        this.getOilCodeListByStationCode();
      }
    });
  },
  activated() {
    console.log("home activated");
    window.onConfirmPreTrade = this.onConfirmPreTrade;
    window.onGetLocation = this.onGetLocation;
    window.onActivateCards = this.onActivateCards;
    window.onSetDefaultCard = this.onSetDefaultCard;
    window.onDeviceFinger = this.onDeviceFinger;
    // 如果还是判断迁移状态，不调后面的接口等操作
    if (this.isQianYi == 1) {
      return;
    }
    this.has2Reapply();
    // 监听锁屏开屏，屏上屏下切换
    this.watchScreenSwitching(true);
  },
  deactivated() {
    console.log("home deactivated");
    this.clearTimer();
    this.watchScreenSwitching(false);
  },
  methods: {
    ...mapMutations(["deviceInfo_fn", "cardNo_fn", "carNo_fn"]),
     /**
     * @description: 设置支付密码 - sdk回调
     * @return {*}
     */

    onActivateCards(params) {
      recordJsLog("onActivateCards", params);
      console.log(params.route + ' ==> onActivateCardsFn ====>', JSON.stringify(params));

      if(params.route === 'changeCompany'){
        this.$refs.changeCompanyRef.onActivateCardsFn(params)
      }
    },

    /**
     * @description: 原生端SDK注册回调(userId会用于APP激活SDK，需监听回调onRegister)
     * @param {*} res
     * @return {*}
     */
    onRegister(res) {
      recordJsLog('onRegister', res);

      console.log("home onRegister", JSON.stringify(res));
      if (res.error == 0) {
        this.$dialog
          .alert({
            title: "提示",
            message: "已注册成功！请继续操作",
          })
          .then(() => {});
      } else {
        this.$dialog
          .alert({
            title: "温馨提示",
            message: `<h4 style="margin:-5px 0;text-align:left">SDK注册失败,请多次尝试，如果多次尝试失败请联系相关管理人员</h4>` + `<p>` + res.error + "-" + res.message + `</p>`,
          })
          .then(() => {
            this.reloadLogin();
          });
      }
    },

    /**
     * @description: 预授权加油输入密码后的回调
     * @return {*}
     */
    onConfirmPreTrade(obj) {
      recordJsLog('onConfirmPreTrade', obj);

      console.log("home onConfirmPreTrade111:" + JSON.stringify(obj));
      if (this.$route.path === "/gasstationmap") {
        //! add by kj - 激活卡片后禁用button，SDK回调后消失（2022.1.30）
        this.isDisabled = false;
        if (obj.error === 0) {
          this.PreAuthOrderQuery(false); //单次查询是否存在预授权加油订单
        } else if (obj.error === "71311001") {
          this.$dialog
            .alert({ title: "提示", message: "由于长时间未使用，移动支付安全组件已失效，需重新注册，否则无法使用支付相关功能", showCancelButton: true, messageAlign: "left" })
            .then(() => {
              recordJsLog('saveInfo');

              this.$cppeiBridge("saveInfo", {});
            })
            .catch(() => {
              // on cancel
            });
        } else {
          // 模拟软件商店审核测试人员进行上线审核
          if (this.$setting.AuditPhone === storage.ss.get("phone")) {
            this.$dialog.alert({ title: "提示", message: "油卡移动支付未开通，请线下开通移动支付" }).then(() => {});
          } else {
            this.$dialog.alert({ title: "预授权加油失败", message: obj.error + "-" + obj.message }).then(() => {});
          }
        }
      }
    },

    /**
     * @description: 切换企业 - 更改sdk默认电子卡号
     * @param {*} params
     * @return {*}
     */

    onSetDefaultCard(params) {
      recordJsLog('onSetDefaultCard', params);
      console.log(params.route + ' ==> onSetDefaultCard ====>', JSON.stringify(params));

      if(params.route === 'changeCompany'){
        this.$refs.changeCompanyRef.onSetDefaultCardFn(params)
      }
      if(params.route === 'transfer'){
        this.$refs.transferHomeRef.onSetDefaultCardFn(params)
      }
    },

    /**
     * @description: SDK回调定位信息
     * @param {*} params
     * @return {*}
     */

    onGetLocation(params) {
      useGetLocation(params, this.callBackFn);
    },  

    /**
     * @description: 定位回调具体页面处理逻辑
     * @param {*} params
     * @return {*}
     */
    callBackFn(bool){
      if(bool){
        if (this.isQianYi == 2) {
          console.log("home, onGetLocaiton success GetNavigationList:" + this.checkOilStation);
          // 判断是否需要重新调用油站接口，油站切换和详情页加油进来不需要重新调用油站接口，只需要调用油品接口即可
          if (!this.checkOilStation) {
            this.GetNavigationList();
          }
          // this.GetUserCardInfo();
        }
      }else{
        // 手动打开，重新赋值默认经纬度，请求油站地图信息
        this.GetNavigationList();
      }
    },

    has2Reapply() {
      // 迁移状态，0-默认状态, 1-需要迁移, 2-已迁移
      console.log("home has2Reapply isQianYi: " + this.isQianYi);
      if (this.isQianYi == 2) {
        this.activeMethods();
        // 防止切换油站频繁加载油站信息
        // if(!this.checkOilStation){
        // this.GetNavigationList();
        // }
        this.GetUserCardInfo();
      }
    },

    /**
     * @description: keep-alive数据缓存执行方法
     * @return {*}
     */
    activeMethods() {
      console.log("activeMethods:" + this.$route.query.isStoplocation);
      
      // 根据地域获取参数addoiltype，判断使用不下车加油或预授权加油
      // this.getFuelTypeByProvince();

      setTimeout(() => {
        // 智慧后台配置 1预授权加油或者  2不下车加油;默认显示预约加油
        if (this.addoiltype == 1) {
          this.isDisabled = false;
          this.preOrderInfo = {};
          this.PreAuthOrderQuery(false);
        } else {
          // this.getOilProductlist();
        }
        document.querySelector(".van-tabbar--fixed").style.paddingBottom = storage.ss.get("paddingBottom");
        document.querySelector(".companybox").style.paddingTop = Number(storage.ss.get("statusHeight") ?? 40) + "px";
      }, 500);

      // 当切换油站的时候不触发重新定位
      if (!this.$route.query.isStoplocation) {
        setTimeout(() => {
          recordJsLog('getLocation', {route: 'gasstationmap'});

          this.$cppeiBridge("getLocation", {route: 'gasstationmap'});
        }, 100);
      }
    },

    /**
     * @description: 监听锁屏开屏，屏上屏下切换
     * @return {*}
     */
    watchScreenSwitching(bool) {
      this.hiddenProperty = "hidden" in document ? "hidden" : "webkitHidden" in document ? "webkitHidden" : "mozHidden" in document ? "mozHidden" : null;
      this.visibilityChangeEvent = this.hiddenProperty.replace(/hidden/i, "visibilitychange");
      if(bool){
        document.addEventListener(this.visibilityChangeEvent, this.onVisibilityChange);
      }else{
        document.removeEventListener(this.visibilityChangeEvent, this.onVisibilityChange);
      }
    },

    /**
     * @description: 锁屏开屏，屏上屏下切换激发操作
     * @return {*}
     */
    onVisibilityChange() {
      // console.log(JSON.stringify(this.$route));
      if (!document[this.hiddenProperty]) {
        // this.$toast("首页激活");
        recordJsLog('getLocation', {route: 'gasstationmap'});
        
        this.$cppeiBridge("getLocation", {route: 'gasstationmap'});
      } else {
        // this.$toast("首页隐藏");
      }
    },
 // 预授权下单事件回调
    oilSelectFunction(order) {
      if(!order) return;
      this.orderPayData = order;
      console.log(this.orderPayData,'this.orderPayData');
      this.payRiskState = 2;
      recordJsLog('deviceFinger', {});
      this.$cppeiBridge("deviceFinger", {});
    },
    /**
     * 设备指纹识别回调
     *
     * @param obj - 回调对象，包含错误信息和设备信息
     */
    onDeviceFinger(obj) {
      recordJsLog("onDeviceFinger", obj);
      console.log(this.payRiskState,"login----onDeviceFinger", obj);
      
      if (obj.error === "") {
        if (this.payRiskState === 2) {
          payRiskManagement(this.orderPayData.outOrderNo, "", 1 ,obj.message ).then(
            (res) => {
              console.log(res,'payRiskManagement');
              
              if (!res) {
                console.log(
                  "oilSelect confirmPreTrade Params ====> ",
                  JSON.stringify({
                    pan: "123123",
                    order: JSON.stringify(this.orderPayData),
                  })
                );
                recordJsLog("confirmPreTrade", {
                  pan: "123123",
                  order: JSON.stringify(this.orderPayData),
                });

                // promise 回调人res为false，则不阻断，true阻断
                this.$cppeiBridge("confirmPreTrade", {
                  pan: "123123",
                  order: JSON.stringify(this.orderPayData),
                });
              }
            }
          );
        } else if (this.payRiskState === 0) {
          payRiskManagement(this.orderData.preAuthOrderNo, 0, 1 ,obj.message);
        } else if (this.payRiskState === 1) {
          payRiskManagement(this.orderData.preAuthOrderNo, 1, 1 ,obj.message);
        } else {
          this.$dialog
            .alert({
              title: "提示",
              message: obj.error + "-" + obj.message,
            })
            .then(() => {});
        }
      } else {
        this.$dialog
          .alert({
            title: "提示",
            message: obj.error + "-" + obj.message,
          })
          .then(() => {});
      }
    },
    /**
     * @description: 预下单查询
     * @param {*} bol 表示轮循环 true表示轮循 false表示直接查询
     * @param {*} orderNo 订单号
     * @param {*} time time 表示预下单成功后的时间
     * @return {*}
     */
    PreAuthOrderQuery(bol, orderNo, time) {
      return PreAuthOrderQuery({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        preAuthOrderNo: orderNo == null ? this.preOrderInfo?.preAuthOrderNo || "" : orderNo,
        carNo: this.rstParams.carNo,
        queryType: this.rstParams.queryType,
        channel: this.rstParams.channel,
        ifPolling: bol,
      }).then((res) => {
        if (res.infoCode == 1) {
          const list = res.data.dataList;
          // 下单的数据列表 目前智慧后台只允许一个userid存在一个预授权加油订单
          if (list.length > 0) {
            // 只要infoCode为1，即可保证数组有一个数据,非轮询是返回当前的预授权订单，轮询是返回是否支付的订单信息
            const [order] = res.data.dataList;
            // 如果已经有了加油码，且是轮询是否支付的状态，则走轮询逻辑
            // preAuthOrderStatus订单支付状态 1等待付款 2预授权成功 3预授权失败  4 支付成功 5支付失败6撤销成功 7撤销失败 8已锁定'
            if (bol === true) {
              this.orderData = order ;
              if (order.preAuthOrderStatus === "4") {
                // 如果核销成功，则取消加油码显示
                this.showPreInfo = false;
                this.amountMoeny = "";
                this.clearTimer();
                this.$toast("核销成功");
                if(compareVersions(storage.ss.get("deviceInfo").version || '', '1.1.2') >= 0) {
                  this.payRiskState = 0;
                  recordJsLog('deviceFinger', {});
                  this.$cppeiBridge("deviceFinger", {});
                } else {
                  payRiskManagement(order.preAuthOrderNo, 0, 1);
                }
                // this.$Dialog.confirm({title:JSON.stringify(order)}).then(()=>{
                this.$router.push({
                  path: "/preOrderPayResult",
                  query: { tradeOrderMix: JSON.stringify(order) },
                });
                // })
              } else if (order.preAuthOrderStatus === "2" || order.preAuthOrderStatus == "8") {
                this.showPreInfo = true;
                this.isPollingFun();
              } else {
                this.showPreInfo = false;
                this.clearTimer();
                if(compareVersions(storage.ss.get("deviceInfo").version || '', '1.1.2') >= 0) {
                  this.payRiskState = 1;
                  recordJsLog('deviceFinger', {});
                  this.$cppeiBridge("deviceFinger", {});
                } else {
                  payRiskManagement(order.preAuthOrderNo, 1, 1);
                }
              }
            } else {
              // 没有显示预授权码，或者没有轮询，则仅做单次的是否存在预授权下单的查询
              if (order.preAuthOrderStatus === "2" || order.preAuthOrderStatus == "8") {
                // 预授权成功，显示加油码界面
                //显示预订单
                this.showPreInfo = true;
                this.fishPreOrderInfo = order;
                // 开始轮询预授权加油是否支付
                this.isPollingFun();
                // 根据油站编码查询油站详细信息 因为获取油站列表有延迟，这里跟着也需要延迟，执行时才能拿到油站列表
                setTimeout(() => {
                  this.findStation(this.oilStationList, order.stationCode);
                }, 1000);
              } else {
                this.showPreInfo = false;
              }
            }
          }
        } else {
          // todo 解决切换用户导致首页预授权订单展示问题
          this.showPreInfo = false;
          /**
           * 预订单查询请求失败
           * 当preOrderInfo为空时候  表示是进入预加载页面的请求,不需要轮循, 如果preOrderInfo不为空 表示预下单 需要轮循
           */
          console.log("PreAuthOrderQuery preOrderInfo:" + JSON.stringify(this.preOrderInfo) != "{}");
          if (JSON.stringify(this.preOrderInfo) != "{}") {
            this.isPollingFun();
          }
        }
      });
    },
    /**
     * @description: 轮询是否存在预授权下单订单
     * @return {*}
     */
    isPollingFun() {
      this.clearTimer();
      this.timer = setTimeout(() => {
        this.PreAuthOrderQuery(true, this.fishPreOrderInfo.preAuthOrderNo);
      }, 4000);
    },

    /**
     * @description: 根据油站编码在list中查询当前油站的具体信息
     * @param {*} list
     * @param {*} code
     * @return {*}
     */
    findStation(list, code) {
      //查询某个具体加油站的具体信息 list加油站列表  code油站编码  根据油站编码在list中查询当前油站的具体信息
      list.some((item, index, arr) => {
        if (item.oilStationCode === code) {
          this.preOrderStationInfo = item;
        }
        return item.oilStationCode === code;
      });
    },

    /**
     * @description: 清除轮询定时器
     * @return {*}
     */

    clearTimer() {
      clearTimeout(this.timer);
      this.timer = null;
    },

    /**
     * @description: 加油卡信息
     * @return {*}
     */

    GetUserCardInfo() {
      return GetUserCardInfo({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
      }).then((res) => {
        if (res.infoCode == 1) {
          this.cardInfo = res.data;
          this.carNo_fn(res.data.carno);
        }
      });
    },
    /**
     * @description: 根据地域获取参数addoiltype，判断使用不下车加油或预授权加油
     * @return {*}
     */

    getFuelTypeByProvince() {
      const params = {
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        provinceCode: "",
        provinceName: storage.ss.get("deviceInfo")?.province.replace(/省|市/g, "") ?? "北京",
      };
      this.$http("POST", `/appcenter/trade/v1/getFuelTypeByProvince`, params, true, true, true, false, false)
        .then((res) => {
          if (res?.infoCode == 1 && res?.data?.data) {
            this.addoiltype = res?.data?.data[0]?.oilType;
            // this.addoiltype = 2;
          }
        })
        .catch((err) => {});
    },

    /**
     * @description: 订单子组件回调方法
     * @return {*}
     */
    oilCodeCallback(res) {
      if (res.infoCode == 1) {
        this.showPreInfo = false;
        this.amountMoeny = "";
        this.GetUserCardInfo();
        this.clearTimer();
        this.$toast("预订单已取消!");
      } else if (res.infoCode == 5) {
        this.showPreInfo = false;
        this.$Dialog.alert({
          title: "温馨提示",
          message: "已支付完成",
        });
      } else {
        this.$Dialog.alert({ title: "温馨提示", message: res.info });
      }
    },

    /**
     * @description: 重新登录
     * @return {*}
     */

    reloadLogin() {
      this.$cppeiBridge("clearToken", {});
      storage.ss.remove("apptoken");
      storage.ss.clear();
      this.$router.replace({
        path: "/loginCenter",
      });
    },
  },
};
</script>

<style lang="scss" scopeed>
.container {
  position: relative !important;
  width: 100vw;
  height: 100vh;
  .map {
    width: 100vw;
    height: 100vh;
  }
  .choose_license {
    width: 100vw;
    position: absolute;
    bottom: 100px;
    height: auto;
  }
  .gnc {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 90px !important;
    margin: auto;
    bottom: 0;
    height: 60vh;
    overflow: scroll;
    border: 1px solid #cdc8c8;
  }
}
.popupoilpre {
  z-index: 0 !important;
  height: 120vw;
  background: transparent;
}
.popupoilpay {
  z-index: 0 !important;
  height: 120vw;
}
.popupoilpay {
  background: #f5f5f5;
  height: 112vw;
  .staion {
    background: #fff;
    margin: 0 3vw 3vw;
    padding: 4vw;
    border-radius: 4vw;
    text-align: left;
    position: relative;
    .staioninfo {
      background: #fff;
      display: flex;
      align-items: center;
      margin-bottom: 1vw;
      b {
        font-size: 1.3em;
        margin-right: 2vw;
        max-width: 50vw;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .distance {
      display: block;
      margin-top: 1vw;
    }
    .carno {
      margin-top: 6vw;
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        font-size: 0.6em;
        background: url("~@/assets/images/gasstationmap/addcar.png") no-repeat left -1px;
        padding-left: 5vw;
        background-size: contain;
      }
    }
    .baidumaping {
      width: 20vw;
      height: 20vw;
      background: url("~@/assets/images/gasstationmap/navigation.png") no-repeat;
      background-size: contain;
      position: absolute;
      top: 3vw;
      right: 1vw;
    }
  }
  .staionorder {
    padding: 4vw 3vw 3vw;
    border-radius: 4vw;
    margin: 0 3vw;
    background: #fff;
    > div {
      text-align: left;
    }
    /deep/ .van-grid {
      margin: 2vw 0;
      .van-grid-item {
        &__content {
          padding: 8px;
          background: transparent url("~@/assets/images/gasstationmap/youpinB.png");
          background-size: 100% 100%;
        }
        &.activedStaion {
          .van-grid-item__content {
            // background: url("~@/assets/images/gasstationmap/youpinA.png");
            background: url("~@/assets/images/winter/btn.png");
            background-size: 100% 100%;
          }
        }
      }
      .other {
        font-size: 12px;
      }
    }
    .gun {
      height: 31vw;
      margin-bottom: 2vw;
      overflow-y: auto;
      > div {
        span {
          float: right;
          color: #989898;
          font-size: 0.8em;
        }
      }
      /deep/ .van-field__control {
        border: 1px solid #ff9200;
        padding-left: 10px;
        border-radius: 1vw;
      }
    }
    .van-button--large {
      height: 40px;
      background-color: #ff9200;
      border: 1px solid #ff9200;
    }
  }
}
</style>