<template>
  <div class="login-code">
    <!-- 顶部图片 -->
    <section class="header">
      <div class="img"></div>
    </section>

    <!-- 登录页 -->
    <section class="login-card">
      <van-form validate-first @submit="onSubmit" label-width="70" :show-error-message="false" :show-error="false">
        <van-field type="tel" v-model="user.phone" label="手机号" clearable placeholder="请输入" maxlength="11" center
          :rules="[{ validator: validatePhone, trigger: 'onBlur' }]">
          <template slot="left-icon">
            <img src="@/assets/images/winter/login_phone.png" class="icon" />
          </template>
        </van-field>

        <van-field v-model="user.captcha" label="图片验证码" clearable placeholder="请输入" center
          :rules="[{ validator: validateCaptcha, trigger: 'onBlur' }]">
          <template slot="left-icon">
            <img src="@/assets/images/winter/login_yanzhengma.png" class="icon" />
          </template>
          <template #button>
            <!-- 前端验证码 -->
            <div @click="changeCode">
              <v-sidentify :identifyCode="picCode"></v-sidentify>
            </div>
          </template>
        </van-field>

        <!-- 验证码登录 -->
        <van-field v-if="isLoginMode" class="mode-height smsCodeBtn" v-model="user.smsCode" label="验证码"
          icon-prefix="my-icon" maxlength="4" clearable placeholder="请输入" center
          :rules="[{ validator: validateSms, trigger: 'onBlur' }]">
          <template slot="left-icon">
            <!-- <img src="@/assets/images/icon/mima.png" class="icon" /> -->
            <img src="@/assets/images/winter/login_password.png" class="icon" />
          </template>
          <template slot="button">
            <van-button style="width:80px" size="small" :color="$setting.themeColor" :loading="smsBtnLoading"
              type="warning" plain :disabled="!showSend" @click.prevent="checkAndSend">
              <!-- 第一次发送显示: 获取验证码 -->
              <div v-show="showSend" v-if="0 === clickNum">发送验证码</div>
              <!-- 以后都显示: 再次发送 -->
              <div v-show="showSend" v-else>再次发送</div>
              <!-- 倒计时60秒 -->
              <span v-show="!showSend">{{ count }}&nbsp;s</span>
            </van-button>
          </template>
        </van-field>

        <!-- 密码登录 -->
        <van-field v-else class="mode-height" v-model="user.password" label="登录密码" icon-prefix="my-icon" clearable
          placeholder="请输入" type="password" center :rules="[{ validator: validatePwd, trigger: 'onBlur' }]">
          <template slot="left-icon">
            <!-- <img src="@/assets/images/icon/mima.png" class="icon" /> -->
            <img src="@/assets/images/winter/login_password.png" class="icon" />
          </template>
          <template slot="extra">
            <div class="forgot-pwd" @click="switch2ForgetPswd">忘记密码</div>
          </template>
        </van-field>

        <!-- 错误提示 -->
        <div class="err-tips">{{ errMsg }}</div>

        <!-- 登录按钮 -->
        <div class="btn-wrap">
          <van-button block round :color="$setting.themeColor" :loading="btnLoading" loading-text="登录中..." size="large"
            :disabled="btnDisabled" native-type="submit">登&nbsp;录</van-button>
        </div>
      </van-form>
    </section>

    <!-- 底部其他操作 切换登录方式 协议 -->
    <div class="switch-btn">
      <span @click="switch2PasswordLogin">{{ isLoginMode ? "账号密码登录" : "短信验证码登录" }}</span>
    </div>
    <div class="loginfooter" v-show="showFooter">
      <van-checkbox class="check-box" label-disabled :checked-color="$setting.themeColor" v-model="checked"
        shape="square">我已阅读并同意</van-checkbox>
      <span class="text" @click="switch2PrivacyPolicy">《隐私政策》</span>及
      <span class="text" @click="switch2UserAgreement">《用户协议》</span>
    </div>
  </div>
</template>

<script>
import { Toast } from "vant";
import { mapState, mapMutations } from "vuex";
import { storage, generateUUID, isAndroid, getUserAgent, compareVersions } from "Utils/common/";
import Sidentify from "@/components/code/drawCodePic.vue"; // 画验证码组件
// 后端接口
import { sendCode, loginBySms, getOpenCardList, getCaptchaText, isPhoneExist, loginByAccount, isChangeEquipment, loginRiskManagement } from "./_service";
import { recordJsLog } from "@/utils/recordJsLog";

//更改倒计时时间
const TIME_COUNT = 59;
import md5 from "js-md5";

export default {
  props: {},
  components: {
    "v-sidentify": Sidentify,
  },
  data() {
    return {
      permissions: false, // 临时保存手机相关权限是否均已授权
      showFooter: true,
      // 表单数据
      user: {
        phone: "",
        // 短信验证码
        smsCode: "",
        // 图形验证码
        captcha: "",
        // 密码
        password: "",
        // 1 单位 2司机
        type: this.$setting.APP_TYPE,
      },
      // 图形验证码,传到画图的子组件,首次加载会被覆盖掉
      picCode: "",
      showSend: true,
      // 点击发送验证码次数
      clickNum: 0,
      // 定时器
      timer: null,
      // 初始化次数
      count: "",
      // 默认选中
      checked: false,
      // 错误提示
      errMsg: "",
      // 图片验证码路径前缀
      prefix: process.env.VUE_APP_AXIOS_URL,
      // 后端生成图片验证码接口
      imgUrl: "",
      uuid2: "",
      // 按钮loading组织穿透
      btnLoading: false,
      smsBtnLoading: false,
      // 登录方式 true=短信验证码  false=密码登录
      loginMode: true,
      // 验证手机是否被邀请
      requestParams: {}, // 登录请求相应参数对象
      // app监测相关
      hiddenProperty: null,
      visibilityChangeEvent: null,
      currentasnResult: false, // 判断当前是否激活
      getLocationFlag: true, // uat原生回调2次导致验证码失效
      payRiskState: null, // 支付风控状态
    };
  },
  computed: {
    ...mapState(["isLoginMode"]),
    btnDisabled() {
      if (this.isLoginMode) {
        return this.user.phone && this.user.captcha && this.user.smsCode && !this.checked;
      } else {
        return this.user.phone && this.user.captcha && this.user.password && !this.checked;
      }
    },
  },
  created() {
    // this.changeImg();
    // 将默认的验证码覆盖掉
    // 延时解决初次加载不出来问题
    setTimeout(() => {
      this.changeCode();
    }, 500);

    // 隐私权限页面 点击同意后到登录页在获取设备ID
    recordJsLog('getDeviceId')
    this.$cppeiBridge("getDeviceId", {});

    // 当跳到登录页时更新迁移状态为0
    recordJsLog('saveReapplyStatus', { reapplyStatus: 0 })
    this.$cppeiBridge("saveReapplyStatus", { reapplyStatus: 0 });

    // 解决退出登录清除缓存后，不退出应用或杀进程继续登录获取必要参数
    this.getUserAgentInfo();
  },
  mounted() {
    window.onRegister = this.onRegister;
    window.onHasCards2Reapply = this.onHasCards2Reapply; //迁移
    window.onGetLocation = this.onGetLocation;
    window.onGetDeviceId = this.onGetDeviceId;
    window.onDeviceFinger = this.onDeviceFinger;
  },
  methods: {
    ...mapMutations(["apptoken_fn", "isQianYi_fn", "isLoginMode_Fn", "currentPhone_Fn"]),

    /**
     * @description: 获取当前定位信息
     * @return {*}
     */

    onGetLocation(params) {
      recordJsLog('onGetLocation', params);
      console.log("login onGetLocation:" + JSON.stringify(params));

      // 解决 H5 挂载 Windows 上相同方法无法区分问题，引入路由判断
      if (params.route === 'loginCenter') {
        // error不为空时，H5 展示 error 内容，为空时若 city 为空，保持原逻辑错误提示
        if (!params.error) { // 判断字符串为空
          setTimeout(() => {
            if (storage.ss.get("deviceInfo")) {
              storage.ss.set("deviceInfo", { ...storage.ss.get("deviceInfo"), ...params });
            } else {
              storage.ss.set("deviceInfo", { ...params });
            }
            this.getLocationMethods()
            // this.getLocationFlag ? this.getLocationMethods() : '' ;
          }, 500);

        } else {
          this.$Dialog
            .alert({
              title: "提示",
              message: params.error
            })
            .then(() => {
              this.btnLoading = false;
            });
        }
      }
    },

    /**
     * @description: 原生端SDK注册回调(userId会用于APP激活SDK，需监听回调onRegister)
     * @param {*} res
     * @return {*}
     */

    onRegister(res) {
      recordJsLog('onRegister', res);

      console.log("login onRegister", JSON.stringify(res));
      if (res.error == 0) {
        // 调用原生迁移判断
        recordJsLog('hasCards2Reapply')
        this.$cppeiBridge("hasCards2Reapply", {});
      } else {
        // this.$dialog
        //   .alert({
        //     title: "SDK注册失败,请多次尝试，如果多次尝试失败请联系相关管理人员",
        //     message: res.error + "-" + res.message,
        //   })
        //   .then(() => {
        //     this.reloadLogin();
        //   });
        this.$dialog
          .alert({
            title: "温馨提示",
            message: `<h4 style="margin:-5px 0;text-align:left">SDK注册失败,请多次尝试，如果多次尝试失败请联系相关管理人员</h4>` + `<p>` + res.error + "-" + res.message + `</p>`,
          })
          .then(() => {
            this.reloadLogin();
          });
      }
    },

    /**
     * @description: onHasCards2Reapply 返回值为true 表示需要迁移
     * @param {*} obj
     * @return {*}
     */

    onHasCards2Reapply(obj) {
      recordJsLog('onHasCards2Reapply', obj);

      console.log("login onHasCards2Reapply:" + JSON.stringify(obj));
      // error成功为0, 20000001-注册已失效需重新登录
      // error为0时原生端的register和判断是否迁移都没问题，这时直接通过result判断是否需要迁移。error不为0时通过message给出提示
      if (obj.error === 0) {
        if (obj.result === true) {
          this.isQianYi_fn(1);
          // 调用迁移判断需要迁移时更新状态为1
          recordJsLog('saveReapplyStatus', { reapplyStatus: 1 })
          this.$cppeiBridge("saveReapplyStatus", { reapplyStatus: 1 });
          this.loginCallback(this.requestParams);
          console.log('login 设置迁移状态isQianYi：1');

        } else {
          //  无错误，且待迁移判断是false，说明不需要迁移
          this.isQianYi_fn(2);
          recordJsLog('saveReapplyStatus', { reapplyStatus: 2 })
          this.$cppeiBridge("saveReapplyStatus", { reapplyStatus: 2 });
          this.loginCallback(this.requestParams);
          console.log('login 设置迁移状态isQianYi：2');

        }
      } else if (obj.error == "20000001") {
        // this.$Dialog
        //   .alert({
        //     title: "提示",
        //     message: obj.error + "-" + "注册已失效需重新登录",
        //   })
        //   .then(() => {
        //     this.reloadLogin();
        //   });

        // 清除本地的token
        this.reloadLogin(true)
        this.getLocationMethods()

      } else if (obj.error == "-11") {
        if (this.currentasnResult) {
          this.$Dialog.alert({
            title: "提示",
            message: obj.error + "-" + obj.message,
          }).then(() => {
            this.reloadLogin();
          });
        } else {
          //  无错误，且待迁移判断是false，说明不需要迁移
          this.isQianYi_fn(2);
          recordJsLog('saveReapplyStatus', { reapplyStatus: 2 })
          this.$cppeiBridge("saveReapplyStatus", { reapplyStatus: 2 });
          this.loginCallback(this.requestParams);
          console.log('login 设置迁移状态isQianYi：2');

        }
      } else {
        this.$Dialog.alert({
          title: "提示",
          message: obj.error + "-" + obj.message,
        }).then(() => {
          this.reloadLogin();
        });
      }
    },
    /**
     * 设备指纹识别回调
     *
     * @param obj - 回调对象，包含错误信息和设备信息
     */
    onDeviceFinger(obj) {
      recordJsLog("onDeviceFinger", obj);
      console.log("login----onDeviceFinger", obj);

      if (obj.error === "") {
        if (this.payRiskState === 2) {
          this.riskManagement({ status: "", deviceId: obj.message }, (res) => {
            console.log("riskManagement:" + JSON.stringify(res));
            if (res.value) {
              // 后台接口登录
              this.isLoginMode ? this.smsCodeLogin() : this.passwordLogin();
            }
          });
        } else if (this.payRiskState === 0) {
          this.riskManagement({ status: "0", deviceId: obj.message });
        } else if (this.payRiskState === 1) {
          this.riskManagement({ status: "1", deviceId: obj.message });
        } else {
          this.$dialog
            .alert({
              title: "提示",
              message: obj.error + "-" + obj.message,
            })
            .then(() => { });
        }
      } else {
        this.$dialog
          .alert({
            title: "提示",
            message: obj.error + "-" + obj.message,
          })
          .then(() => { });
      }
    },
    /**
     * @description: 原生SDK设置默认卡回调
     * @param {*} res
     * @return {*}
     */

    onSetDefaultCard(res) {
      recordJsLog('onSetDefaultCard', res);

      if (res.error === 0) {
      } else {
        this.$Dialog
          .alert({
            title: "设置默认卡失败",
            message: res.error + "-" + res.message,
          })
          .then(() => { });
      }
    },

    /**
     * @description: 获取设备唯一编码回调
     * @return {*}
     */

    onGetDeviceId(params) {
      recordJsLog('onGetDeviceId', params);

      console.log("login onGetDeviceId:" + JSON.stringify(params));
      storage.ss.set("deviceInfo", { ...storage.ss.get("deviceInfo"), ...params });
      console.log("login onGetDeviceId deviceInfo: " + JSON.stringify(storage.ss.get("deviceInfo")));
    },

    /**
     * @description: 通过UserAgent获取设备信息缓存
     * @return {*}
     */
    getUserAgentInfo() {
      let uaStr = getUserAgent();
      console.log("log 通过UserAgent获取设备信息：" + JSON.stringify(uaStr));
      if (storage.ss.get("deviceInfo")) {
        storage.ss.set("deviceInfo", { ...storage.ss.get("deviceInfo"), ...uaStr });
      } else {
        storage.ss.set("deviceInfo", { ...uaStr });
      }
      // 在有导航的地方设置安全区域 故缓存 然后在有导航的地方设置
      storage.ss.set("paddingBottom", uaStr.bottomHeight + "px");
      storage.ss.set("statusHeight", uaStr.statusHeight);
      storage.ss.set("routerViewHit", document.body.clientHeight - uaStr.bottomHeight + "px");
    },

    /**
     * @description: 校验&发短信
     * @return {*}
     */

    checkAndSend() {
      // 校验手机号
      var isPhone = this.$validate.isPhone(this.user.phone);
      if (!isPhone) {
        this.errMsg = "* 请输入正确的手机号码";
        return;
      }
      if (this.user.captcha === "") {
        this.errMsg = "* 请填写图片验证码";
        return;
      }
      this.errMsg = "";
      // 发送验证码
      this.checkAndSendCode();
    },

    /**
     * @description: 校验后发送验证码
     * @return {*}
     */

    checkAndSendCode() {
      // 验证是否有权限
      isPhoneExist({
        phone: this.user.phone,
        type: this.user.type,
      })
        .then((res) => {
          if (1001 === res.infoCode) {
            // 跳到未授权提示页面
            this.$router.push({
              path: "/noAuth",
            });
          } else {
            // 发送短信验证码
            this.sendCodeByPhone();
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 发送短信验证码
     * @return {*}
     */

    sendCodeByPhone() {
      this.smsBtnLoading = true;
      sendCode({
        phone: this.user.phone,
        captcha: this.user.captcha,
        type: this.user.type,
        deviceName: this.deviceName,
        uuid: this.uuid2,
      })
        .then((res) => {
          if (!res.value) {
            setTimeout(() => {
              this.changeCode();
            }, 500);
            Toast(res.info);
          } else {
            // 倒计时60秒
            this.countdown();
            // 必须延时关闭弹窗  ${phone.substr(0, 3)}****${phone.substr(7, 11)}
            setTimeout(() => {
              Toast(`验证码已发送至: ${this.user.phone.substring(0, 3)}****${this.user.phone.substring(7, 11)}`);
            }, 300);
          }
          this.smsBtnLoading = false;
        })
        .catch((err) => {
          setTimeout(() => {
            this.changeCode();
          }, 500);
          console.log(err);
          this.smsBtnLoading = false;
        });
    },

    /**
     * @description: 表单失焦判断
     * @return {*}
     */
    validatePhone(val) {
      if (!val) {
        this.errMsg = "*手机号不能为空";
        return false;
      } else {
        // let reg = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
        var isPhone = this.$validate.isPhone(val);
        if (isPhone) {
          this.errMsg = "";
          return this.isPhoneExist();
        } else {
          this.errMsg = "*手机号格式不正确";
          return false;
        }
      }
    },
    validateCaptcha(val) {
      if (!val) {
        this.errMsg = "*图片验证码不能为空";
        return false;
      } else {
        this.errMsg = "";
        return true;
      }
    },
    validatePwd(val) {
      if (!val) {
        this.errMsg = "*密码不能为空";
        return false;
      } else {
        this.errMsg = "";
        return true;
      }
    },

    validateSms(val) {
      if (!val) {
        this.errMsg = "*短信验证码不能为空";
        return false;
      } else {
        if (val.length != 4) {
          this.errMsg = "*短信验证码错误";
          return false;
        } else {
          this.errMsg = "";
          return true;
        }
      }
    },

    /**
     * @description: 点击登录触发
     * @return {*}
     */

    onSubmit() {
      this.requestParams = {};
      if (!this.checked) {
        Toast("请勾选同意《隐私政策》及《用户协议》！");
        return;
      }
      this.btnLoading = true;
      recordJsLog('getLocation', { route: 'loginCenter' })
      this.$cppeiBridge("getLocation", { route: 'loginCenter' });
      console.log('login --- 触发原生getLocation定位获取');
    },

    /**
     * @description: 11月版本上架应用商店定位问题
     * @return {*}
     */

    getLocationMethods() {
      // this.getLocationFlag = false
      // 风控接口调用
      if (compareVersions(storage.ss.get("deviceInfo").version || '', '1.1.2') >= 0) {
        this.payRiskState = 2;
        recordJsLog('deviceFinger', {});
        this.$cppeiBridge("deviceFinger", {});
        console.log(this.payRiskState, 'this.payRiskState');
      } else {
        this.riskManagement({ status: "" }, (res) => {
          console.log("riskManagement:" + JSON.stringify(res));
          if (res.value) {
            // 后台接口登录
            this.isLoginMode ? this.smsCodeLogin() : this.passwordLogin();
          }
        });
      }
    },

    // doLogin() {
    //   this.$refs.form.onSubmit();
    // },

    /**
     * @description: 登录风控，初次登录不传参，
     * @return {*}
     */

    // 登录风控
    riskManagement(params, callback = false) {
      // let res = {
      //   value: true,
      // };
      // if (callback) {
      //   callback(res);
      // }
      // return;
      const deviceInfo = storage.ss.get("deviceInfo");
      let { status, deviceId = '' } = params;
      loginRiskManagement({
        appid: 2,
        phone: this.user.phone,
        ip: "",
        status,
        gps: "gps",
        address: deviceInfo?.province + ";" + deviceInfo?.city + ";" + deviceInfo?.district,
        // address: "四川省;成都;青羊区",
        latitude: deviceInfo?.lat ?? "39.920248",
        longitude: deviceInfo?.lon ?? "116.407718",
        deviceId,
      })
        .then((res) => {
          if (callback) {
            callback(res);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 短信验证码登录
     * @return {*}
     */
    smsCodeLogin() {
      loginBySms({
        phone: this.user.phone,
        smsCode: this.user.smsCode,
        type: this.user.type,
        deviceName: storage.ss.get("deviceInfo")?.deviceId ? storage.ss.get("deviceInfo")?.deviceId : "1",
        gps: storage.ss.get("deviceInfo")?.lon + "-" + storage.ss.get("deviceInfo")?.lat,
        city: storage.ss.get("deviceInfo")?.city ? storage.ss.get("deviceInfo")?.city : "北京",
        uuid: this.uuid2,
        captcha: this.user.captcha,
      })
        .then((res) => {
          if (!res.value) {
            if (compareVersions(storage.ss.get("deviceInfo").version || '', '1.1.2') >= 0) {
              this.payRiskState = 1;
              recordJsLog('deviceFinger', {});
              this.$cppeiBridge("deviceFinger", {});
              console.log(this.payRiskState, 'this.payRiskState');
            } else {
              this.riskManagement({ status: "1" });
            }
            setTimeout(() => {
              this.changeCode();
            }, 500);
            this.btnLoading = false;
            // 登录失败提示
            Toast(res.info);
          } else {
            if (compareVersions(storage.ss.get("deviceInfo").version || '', '1.1.2') >= 0) {
              this.payRiskState = 0;
              recordJsLog('deviceFinger', {});
              this.$cppeiBridge("deviceFinger", {});
              console.log(this.payRiskState, 'this.payRiskState');
            } else {
              this.riskManagement({ status: "0" });
            }

            // 登录成功后将token存在storage中
            storage.ss.set("apptoken", res.data.apptoken);
            storage.ss.set("userId", res.data.id);
            storage.ss.set("defaultCompanyCode", res.data.defaultCompanyCode);
            storage.ss.set("phone", res.data.phone);
            storage.ss.set("cardNo", res.data.cardNo);
            storage.ss.set("idCard", res.data.idCard);
            this.apptoken_fn(res.data.apptoken);
            this.currentPhone_Fn(res.data.phone);

            // 将响应参数保存，确保注册，迁移判断流程走完在做业务处理
            this.requestParams = { ...res.data };

            // 调用原生保存
            // this.$cppeiBridge("saveInfo", {
            //   token: res.data.apptoken,
            //   userId: res.data.id,
            // });

            /**
             * 二要素导致问题，原来是在登录后统一注册SDK，现在需要增加一种情况，首次激活的时候点激活更新默认企业后再调注册SDK，
             * 目的就是为了拿到准确的userId（二要素的情况下只有实人认证后才能确定），避免变更手机号等情况导致的userId不同引起未进行卡数据合并等问题
             */
            // 第一次登录，需要设置登录密码
            if (res.data.existPassword == "0") {
              this.btnLoading = false;
              // 不是忘记密码, 设置密码后跳到实名认证
              storage.ss.set("isForget", false);
              this.$router.push({ path: "/setPswd" });

            } else {
              if (res.data.defaultCompanyCode === null || res.data.defaultCompanyCode === "") {
                this.btnLoading = false;
                // 跳到实名认证
                this.$router.replace({
                  path: "/identityCardOCR",
                });
              } else {
                // 需判断已开通,未激活saveinfo不注册，已激活的在注册
                this.getOpenCardMethods({ phone: this.user.phone, type: this.user.type });
              }
            }
          }
        })
        .catch((err) => {
          this.changeCode();
          console.log(err);
          this.btnLoading = false;
        });
    },

    /**
     * @description: 密码登陆
     * @return {*}
     */

    passwordLogin() {
      this.errMsg = "";
      // 后台接口登录
      loginByAccount({
        captcha: this.user.captcha,
        deviceName: storage.ss.get("deviceInfo")?.deviceId ? storage.ss.get("deviceInfo")?.deviceId : "1",
        city: storage.ss.get("deviceInfo")?.city ? storage.ss.get("deviceInfo")?.city : "北京",
        gps: storage.ss.get("deviceInfo")?.lon + "-" + storage.ss.get("deviceInfo")?.lat,
        password: md5(this.user.password),
        phone: this.user.phone,
        type: this.user.type,
        uuid: this.uuid2,
      })
        .then((res) => {
          if (res.infoCode === 1) {
            if (compareVersions(storage.ss.get("deviceInfo").version || '', '1.1.2') >= 0) {
              this.payRiskState = 0;
              recordJsLog('deviceFinger', {});
              this.$cppeiBridge("deviceFinger", {});
              console.log(this.payRiskState, 'this.payRiskState');
            } else {
              this.riskManagement({ status: "0" });
            }
            Toast("登录成功");
            // 登录成功后将token存在storage中
            storage.ss.set("apptoken", res.data.apptoken);
            storage.ss.set("userId", res.data.id);
            storage.ss.set("defaultCompanyCode", res.data.defaultCompanyCode);
            storage.ss.set("phone", res.data.phone);
            storage.ss.set("cardNo", res.data.cardNo);
            storage.ss.set("idCard", res.data.idCard);
            this.apptoken_fn(res.data.apptoken);
            this.currentPhone_Fn(res.data.phone);
            this.requestParams = { ...res.data };

            //  登录之后查询绑定设备关系
            this.isChangeEquipment(res.data);
          } else {
            if (compareVersions(storage.ss.get("deviceInfo").version || '', '1.1.2') >= 0) {
              this.payRiskState = 1;
              recordJsLog('deviceFinger', {});
              this.$cppeiBridge("deviceFinger", {});
              console.log(this.payRiskState, 'this.payRiskState');
            } else {
              this.riskManagement({ status: "1" });
            }
            setTimeout(() => {
              this.changeCode();
            }, 500);
            Toast(res.info);
            this.btnLoading = false;
          }
        })
        .catch((err) => {
          this.changeCode();
          console.log(err);
          this.btnLoading = false;
        });
    },

    /**
     * @description: 注册迁移回调完成后进行登录成功后的业务处理
     * @return {*}
     */

    loginCallback(obj) {
      this.btnLoading = false;

      // 第一次登录，需要设置登录密码
      if (obj.existPassword == "0") {
        // 不是忘记密码, 设置密码后跳到实名认证
        storage.ss.set("isForget", false);
        this.$router.push({ path: "/setPswd" });
      } else {
        if (obj.defaultCompanyCode === null || obj.defaultCompanyCode === "") {
          // 跳到实名认证
          this.$router.replace({
            path: "/identityCardOCR",
          });
        } else {
          // 已开通,未激活
          this.getOpenCard({ phone: this.user.phone, type: this.user.type });
        }
      }
    },

    /**
     * @description: 验证手机号是否有权限
     * @return {*}
     */

    isPhoneExist() {
      // 验证是否有权限
      isPhoneExist({
        phone: this.user.phone,
        type: this.user.type,
      })
        .then((res) => {
          // 判断是否都激活
          console.log('login isPhoneExist res.data:' + JSON.stringify(res.data));
          this.errMsg = "";
          if (res.infoCode == 1) {
            // 判断是否都激活
            this.currentasnResult = res.data.data.every(item => item.currentasn != null);
            console.log('login isPhoneExist currentasnResult:' + this.currentasnResult);
            // 检测关联企业是否已迁移
            if (Array.isArray(res.data.data) && res.data.data.every(item => item.migrateFlag === 1)) {
              // 弹窗提示
              this.$dialog.alert({
                message: '您当前使用企业已全部迁移，请下载并登录中油暂行App，以继续使用相关服务。',
                confirmButtonText: '确定'
              }).then(() => {
                // 清除登录态等后续操作
              });
              return;
            }
          }
        })
        .catch((err) => {
          this.errMsg = "*" + err;
        });
    },

    /**
     * @description: 登录之后查询绑定设备关系
     * @param {*} data
     * @return {*}
     */

    isChangeEquipment(data) {
      isChangeEquipment({
        apptoken: data.apptoken,
        equipmentNo: this.user.phone == this.$setting.AuditPhone ? this.$setting.AuditDeviceId : storage.ss.get("deviceInfo")?.deviceId ? storage.ss.get("deviceInfo")?.deviceId : "1",
        phone: data.phone,
        type: this.user.type,
      })
        .then((res) => {
          if (res && res.infoCode == 1) {
            if (this.$isBackData(res.data)) {
              if (!res.data.isChange) {
                this.$router.push({ name: "safeNotice" });
              }
            } else {
              // 调用原生保存
              // this.$cppeiBridge("saveInfo", {
              //   token: data.apptoken,
              //   userId: data.id,
              // });
              // 第一次登录，需要设置登录密码
              if (data.existPassword == "0") {
                this.btnLoading = false;
                // 不是忘记密码, 设置密码后跳到实名认证
                storage.ss.set("isForget", false);
                this.$router.push({ path: "/setPswd" });

              } else {
                if (data.defaultCompanyCode === null || data.defaultCompanyCode === "") {
                  this.btnLoading = false;
                  // 跳到实名认证
                  this.$router.replace({
                    path: "/identityCardOCR",
                  });
                } else {
                  // 需判断已开通,未激活saveinfo不注册，已激活的在注册
                  this.getOpenCardMethods({ phone: this.user.phone, type: this.user.type });
                }
              }
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 调用接口判断激活流程
     * @param {*} params
     * @return {*}
     */

    getOpenCardMethods(params) {
      console.log("getOpenCardMethods=====params", params);
      getOpenCardList(params)
        .then((res) => {
          if (res.infoCode === 1) {
            const defIsOpen = res.data.some((item) => item.companyCode == storage.ss.get("defaultCompanyCode"));
            if (defIsOpen) {
              this.$router.replace({
                path: "/chooseEnterprise",
                query: {
                  isActive: 2,
                },
              });

            } else {
              recordJsLog('saveInfo', {
                token: storage.ss.get('apptoken'),
                userId: storage.ss.get('userId')
              })
              // 跳到首页的企业需走全部流程
              this.$cppeiBridge("saveInfo", {
                token: storage.ss.get('apptoken'),
                userId: storage.ss.get('userId')
              });
            }
          } else {
            Toast(res.info);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 获取已开通未激活的卡片
     * @param {*} params
     * @return {*}
     */

    getOpenCard(params) {
      getOpenCardList(params)
        .then((res) => {
          if (res.infoCode === 1) {
            const defIsOpen = res.data.some((item) => item.companyCode == storage.ss.get("defaultCompanyCode"));
            if (defIsOpen) {
              this.$router.replace({
                path: "/chooseEnterprise",
                query: {
                  isActive: 2,
                },
              });
            } else {
              // 跳到首页
              this.$router.replace({ path: "/gasstationmap" });
            }
          } else {
            Toast(res.info);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 倒计时60秒
     * @return {*}
     */

    countdown() {
      this.clickNum++;
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.showSend = false;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.showSend = true;
            clearInterval(this.timer); // 清除定时器
            this.timer = null;
          }
        }, 1000);
      }
    },

    /**
     * @description: 切换图片验证码
     * @return {*}
     */

    changeImg() {
      this.imgUrl = this.prefix + "/appUser/v1/getCaptcha?uuid=" + `${generateUUID()}`;
    },

    /**
     * @description: 刷新验证码
     * @return {*}
     */

    changeCode() {
      var uuid = `${generateUUID()}`;
      this.uuid2 = uuid;
      // 调用后台接口
      getCaptchaText({
        uuid: uuid
      })
        .then((res) => {
          this.picCode = res.data;
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 切换到密码登录
     * @return {*}
     */

    switch2PasswordLogin() {
      this.errMsg = "";
      this.user.smsCode = "";
      this.user.password = "";
      this.loginMode = !this.loginMode;
      this.isLoginMode_Fn(this.loginMode);
    },

    /**
     * @description: 忘记密码
     * @return {*}
     */

    switch2ForgetPswd() {
      this.$router.push({
        path: "/forgetPswd",
      });
    },

    /**
     * @description: 协议文本页
     * @return {*}
     */

    switch2PrivacyPolicy() {
      this.$router.push({
        path: "/privacyPolicy",
      });
    },
    switch2UserAgreement() {
      this.$router.push({
        path: "/userAgreement",
      });
    },

    /**
     * @description: 重新登录
     * @return {*}
     */

    reloadLogin(bool = false) {
      if (!bool) {
        this.btnLoading = false;
      }
      // this.getLocationFlag = true;
      this.$cppeiBridge("clearToken", {});
      storage.ss.remove("apptoken");
      storage.ss.clear();
      // this.$router.replace({
      //   path: "/loginCenter",
      // });
    },
  },
};
</script>

<style scoped lang="scss">
$themeColor: #ff9200;

.mode-height {
  min-height: 14vw;
}

.login-code {
  height: 100vh;
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;

  .header {
    height: 56vw;
    width: 100vw;

    .img {
      height: 100%;
      width: 100%;
      // 背景图片
      background-image: url(~Images/winter/login_bj.png);
      background-size: 100% 100%;

      .title {
        font-size: 45px;
        color: #fff;
        line-height: 335px;
        padding-left: 45px;
        background: url(~Images/login/logo_3.png) no-repeat;
        background-size: 45px 45px;
      }
    }

    margin-bottom: 50px;
  }

  .login-card {
    background: #fff;
    margin: 0 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding-top: 10px;

    .icon {
      width: 27.5px;
      height: 29.5px;
      vertical-align: sub;
    }

    .err-tips {
      color: #ee0a24;
      line-height: 3;
      // padding: 30px 30px 0;
      padding-left: 40px;
      text-align: left;
      height: 15px;
      // line-height: 20px;
      font-size: 24px;
    }

    .btn-wrap {
      padding: 0 20px;
      position: relative;
      bottom: -48px;

      .van-button--disabled {
        // 按钮透明度
        opacity: 1;
      }
    }

    /deep/ .van-cell {
      background: none;

      #pic-code {
        width: 100px;
        height: 32.5px;
      }
    }
  }

  .switch-btn {
    text-align: center;
    color: $themeColor;
    cursor: pointer;
    margin-right: 456px;
    margin-top: 64px;
    line-height: 40px;
  }

  .loginfooter {
    font-size: 0.8em;
    margin-top: 2vw;
    padding-left: 4vw;
    width: 100%;
    display: flex;
    justify-content: start;
    align-items: center;

    .check-box {
      justify-content: start;

      /deep/ .van-checkbox__icon {
        .van-icon {
          width: 1em;
          height: 1em;
          line-height: 1em;
          border-radius: 50%;
        }
      }
    }

    .text {
      color: #ff9200;
      cursor: pointer;
    }
  }
}

.smsCodeBtn {
  /deep/.van-button--small {
    padding: 0 4px !important;
  }
}
</style>
