<template>
  <div class="login-code">
    <nav-bar title="设置支付密码" />
    <!-- 顶部图片 -->
    <!-- <div class="header">
      <img src="@/assets/images/login/bg_setPswd.png" />
    </div> -->
    <!-- 登录页 -->
    <div class="login-card">
      <van-form validate-first label-width="100" :show-error-message="false" :show-error="false">
        <van-field v-model="user.password" label="请输入支付密码" clearable type="password" placeholder="6-20位字母数字组合" maxlength="20" center>
          <template slot="left-icon">
            <img src="@/assets/images/icon/mima.png" class="icon" />
          </template>
        </van-field>
        <van-field v-model="passwordCheck" label="请确认支付密码" clearable type="password" placeholder="" maxlength="20" center>
          <template slot="left-icon">
            <img src="@/assets/images/icon/mima.png" class="icon" />
          </template>
        </van-field>
        <!-- 错误提示 -->
        <div class="err-tips">{{ errorMsg }}</div>
      </van-form>
    </div>
    <!-- 完成按钮 -->
    <div class="btn-wrap">
      <van-button block round :color="this.checked ? $setting.themeColor : '#9cd4f5'" size="large" @click="doSetPayPswd" :disabled="btnDisabled">完&nbsp;成</van-button>
    </div>
  </div>
</template>

<script>
import navBar from "@/components/navBar/NavHeader";
import { Toast } from "vant";
import { storage } from "Utils/common/";

// 弃用
export default {
  props: {},
  components: { navBar },
  data() {
    return {
      // 表单数据
      user: {
        phone: storage.ss.get("phone"),
        password: "",
        // 1 单位 2司机
        type: this.$setting.APP_TYPE,
      },
      // 密码二次确认
      passwordCheck: "",
      // 默认选中
      checked: true,
      // 默认可用
      btnDisabled: false,
      // 错误提示
      errorMsg: "",
    };
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {
    checked: function() {
      // true 不同意协议,禁止登录
      this.btnDisabled = !this.checked;
    },
  },
  methods: {
    // 校验密码
    check() {
      if ("" === this.user.password) {
        this.errorMsg = "* 请输入密码";
        return false;
      }
      if (this.$validate.RegExp_Rules.PASSWORD_REG.test(this.user.password)) {
        if (this.user.password === this.passwordCheck) {
          this.errorMsg = "";
          return true;
        } else {
          this.errorMsg = "* 两次密码不一致";
          return false;
        }
      } else {
        this.errorMsg = "* 密码必须为6-20位字母数字组合";
        return false;
      }
    },
    doSetPayPswd() {
      var isPass = this.check();
      if (!isPass) {
        return;
      }
      Toast("支付密码设置成功");
      // todo 设置支付密码
      setTimeout(() => {
        // 成功后跳到...
        // this.$router.replace({
        //   path: "/gasstationmap",
        // });
      }, 1200);
    },
  },
};
</script>

<style scoped lang="scss">
.login-code {
  .header {
    img {
      width: 100%;
    }
  }
  .login-card {
    height: 33vw;
    background: #fff;
    margin: 0 20px;
    margin-top: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    .other {
      text-align: right;
      color: #22a1d7;
      cursor: pointer;
      margin-right: 16px;
      line-height: 40px;
    }
    .icon {
      width: 27.5px;
      height: 29.5px;
      vertical-align: sub;
    }
    .err-tips {
      color: #ee0a24;
      // line-height: 1.4;
      padding: 30px 30px 0;
      text-align: left;
      min-height: 20px;
      font-size: 24px;
    }
    /deep/ .van-cell {
      background: none;
    }
  }
  .btn-wrap {
    padding: 0 20px;
    margin: 0 20px;
    margin-top: 50px;
    // position: relative;
    .van-button--disabled {
      // 按钮透明度
      opacity: 1;
    }
  }
  .check-box {
    margin-top: 80px;
    display: flex;
    justify-content: center;
    .text {
      color: #f37f06;
      cursor: pointer;
    }
  }
}
</style>
