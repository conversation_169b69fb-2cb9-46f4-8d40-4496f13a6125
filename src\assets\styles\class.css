.btnBc,
.actived {
  background: #F59A23 !important;
}

.btnFixed {
  position: fixed;
  bottom: 100px;
  left: 0;
}

.container {
  width: 100vw;
  height: 100vh;
}

/* 宽度相关 */
.vw50 {
  width: 50vw;
}

/* flex相关 */
.flex_start {
  display: flex;
  justify-content: start;
  align-items: center;
}

.flex_top {
  display: flex;
  align-items: flex-start;
  justify-content: space-around;
}

.flex_center {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex_between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 背景颜色相关 */
.bgWrite {
  background-color: #ffffff;
}

/* 字体颜色相关 */
.white {
  color: #ffffff;
}

/* 字体大小相关 */
.fs32 {
  font-size: 32px;
}

.fz26 {
  font-size: 26px;
}

/* text-align 相关 */
.tll {
  text-align: left;
}

.tac {
  text-align: center;
}

/* margin相关 */
.ml20 {
  margin-left: 20px;
}

.ml30 {
  margin-left: 30px;
}

.mt5 {
  margin-top: 5px;
}

.m10 {
  margin: 10px auto;
}

.m5 {
  margin: 5px auto;
}

/* padding相关 */
.p10 {
  padding: 10px auto !important;
}