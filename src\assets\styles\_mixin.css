@charset "UTF-8";
/** 
    全局宏mixin定义
    基础的常用的写进来 方便维护
*/
/* 基础相关 */
/*
    宽高
    @param $width css宽度
    @param $height css高度
*/
/* flex相关 */
/*
    flex 水平垂直居中
    @param $direction 方向，具体参考flex布局
*/
/*
    flex 基础布局
    @param $direction 方向，具体参考flex布局
    @param $wrap 是否换行，具体参考flex布局
*/
/*
    1px border
    @param $direction 方向，bottom,top,right,left,若为全边框则传(bottom,top,right,left)
    @param $color 边框颜色 默认#333
    @param $radius 四个角圆角值 默认值无
    @param $position css伪类 after或before 默认after
*/
/*
    1px border
*/
@media (-webkit-min-device-pixel-ratio: 1.5), (min-device-pixel-ratio: 1.5) {
  .border-1px ::after {
    -webkit-transform: scaleY(0.7);
    transform: scaleY(0.7);
  }
}

@media (-webkit-min-device-pixel-ratio: 2), (min-device-pixel-ratio: 2) {
  .border-1px ::after {
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }
}

@media (-webkit-min-device-pixel-ratio: 3), (min-device-pixel-ratio: 3) {
  .border-1px ::after {
    -webkit-transform: scaleY(0.33333);
    transform: scaleY(0.33333);
  }
}

@media (-webkit-min-device-pixel-ratio: 1.5), (min-device-pixel-ratio: 1.5) {
  .border-1px ::after {
    -webkit-transform: scaleY(0.7);
    transform: scaleY(0.7);
  }
}

@media (-webkit-min-device-pixel-ratio: 2), (min-device-pixel-ratio: 2) {
  .border-1px ::after {
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }
}

@media (-webkit-min-device-pixel-ratio: 3), (min-device-pixel-ratio: 3) {
  .border-1px ::after {
    -webkit-transform: scaleY(0.33333);
    transform: scaleY(0.33333);
  }
}

/**
 * @description: 组件公共样式混入
 * @return {*}
 */
