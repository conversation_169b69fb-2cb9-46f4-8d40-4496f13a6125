<template>
  <div class="my-ecard">
    <nav-bar title="我的加油卡" />
    <div class="my_card_details flex_center white">
      <div class="card_left ml30 tac">
        <logo />
        <div class="m10">中国石油</div>
        <div id="icon" @click="inverse">
          <van-icon name="closed-eye" size="25" v-if="hide" />
          <van-icon name="eye-o" size="25" v-else />
        </div>
      </div>
      <div class="card_right ml30 tll">
        <div id="city">{{ formatItem.city }}</div>
        <div>{{ formatItem.cardNo }}</div>
        <div>
          <div class="flex_between vw50">
            <div>
              余额(元)
              <div>
                {{ null === this.formatItem.balance ? "0.00" : this.formatItem.balance }}
              </div>
            </div>
            <div>
              总积分
              <div>
                {{ null === this.formatCredit ? "0" : this.formatCredit }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="my-ecard-links">
      <!-- <van-cell v-if="item.moneyType == 2" is-link title="加油卡充值" link-type="reLaunch" to="/my/ecard/recharge" /> -->
      <!-- <van-cell is-link title="本卡充值记录" link-type="reLaunch" to="/my/ecard/rechargeRecord" /> -->
      <!-- <van-cell is-link title="本卡消费记录" link-type="reLaunch" to="/my/ecard/consumingRecords" /> -->
      <van-cell is-link title="查询优惠合同" link-type="reLaunch" to="/my/ecard/discountContract" />
    </div>
  </div>
</template>

<script>
import navBar from "@/components/navBar/NavHeader";
import logo from "@/components/logo";
import filter from "@/filters/index.js"; // 不能用{}
import { storage } from "Utils/common/";

export default {
  props: {},
  components: {
    navBar,
    logo,
  },
  data() {
    return {
      hide: false,
      item: storage.ss.get("driverInfo"),
      // 积分
      credit: null,
    };
  },
  computed: {
    formatItem: function() {
      // 不隐藏,直接return
      if (!this.hide) {
        return {
          cardNo: this.item.cardNo,
          city: this.item.city,
          balance: filter.formatMoney(this.item.balance / 100),
          shareBalance: this.item.shareBalance,
          phone: this.item.phone,
          companyCode: this.item.companyCode,
        };
      }
      // 隐藏,替换成*,再return
      var result = {
        cardNo: filter.formatBankCard(this.item.cardNo),
        city: this.item.city,
        balance: filter.formatDef(this.item.balance),
        shareBalance: filter.formatDef(this.item.shareBalance),
        phone: filter.formatDef(this.item.phone),
        companyCode: filter.formatDef(this.item.companyCode),
      };
      return result;
    },
    formatCredit: function() {
      // 不隐藏,直接return
      if (!this.hide) {
        return this.credit;
      }
      // 隐藏,替换成*,再return
      return filter.formatDef(this.credit);
    },
  },
  created() {
    this.item = storage.ss.get("driverInfo");
  },
  activated() {
    this.item = storage.ss.get("driverInfo");
  },
  mounted() {},
  watch: {},
  methods: {
    // 取反
    inverse() {
      this.hide = !this.hide;
    },
  },
};
</script>

<style scoped lang="scss">
.my-ecard {
  width: 100%;
  min-height: 100vh;
  overflow: hidden;
  .my_card_details {
    width: 92vw;
    height: 285px;
    border-radius: 20px;
    background-image: url("../../assets/images/common/ecard_bg.png"); // 背景图片
    background-size: cover;
    padding: 20px 0;
    @include margin(20px, 0, 20px 0);
    .card_right {
      flex: 1;
      line-height: 38px;
      div {
        padding: 6px 0;
      }
    }
  }
  .my-ecard-links {
    width: $width700;
    height: auto;
    text-align: left;
    border-radius: 10px;
    background: #ffffff;
    // position: relative;
    margin: 0 auto; // div居中
    // 上/下 & 左/右
    padding: 10px 0;
    /deep/ .van-cell {
      padding-left: 25px;
      padding-right: 20px;
      padding-top: 15px;
      padding-bottom: 15px;
      height: 56px;
      .van-cell__title {
        display: flex;
        align-items: center;
        .icon {
          height: 20px;
          margin-right: 12px;
        }
      }
    }
  }
}
</style>
