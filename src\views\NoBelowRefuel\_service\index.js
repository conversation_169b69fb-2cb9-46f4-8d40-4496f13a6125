/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-08-29 08:34:10
 * @LastEditors: Yongon
 * @LastEditTime: 2022-10-25 16:41:11
 * @Description: file content
 */
import request from "Utils/http/"; // 导入axios封装函数

/**
 * 附近加油站列表接口
 * @param params
 * @returns {Promise<*>}
 * @constructor
 */
export async function GetStationInfos(params) {
  return request("post", "/OilStation/GetStationInfos", params, true, true, true, false, true);
}
// 获取加油卡信息
export function GetUserCardInfo(params) {
  return request("post", "/card/v1/getUserCardInfo", params, true, true, true, false, true);
}
// 10.2预授权下单
export async function PreAuthAddOrder(params) {
  return request("post", "/appcenter/preTrade/v1/preAuthAddOrder", params, true, true, true, false, true);
}
// 10.3撤销预授权下单
export async function CancelPreAuthAddOrder(params) {
  return request("post", "/appcenter/preTrade/v1/cancelPreAuthAddOrder", params, true, true, true, false, true);
}
// 10.4已完成预授权订单查询
export async function PreAuthOrderQuery(params) {
  return request("post", "/appcenter/preTrade/v1/preAuthOrderQuery", params, false, true, true, false, true);
}
// 导航列表接口
export async function GetNavigationList(params) {
  return request("post", "/user/v1/getNavigationList", params, true, true, true, false, true);
}
// 导航详情接口
export async function GetNavigationInfo(params) {
  return request("post", "/user/v1/getNavigationInfo", params, true, true, true, false, true);
}
// 11.6修改车牌号信息接口-陈东东
export async function ModifyLicenseNum(params) {
  return request("post", "/user/v1/modifyLicenseNum", params, true, true, true, false, true);
}
//11.4获取司机用户信息接口-陈东东
export async function GetDriverInfo(params) {
  return request("post", "/user/v1/getDriverInfo", params, true, true, true, false, true);
}
// 获取激活企业列表
export async function getActivatedList(params) {
  return request("post", "/user/v1/getActivatedList", params, true, true, true, false, false);
}
// 改变卡片状态
export async function changeState(params) {
  return request("post", "/card/appUser/v1/activeCards", params, true, true, true, false, true);
}
// 修改司机默认的企业
export async function updateDefaultCompanyCode(params) {
  return request("post", "/user/v1/updateDefaultCompanyCode", params, true, true, true, false, true);
}
// 获取油站服务详情（智慧司机之家）
export async function queryStationInfoList(params) {
  return request("post", "/user/v1/queryStationInfoList", params, true, true, true, false, true);
}
