/**
 *  @module axios封装
 *  <AUTHOR>
 *  @tips 封装前端请求axios，独立配置项config
 *        做请求头拦截，响应头拦截
 *        封装请求主体，目前为get,post请求
 *        针对每个接口：增加是否loading;是否全部返还response
 */

import axios from "axios";
import qs from "qs";
import cppeiBridge from "Utils/bridge";
// import {getDeviceId} from "Utils/bangbangEncrypt";
import router from "@/router";
import { throwErrorCode, throwErrorBussCode } from "./throwErrorCode";
import { Dialog, Toast } from "vant";
import { sign, storage, generateUUID, compareVersions, randomString } from "Utils/common/";
import md5 from "js-md5";
import { store } from "../../store/index"; // 引入vuex
import { getBangbangEncrypt } from '@/utils/bangbangEncrypt.js'
import { getBangbangEncrypt1 } from '@/utils/bangbangEncrypt1.js'

// vant提示组件
const vantErrorTips = (message, infoCode) => {
  if (infoCode == 253 || infoCode == 254 || infoCode == 258) {
    Dialog.alert({
      title: "提示",
      message: "token失效，请重新登录",
    }).then(() => {
      router.replace({ path: "/loginCenter" });
      cppeiBridge("clearToken", {});
      storage.ss.clear();
      storage.ss.remove("apptoken");
    });
  } else if (infoCode == 1 || infoCode == 4 || infoCode == 5 || infoCode == 7) {
  } else if (infoCode == 255) {
    Dialog.alert({
      title: "温馨提示",
      message: "抱歉，该企业注销了您的账户，如有问题，请与企业管理员联系",
      confirmButtonText: "我知道了",
    }).then(() => {
      cppeiBridge("clearToken", {});
      storage.ss.remove("apptoken");
      storage.ss.clear();
      router.replace({ path: "/loginCenter" });
    });
  } else {
    Dialog.alert({
      title: "提示",
      message,
    });
  }
};
const config = {
  baseURL: process.env.VUE_APP_AXIOS_URL,
  timeout: 60 * 1000,
};

axios.defaults.headers.post["Content-Type"] = "application/x-www-form-urlencoded;charset=UTF-8";

const _axios = axios.create(config);
const uuidStr = generateUUID();
// 请求头拦截
window._axiosPromiseArr = [];
_axios.interceptors.request.use(
  (config) => {
    config.cancelToken = new axios.CancelToken(function (cancel) {
      // store.commit('pushToken', { cancelToken: cancel })
      window._axiosPromiseArr.push({
        cancel,
      });
    });
    const { isShowLoading, isOtherBaseUrl } = config.headers;
    // 是否显示loading状态
    isShowLoading &&
      Toast.loading({
        duration: 0,
        mask: false,
        loadingType: "spinner",
        forbidClick: true, // 禁用背景点击
        message: "加载中...",
      });

    config.headers.token = "development" ? process.env.VUE_APP_TOKEN : storage.ss.get("appToken");
    config.headers['app-version'] = (storage.ss.get("deviceInfo") || {}).version;
    // TODO: 0121 增加多后端ip地址配置
    if (!!isOtherBaseUrl) {
      config.baseURL = isOtherBaseUrl;
    } else {
      config.baseURL = process.env.VUE_APP_AXIOS_URL;
    }
    return config;
  },
  (error) => {
    // 关闭loading
    Toast.clear();
    return Promise.reject(error);
  }
);

// 响应头拦截
_axios.interceptors.response.use(
  (resp) => {
    const { isBackAllResp, isHandleBySelf, isOtherBaseUrl } = resp.config.headers;
    // 关闭loading
    Toast.clear();
    if (resp.status === 200) {
      // TODO: 0121 增加多后端ip地址配置
      if (!!isOtherBaseUrl) {
        return Promise.resolve(resp.data);
      }
      // 当状态为200  infoCode为255时为销户状态
      // if(resp.data.infoCode == 255) {
      //   Dialog.alert({
      //     title: '温馨提示',
      //     message: '抱歉，该企业注销了您的账户，如有问题，请与企业管理员联系',
      //     confirmButtonText: '我知道了',
      //   }).then(() => {
      //     cppeiBridge("clearToken", {});
      //     router.push({path: "/loginCenter",});
      //     storage.ss.remove("apptoken");
      //   });
      // }
      if (resp.data[`${process.env.VUE_APP_FLAGFIELD}`] === Boolean(`${process.env.VUE_APP_FLAGFIELDVALUE}`)) {
        // 是否返还全部响应信息
        var respData = null;
        if (isBackAllResp) {
          respData = resp.data;
        } else {
          respData = resp.data[`${process.env.VUE_APP_TRUEDATA}`];
        }
        return Promise.resolve(respData);
      } else {
        // 若不成功 则检查业务code 响应错误消息
        isHandleBySelf && vantErrorTips(!!throwErrorBussCode(process.env.VUE_APP_BUSSCODEFIELD, resp.data) ? throwErrorBussCode(process.env.VUE_APP_BUSSCODEFIELD, resp.data) : resp.data[process.env.VUE_APP_ERRMSGFIELD], resp.data[process.env.VUE_APP_BUSSCODEFIELD]);
        if (isHandleBySelf) {
          return Promise.reject(isHandleBySelf ? resp.data[process.env.VUE_APP_ERRMSGFIELD] : resp.data);
        } else {
          return Promise.resolve(resp.data);
        }
      }
    } else {
      vantErrorTips("响应成功，但status非200");
      return Promise.reject("响应成功，但status非200");
    }
  },
  (error) => {
    // 清除toast
    Toast.clear();
    let errMsg = null;
    if (error && error.response) {
      errMsg = throwErrorCode(error.response.status, error.response);
    } else if (error.code === "ECONNABORTED") {
      // 服务器过早终止
      errMsg = "服务器过早终止";
    } else if (error.code === undefined || error.code === "undefined" || !window.navigator.onLine) {
      // 断网 这里可以通过vuex作断网更新视图操作 促使用户点击“重新加载”
      errMsg = "网络中断";
    } else {
      // 未知错误
      errMsg = "未知错误";
    }
    // vantErrorTips(errMsg);
    return Promise.reject(errMsg);
  }
);

/**
 *  @method  request
 *  <AUTHOR>
 *  @param {String} method 支持get,post 忽略大小写
 *  @param {String} url 接口地址
 *  @param {Object} body 请求入参 默认空对象
 *  @param {Boolean} isShowLoading 是否在请求过程中显示loading 默认true
 *  @param {Boolean} isBackAllResp 是否在返还所有响应信息 默认false
 *  @param {Boolean} isJSONPost 是否以JSON格式发送post请求 默认false
 *  @param {Boolean} isDeleteType 删除操作带有一个其他参数 默认false
 *  @param {Boolean} isHandleBySelf 当status为200 但是非成功code/data/value等时 由前端自己来处理逻辑 设置为true时isBackAllResp必须为true 默认false
 *  @param {String}  isOtherBaseUrl 是否为其他接口ip地址 其他逻辑全部丢前面处理 默认空
 */
export default async function request(method, url, body = {}, isShowLoading = true, isBackAllResp = false, isJSONPost = false, isDeleteType = false, isHandleBySelf = false, isOtherBaseUrl = "") {
  // 转化method
  method = method.toLocaleUpperCase();
  const timestamp = String(Math.round(new Date().getTime() / 1000));

  //---------------------斑马对业务进行两次加密-----------------------
  //-- ---------------对所有入参进行一次md5加密---------------------
  let commonParams = {
    //   wheretype: '16',
    noncestr: uuidStr,
    appid: '2',
    timestamp: timestamp,
    sign: ''
  };
  // //拼装公共参数和业务参数
  const businessData = Object.assign({}, commonParams, body);
  // //加密生产新的标签
  // let md5sign = md5(JSON.stringify(businessData) + '&zsy_key=CCLE9Y5H').toUpperCase();
  // //新的标签作为公共入参
  let commonUsedParams = {
    // wheretype: '16',
    noncestr: uuidStr,
    appid: '2',
    timestamp: timestamp,
    sign: sign(businessData, url)
  };
  // //新生产的公共参数和业务参数，拼装入参
  body = Object.assign({}, commonUsedParams, body);

  if (url != "https://itunes.apple.com/lookup?id=1623514959" && url != "/user/v1/getAppVersion" && url != "/user/v1/recordJsLog" && url != "/card/appUserOcr/v1/ocrIdCardIdentification") {
    console.log("加密前url========",url);
    console.log("body22=========加密前数据", JSON.stringify(body));
    let bangbangData;
    if (compareVersions(storage.ss.get("deviceInfo").version || '', '1.1.2') >= 0) {
      bangbangData = await getBangbangEncrypt1(body);
      console.log(bangbangData, 'bb');
    } else {
      bangbangData = await getBangbangEncrypt(body);
      console.log(bangbangData, 'aes');
    }

    let requestData = {};
    requestData.jsonData = bangbangData.encryptParam;

    requestData.channelCode = "fleetCard";
    body = requestData;
  }

  // TODO 生成uuid
  const uuid = "C15" + randomString(29);

  if (method === "GET") {
    return new Promise((resolve, reject) => {
      // 这里貌似axios 0.18.0以上的版本限制了自定义参数配置,我写在headers里面 但是不太清除有没得其他影响
      _axios
        .get(
          url,
          {
            params: body,
          },
          {
            headers: {
              isShowLoading,
              isBackAllResp,
              url,
              isHandleBySelf,
              isOtherBaseUrl,
              'client-uuid': uuid,
            },
          }
        )
        .then((response) => {
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  // post
  if (method === "POST") {
    return new Promise((resolve, reject) => {
      // 这里后端接口不规范 作了两层处理 第一层：有些接口需要提交json格式数据，有些序列化表单提交；第二层：针对删除接口还需要序列化一个arrayFormat??
      _axios
        .post(
          url,
          !isJSONPost
            ? !isDeleteType
              ? qs.stringify(body)
              : qs.stringify(body, {
                arrayFormat: "brackets",
                // })) : JSON.stringify(body), {
                // 修复上传图片
              })
            : body,
          {
            headers: {
              isShowLoading,
              isBackAllResp,
              url,
              isHandleBySelf,
              isOtherBaseUrl,
              'client-uuid': uuid,
              post: {
                "Content-Type": !isJSONPost ? "application/x-www-form-urlencoded;charset=UTF-8" : "application/json;charset=UTF-8",
              },
            },
          }
        )
        .then((response) => {
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }
}
