/*
 * @Author: <PERSON>on
 * @Date: 2022-08-19 10:00:43
 * @LastEditors: ltw <EMAIL>
 * @LastEditTime: 2024-04-26 17:24:43
 * @Description: file content
 */
import request from "Utils/http/"; // 导入axios封装函数

// 获取未激活企业列表
export async function getUnActivatedList(params) {
  return request("post", "/user/v1/getUnActivatedList", params, true, true, true, false, true);
}

// 开通司机卡
export async function doOpenCards(params) {
  return request("post", "/user/v1/activateCompanyOrDriver", params, true, true, true, false, true);
}

// 获取已开通未激活企业
export async function getOpenCardList(params) {
  return request("post", "/card/appUser/v1/getOpenCardList", params, true, true, true, false, true);
}

// 改变卡片状态
export async function changeState(params) {
  return request("post", "/card/appUser/v1/activeCards", params, true, true, true, false, true);
}

// 修改司机默认的企业
export async function updateDefaultCompanyCode(params) {
  return request("post", "/user/v1/updateDefaultCompanyCode", params, true, true, true, false, true);
}

// 查询出第一张卡号对应的公司id
export async function getCompanyInfoByCardNo(params) {
  return request("post", "/card/appUser/v1/getCompanyInfoByCardNo", params, true, true, true, false, true);
}
