NODE_ENV = 'production'

# 配置公共路径
BASE_URL = '/fleetcard_driver'

# 离线缓存,本地缓存提供服务(未使用)
PUBLIC_URL = '/'

# 判断打包环境指定对应的服务器id(自动部署用到)
VUE_APP_SERVER_ID=0

# 图片验证码路径前缀
# VUE_APP_AXIOS_URL = '/ApiDriver'
VUE_APP_AXIOS_URL = ''

# 生产环境构建文件的目录
VUE_APP_BUILD_FOLDER = 'fleetcard_driver'

# 是否开启打包分析
VUE_APP_ISREPORTER = 'false'

# 是否配置gzip压缩
VUE_APP_iSGZIP = 'true'

# 是否开启骨架屏方案
VUE_APP_ISSKELETON = "false"

# 是否去掉console.log debugger等冗余代码（false可以看见）
VUE_APP_ISREMOVECODE = 'false'

# 接口响应是否执行成功
VUE_APP_FLAGFIELD = "value"
# 是否返回接口响应全部信息
VUE_APP_FLAGFIELDVALUE = true
# 接口响应信息内容
VUE_APP_TRUEDATA = "data"
# 接口响应信息文本
VUE_APP_ERRMSGFIELD = "info"
# 接口响应信息编码 0失败；1成功
VUE_APP_BUSSCODEFIELD = "infoCode"