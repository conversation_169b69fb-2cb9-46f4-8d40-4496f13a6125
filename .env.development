NODE_ENV = "development"

# 配置公共路径
BASE_URL = "/fleetcard_driver"

# 判断打包环境指定对应的服务器id(自动部署用到)
VUE_APP_SERVER_ID=1

# UAT环境
VUE_APP_API_URL = "https://cdkdev.kunlunjyk.com/"

# ② 配置mock模拟数据
VUE_APP_MOCK = 'false'
# ③ 服务监听端口
VUE_APP_PORT = "8888"

# 图片验证码路径前缀
VUE_APP_AXIOS_URL = "/Api"

# 是否开启骨架屏方案
VUE_APP_ISSKELETON = "false"

# 是否去掉console.log debugger等冗余代码（false可以看见）
VUE_APP_ISREMOVECODE = 'false'

# 接口响应是否执行成功
VUE_APP_FLAGFIELD = "value"
# 是否返回接口响应全部信息
VUE_APP_FLAGFIELDVALUE = true
# 接口响应信息内容
VUE_APP_TRUEDATA = "data"
# 接口响应信息文本
VUE_APP_ERRMSGFIELD = "info"
# 接口响应信息编码 0失败；1成功
VUE_APP_BUSSCODEFIELD = "infoCode"
# 用户token标识
VUE_APP_TOKEN = ""