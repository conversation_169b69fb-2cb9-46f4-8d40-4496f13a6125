<template>
  <div class="certification-name">
    <nav-header title="实名认证" left-arrow />
    <div>
      <!-- <div class="desc">请拍摄上传您本人身份证的头像面</div> -->
      <div class="photo-wrap">
        <div class="desc">请拍摄上传您本人身份证的头像面</div>
        <div class="photo">
          <van-uploader v-if="frontPic.length == 0" :preview-image="false" v-model="frontPic" ref="clickUploadFront" :after-read="afterReadFront" :deletable="false" :max-count="1" preview-size="120px" style="width: 60%"></van-uploader>
          <!-- 解决IOS设备上上传图片后偶现预览为空白的问题 -->
          <van-image v-else class="list-img" v-for="(item,index) in frontPic" :key="index" :src="item.content"></van-image>
          <!-- 蒙层 -->
          <div class="mask" v-if="!isPermissions" @click.stop="getPermissions(true)"></div>
          <div class="upload">
            <div class="upload-msg">{{ frontErr }}</div>
            <van-button block :color="$setting.themeColor" size="small" native-type="button" @click="deleteImg('front')">重新上传</van-button>
          </div>
        </div>
      </div>
      <div class="photo-wrap">
        <div class="desc">请拍摄上传您本人身份证的国徽面</div>
        <div class="photo">
          <van-uploader v-if="backPic.length == 0" :preview-image="false" v-model="backPic" ref="clickUploadBack" :after-read="afterReadBack" :deletable="false" :max-count="1" preview-size="120px" style="width: 60%; border: 1px solid"> </van-uploader>
          <!-- 解决IOS设备上上传图片后偶现预览为空白的问题 -->
          <van-image v-else class="list-img" v-for="(item,index) in backPic" :key="index" :src="item.content"></van-image>
          <!-- 蒙层 -->
          <div class="mask" v-if="!isPermissions" @click.stop="getPermissions(false)"></div>
          <div class="upload">
            <div class="upload-msg">{{ backErr }}</div>
            <van-button block :color="$setting.themeColor" size="small" native-type="button" @click="deleteImg('back')">重新上传</van-button>
          </div>
        </div>
      </div>
    </div>
    <div>
      <van-cell-group style="margin-bottom: 15px; text-align: left">
        <!-- <van-cell title="本人姓名" :value="name" />
        <van-cell title="身份证号" :value="icCardNumber" /> -->
        <van-field label="姓名" v-model="name" />
        <van-field label="身份证号" v-model="icCardNumber"/>
      </van-cell-group>
      <van-checkbox class="check-box" ref="checkbox" label-disabled :checked-color="$setting.themeColor" v-model="agree" shape="square">
        <span @click="onChecked">同意</span>
        <span class="text" @click="jumpToContent(2)">《认证协议》</span>
      </van-checkbox>
    </div>
    <div class="btn-wrap">
      <van-button block round :color="$setting.themeColor" :disabled="isDisabled" @click="onSubmit">提交</van-button>
    </div>
  </div>
</template>

<script>
import { Toast } from "vant";
import { storage, isAndroid } from "Utils/common";
import NavHeader from "@/components/navBar/NavHeader";
import editUploadImage from "Utils/editUploadImage";
import { mapState, mapMutations } from "vuex";
import { batchCertification } from "./_service";
import { recordJsLog } from "@/utils/recordJsLog";
import { useGetLocation } from '@/utils/useGetLocation';

export default {
  components: {
    NavHeader,
  },
  data() {
    return {
      frontPic: [],
      backPic: [],
      pos: "",
      frontErr: "",
      backErr: "",
      name: "",
      icCardNumber: "",
      errMsg: "",
      agree: false,
      backSucess: false,
      isFrontBack: true,
    };
  },
  computed: {
    ...mapState(["permissions"]),
    isDisabled() {
      return this.name && this.icCardNumber && (!this.backSucess || !this.agree);
    },
    isPermissions() {
      return this.permissions.album && this.permissions.camera;
    },
  },
  mounted() {
    window.onGetPermissions = this.onGetPermissions;
    window.onGetLocation = this.onGetLocation;
  },
  methods: {
    ...mapMutations(["Update_permissions"]),

    /**
     * @description: 获取当前定位信息
     * @return {*}
     */

    onGetLocation(params) {
      useGetLocation(params, this.realNameAuthentication);       
    },

    /**
     * @description: sdk获取隐私权限回调
     * @return {*}
     */

    onGetPermissions(params) {
      recordJsLog('onGetPermissions', params);

      console.log("IdentityCardOCR 隐私权限回调", params);
      this.Update_permissions(params);

      // 跳转到应用详情设置页 （注：仅限Android, 后续有小版本可考虑适配各厂商，跳转到具体的权限设置页）
      if (!params.camera) {
        this.$Dialog
          .alert({
            title: "相机权限需要",
            message: isAndroid() ? "请前往设置->应用->中油车队端->权限中打开相机相关权限，否则功能无法正常运行!" : "请前往设置->中油车队端打开相机相关权限，否则功能无法正常运行！",
          })
          .then(() => {
            setTimeout(() => {
              recordJsLog('openAuthSetting');

              this.$cppeiBridge("openAuthSetting", {});
            }, 500);
          })
          .catch(() => {});
      } else if (!params.album) {
        this.$Dialog
          .alert({ title: "存储权限需要", message: "请前往设置->应用->中油车队端->权限中打开存储相关权限，否则功能无法正常运行！" })
          .then(() => {
            setTimeout(() => {
              recordJsLog('openAuthSetting');

              this.$cppeiBridge("openAuthSetting", {});
            }, 500);
          })
          .catch(() => {});
      } else {
        if (this.isFrontBack) {
          this.$refs.clickUploadFront.chooseFile();
        } else {
          this.$refs.clickUploadBack.chooseFile();
        }
      }
    },

    /**
     * @description: 实名认证
     * @return {*}
     */

    onSubmit() {
      if (this.frontPic.length === 0 || this.backPic.length === 0) {
        Toast("请上传身份证");
        return;
      }
      const deviceInfo = storage.ss.get("deviceInfo");
      // 判断是否有定位信息，没有则获取
      if (deviceInfo?.province || deviceInfo?.city || deviceInfo?.district) {
        this.realNameAuthentication();
        
      } else {
        recordJsLog('getLocation', { route: 'identityCardOCR'});
        this.$cppeiBridge("getLocation", { route: 'identityCardOCR'});
      }
    },

    /**
     * @description: 10.14二要素修改
     * @return {*}
     */
    realNameAuthentication() {
      const params = {
        channel: 912,
        realNameAuthenticationArray: [
          {
            mobileNumber: storage.ss.get("phone"),
            cardNumber: this.icCardNumber,
            name: this.name,
            idnoType: "1",
            local: storage.ss.get("deviceInfo")?.province + storage.ss.get("deviceInfo")?.city,
            gpsLocation: storage.ss.get("deviceInfo")?.province + storage.ss.get("deviceInfo")?.city,
          },
        ],
      };
      this.$http("POST", `/card/appUserOcr/v1/batchCertification`, params, true, true, true)
        .then((res) => {
          if (res.infoCode === 1) {
            storage.ss.set("verifyUnique", res.data.authSuccessResultArray[0]?.verifyUnique);
            storage.ss.set("idCard", this.icCardNumber);
            storage.ss.set("name", this.name);
            // 跳到人脸识别(实人认证)
            this.$router.push({ path: "/faceRecognition" });
          } else {
            Toast(res.info);
          }
        })
        .catch((e) => {
          Toast(e.info);
        });
    },

    /**
     * @description: 实名认证提交
     * @return {*}
     */

    // realNameAuthentication() {
    //   const params = {
    //     type: 1,
    //     idCard: this.icCardNumber,
    //     name: this.name,
    //     phone: storage.ss.get("phone"),
    //     gpsLocation: storage.ss.get("deviceInfo")?.province + storage.ss.get("deviceInfo")?.city,
    //   };
    //   this.$http("POST", `/card/appUserOcr/v1/realNameAuthentication`, params, true, true, true)
    //     .then((res) => {
    //       if (res.infoCode === 1) {
    //         storage.ss.set("verifyUnique", res.data.verifyUnique);
    //         storage.ss.set("idCard", this.icCardNumber);
    //         storage.ss.set("name", this.name);
    //         // 跳到人脸识别(实人认证)
    //         this.$router.push({ path: "/faceRecognition" });
    //       } else {
    //         Toast(res.info);
    //       }
    //     })
    //     .catch((e) => {
    //       Toast(e.info);
    //     });
    // },

    /**
     * @description: 点击上传区域  获取权限获取隐私权限是否授权，JS与APP协商定义，方便扩展（请求几个返回几个）
     * @return {*}
     */

    getPermissions(bool) {
      this.isFrontBack = bool;
      // if(!this.isPermissions){
      recordJsLog('getPermissions', {
        permissions: ["camera", "album"], //camera-相机，album-相册
      });
      
      this.$cppeiBridge("getPermissions", {
        permissions: ["camera", "album"], //camera-相机，album-相册
      });
      // }
    },

    /**
     * @description: 认证协议
     * @param {*} type
     * @return {*}
     */

    jumpToContent(type) {
      this.$router.push({
        path: "/personAuthAgreement",
      });
    },

    // 点击同意协议
    onChecked() {
      this.$refs.checkbox.toggle();
    },

    // 上传身份证图片
    afterReadFront(file) {
      this.frontPic = []
      this.frontErr = "上传中...";
      editUploadImage.imgPreview(file.file, this.postImgFront);
    },
    afterReadBack(file) {
      this.backPic = []
      this.backSucess = false;
      this.backErr = "上传中...";
      editUploadImage.imgPreview(file.file, this.postImgBack);
    },

    // 提交图片到后端
    postImgFront(base64) {
      this.frontPic = [{content: base64}];
      const deviceInfo = storage.ss.get("deviceInfo") || {};
      const params = {
        type: 1,
        idCardSide: "front",
        headPortrait: base64,
        devSn: deviceInfo.deviceId || "pc端",
      };
      // 提交图片
      this.$http("POST", `/card/appUserOcr/v1/ocrIdCardIdentification`, params, true, true, true)
        .then((res) => {
          if (res.infoCode == 1) {
            console.log('postImgFront:'+JSON.stringify(base64).slice(0,100))
            this.name = res.data.name;
            this.icCardNumber = res.data.icCardNo;
            storage.ss.set("name", res.data.name);
          }
          this.frontErr = res.info;
        })
        .catch((e) => {
          this.frontErr = e.info;
        });
    },

    postImgBack(base64) {
      this.backPic = [{content: base64}];
      const deviceInfo = storage.ss.get("deviceInfo") || {};
      const params = {
        type: 1,
        idCardSide: "back",
        headPortrait: base64,
        devSn: deviceInfo.deviceId || "pc端",
      };
      // 提交图片
      this.$http("POST", `/card/appUserOcr/v1/ocrIdCardIdentification`, params, true, true, true)
        .then((res) => {
          if (res.infoCode == 1) {
            console.log('postImgBack:'+JSON.stringify(base64).slice(0,100))
            this.backSucess = true;
          }
          this.backErr = res.info;
        })
        .catch((e) => {
          this.backErr = e.info;
        });
    },

    // 重新上传
    deleteImg(pos) {
      this[pos + "Pic"] = [];
      this[pos + "Err"] = "";
    },
  },
};
</script>

<style scoped lang="scss">
.certification-name {
  .photo-wrap {
    padding: 10px 16px;
    background: #fff;

    .desc {
      margin: 15px 0;
    }

    /deep/ .van-uploader__upload {
      width: 100% !important;
    }

    /deep/ .van-image {
      // width: 100% !important;
    }

    .photo {
      display: flex;
      justify-content: space-between;
      position: relative;

      .mask {
        width: 410px;
        height: 240px;
        position: absolute;
        // border:1px solid #ee0a24;
      }
      .list-img{
        width: 410px;
        height: 240px;
      }

      .upload {
        width: 35%;
        display: flex;
        flex-direction: column;
        justify-content: space-around;

        .upload-msg {
          color: #ee0a24;
          line-height: 1.4;
          font-size: 12px;
          min-height: 20px;
        }
      }
    }
  }

  .check-box {
    margin-top: 10px;
    display: flex;
    justify-content: center;
    align-items: flex-start;

    .text {
      color: #ff9200;
      cursor: pointer;
    }
  }

  .btn-wrap {
    padding: 20px 20px;
  }
}
</style>
