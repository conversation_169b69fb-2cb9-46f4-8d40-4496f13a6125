/*
 * @Author: <PERSON><PERSON>
 * @LastEditors: Yongon
 * @Description: file content
 */
import request from "Utils/http/"; // 导入axios封装函数

// ocr识别
export async function ocr(params) {
  return request("post", "/card/appUserOcr/v1/ocrIdCardIdentification", params, true, true, true, false, true);
}

// 实名认证
export async function realNameAuthentication(params) {
  return request("post", "/card/appUserOcr/v1/realNameAuthentication", params, true, true, true, false, true);
}

// 实人认证
export async function personFaceAuthentication(params) {
  return request("post", "/card/appUserOcr/v1/personFaceAuthentication", params, true, true, true, false, true);
}

/*** 二要素修改  ***/
// 批量三要素用户认证
export async function batchCertification(params) {
  return request("post", "/card/appUserOcr/v1/batchCertification", params, true, true, true, false, true);
}

// 用户信息初始化人脸认证
export async function initFaceVerifyByUserInfo(params) {
  return request("post", "/card/appUserOcr/v1/initFaceVerifyByUserInfo", params, true, true, true, false, true);
}

// 人脸活体认证结果查询
export async function describeFaceVerify(params) {
  return request("post", "/card/appUserOcr/v1/describeFaceVerify", params, true, true, true, false, true);
}
