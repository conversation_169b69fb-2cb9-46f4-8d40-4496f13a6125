<!--
 * @Author: Yongon
 * @Date: 2022-07-12 17:19:23
 * @LastEditors: Yongon
 * @LastEditTime: 2022-07-14 11:37:37
 * @Description: file content
-->
<template>
  <div class="home">
    <nav-bar title="认证协议" />
    <div class="title">
      <p>中油车队端认证协议</p>
    </div>
    <div class="content">
      <div class="content-item">
        <div></div>
        <p class="content-2-level">
          本协议是您与中国石油天然气股份有限公司之间关于实名、实人验证相关内容所订立的协议,请您务必认真阅读本协议，在确认充分了解后慎重决定是否同意本协议。您主动勾选或点击同意后，本协议生效，对您及中国石油天然气股份有限公司均具有法律约束力。
        </p>
        <p class="content-2-level">
          一、为了更好地提供服务，中国石油天然气股份有限公司开发了实名、实人验证功能，该功能由包括但不限于公安部“互联网+”可信身份认证平台等机构提供核验数据及技术支持。您已将个人身份信息(包括姓名、身份证号、手机号和人脸)提交给中国石油天然气股份有限公司，以及合作的验证服务提供商。
        </p>
        <p class="content-2-level">
          二、您在实名、实人验证过程中，应提供本人合法、真实、有效、准确并完整的资料。
        </p>
        <p class="content-2-level">
          三、如你不同意授权，请不要点击“下一步”或“同意”，同时您将无法继续使用认证功能，进而也无法继续使用电子加油卡功能，但这并不影响你使用其他服务。
        </p>
        <p class="content-2-level">
          四、实名、实人验证服务提供商会尽商业上的合理努力提升认证结果的准确性。但由于能采集到的信息范围有限，以及行业技术水平及认证手段仍在完善过程中，实名、实人验证服务提供商无法确保认证结果的绝对准确。如您发现认证结果不准确，您可联系中国石油客服（956100）沟通解决。
        </p>
        <p class="content-2-level">
          五、如您发现我们采集、使用您个人信息的行为，违反了法律、行政法规规定或违反了与您的约定,或您发现我们釆集、储存的您的个人信息有错误的，您可联系中国石油客服（956100），要求删除或更正。
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import navBar from "@/components/navBar/NavHeader";

export default {
  props: {},
  components: { navBar },
  data() {
    return {};
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {},
};
</script>

<style scoped lang="scss">
.home {
  background-color: white !important;
  .title {
    color: red;
    font-weight: bold;
    font-size: 4.5vw;
  }
  .content {
    @include useScrollByBrowser(0);
    padding: 0 20px;
    .content-item {
      padding: 5px 0;
      margin: 10px 10px 0 10px;
      .content-1-level {
        font-size: 4vw;
      }
      .content-2-level {
        text-indent: 2em;
      }
      .content-3-level {
        text-indent: 2em;
      }
      p {
        // p标签换行
        word-wrap: break-word;
        word-break: break-all;
        text-align: left;
        font-family: "Microsoft YaHei", "微软雅黑", "MicrosoftJhengHei", "华文细黑", "STHeiti", "MingLiu";
        font-size: 20px;
        margin: 8px 0;
        line-height: 5vw;
      }
    }
  }
}
</style>
