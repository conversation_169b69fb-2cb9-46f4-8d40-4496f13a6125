<!--
 * @Author: Yongon
 * @Date: 2022-05-17 15:59:16
 * @LastEditors: Yongon
 * @LastEditTime: 2022-05-17 15:59:22
 * @Description: file content
-->
<template>
  <div class="no-auth">
    <div class="title">中石油企业司机版</div>
    <img src="@/assets/images/login/face-no-auth.png" />
    <div class="tip">您并未被邀请授权使用, 您可以联系您的企业管理员</div>
    <div class="btn">
      <van-button round @click="back">关闭</van-button>
    </div>
  </div>
</template>

<script>
  export default {
    props: {},
    components: {},
    data() {
      return {};
    },
    computed: {},
    created() {},
    mounted() {},
    watch: {},
    methods: {
      back() {
        this.$router.go(-1);
        // this.$router.replace({
        //   path: "/loginCenter",
        // });
      },
    },
  };
</script>

<style scoped lang="scss">
  .no-auth {
    overflow: hidden;

    .title {
      font-size: 36px;
      text-align: left;
      margin: 150px 0 0 60px;
    }

    img {
      height: 238px;
      width: auto;
      margin: 212px 0 45px 0;
    }

    .tip {
      margin-bottom: 130px;
    }

    .btn {
      /deep/ .van-button {
        width: 326px;
        height: 55px;
        background-color: rgba(255, 146, 0, 100%) !important;
      }
    }
  }
</style>