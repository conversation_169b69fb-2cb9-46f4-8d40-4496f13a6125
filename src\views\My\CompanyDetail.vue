<template>
  <div class="device-management">
    <nav-bar title="详情" />
    <!-- 列表 -->
    <van-cell-group>
      <van-cell center title="姓名：" :value="!driverInfo ? '无' : driverInfo.name" />
      <van-cell center title="证件号码：" :value="!driverInfo ? '无' : driverInfo.idNo" />
      <van-cell center title="手机号：" :value="!driverInfo ? '无' : driverInfo.phone" />
      <van-cell center title="加油卡号：" :value="!driverInfo ? '无' : driverInfo.cardNo" />
      <van-cell id="car-number-plate-input" @click="isShowKeybord = true" center title="车牌号码：" :value="!driverInfo ? '请点击按钮新增' : driverInfo.licenseNum">
        <template #right-icon>
          <van-button id="btn" round size="small" :color="$setting.themeColor">新增/更换车牌</van-button>
        </template>
      </van-cell>
    </van-cell-group>
    <div class="btn-wrap">
      <van-button block round :color="$setting.themeColor" @click="doModifyLicenseNum">保 存</van-button>
    </div>
    <!-- 车牌键盘 -->
    <div class="car-number-plate" id="car-number-plate">
      <!-- keybord -->
      <div class="car-number-plate__keybord" v-if="isShowKeybord">
        <!-- header -->
        <div class="keybord-header" @click="onClose">
          <span>上下滑动，切换键盘</span>
          <span>关闭</span>
        </div>
        <!-- body -->
        <van-swipe class="my-swipe" indicator-color="#ff8c00" :vertical="true" :loop="false">
          <van-swipe-item class="keybord-body" v-show="isShowProvice">
            <div class="keybord-body__row" v-for="(item, index) in dsProvice" :key="index">
              <template v-for="(el, i) in item">
                <div class="row-item" v-if="el !== 'delete'" :key="i" @click="onKeyClick(el)">{{ el }}</div>
                <div class="row-item-delete row-item" v-else :key="i" @click="onKeyClick(el)">
                  <!-- <img src="static/images/btn_delect.png" alt=""> -->
                  {{ el }}
                </div>
              </template>
            </div>
          </van-swipe-item>
          <van-swipe-item class="keybord-body">
            <div class="keybord-body__row" v-for="(item, index) in dsNumber" :key="index">
              <template v-for="(el, i) in item">
                <div class="row-item" v-if="el !== 'delete'" :key="i" @click="onKeyClick(el)">{{ el }}</div>
                <div class="row-item-delete row-item" v-else :key="i" @click="onKeyClick(el)">
                  <!-- <img src="static/images/btn_delect.png" alt=""> -->
                  {{ el }}
                </div>
              </template>
            </div>
          </van-swipe-item>
        </van-swipe>
      </div>
    </div>
  </div>
</template>

<script>
import navBar from "@/components/navBar/NavHeader";
import { Toast } from "vant";
import { storage } from "Utils/common/";
import { getDriverInfo, ModifyLicenseNum } from "./_service/";

export default {
  props: {},
  components: { navBar },
  data() {
    return {
      // 设备编号
      deviceName: storage.ss.get("deviceInfo") ? storage.ss.get("deviceInfo").deviceId : "1",
      driverInfo: storage.ss.get("driverInfo"),
      show: false,
      value: "",
      showPicker: false,
      licenseNumPrefix: "川",
      defaultIndex: 22,
      licenseNumSuffix: "",
      provinceAbbrs: this.$validate.RegExp_Rules.PROVINCE_ABBRS,

      dsProvice: [
        ["京", "沪", "粤", "津", "冀", "豫", "云", "辽", "黑", "湘"],
        ["皖", "鲁", "新", "苏", "浙", "赣", "鄂", "桂", "甘"],
        ["晋", "蒙", "陕", "吉", "闽", "贵", "渝", "川", "使"],
        ["青", "藏", "琼", "宁", "学", "挂", "警", "领", "delete"],
      ],
      dsNumber: [
        ["1", "2", "3", "4", "5", "6", "7", "8", "9", "0"],
        ["Q", "W", "E", "R", "T", "Y", "U", "P", "A", "S"],
        ["D", "F", "G", "H", "J", "K", "L", "Z", "X"],
        ["C", "V", "B", "N", "M", "港", "澳", "delete"],
      ],
      curNumberPlate: [], // 当前已填写的车牌号集合
      isShowKeybord: false,
      isShowProvice: true,
    };
  },
  computed: {
    // 新车牌
    newLicenseNum: function() {
      return this.licenseNumPrefix + this.licenseNumSuffix;
    },
  },
  created() {
    this.driverInfo = storage.ss.get("driverInfo");
    this.loadData();
    // 监听，除了点击自己，点击其他地方将自身隐藏
    document.addEventListener("click", (e) => {
      const dom = document.getElementById("car-number-plate");
      const domInput = document.getElementById("car-number-plate-input");
      if (dom || domInput) {
        const flag = dom.contains(e.target) || domInput.contains(e.target);
        if (!flag) {
          this.isShowKeybord = false;
        }
      }
    });
  },
  activated() {
    this.driverInfo = storage.ss.get("driverInfo");
  },
  mounted() {},
  watch: {
    curNumberPlate: function(val, oldVal) {
      if (val.length >= 1) this.isShowProvice = false;
      else this.isShowProvice = true;
    },
  },
  methods: {
    // 车牌前缀
    onChange(picker, value, index) {
      this.licenseNumPrefix = `${value}`;
    },
    // 更换车牌
    doModifyLicenseNum() {
      // if (!LICENSE_NUM_REG.test(this.driverInfo?.licenseNum)) {
      if (!this.$validate.isCarNumber(this.driverInfo?.licenseNum)) {
        Toast("车牌 [ " + this.driverInfo?.licenseNum + " ] 输入错误，请重新输入！");
        return false;
      }
      var params = {
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        companyCode: storage.ss.get("defaultCompanyCode"),
        phone: storage.ss.get("phone"),
        userid: storage.ss.get("userId"),
        cardNo: this.driverInfo.cardNo,
      };
      params.licenseNum = this.driverInfo?.licenseNum;
      ModifyLicenseNum(params)
        .then((res) => {
          this.show = false;
          // 刷新页面
          this.loadData();
          setTimeout(() => {
            Toast("车牌更换成功");
          }, 300);
        })
        .catch((err) => {
          console.log(err);
          Toast("车牌更换失败");
        });
    },
    // 加载数据
    loadData() {
      getDriverInfo({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        companyCode: storage.ss.get("defaultCompanyCode"),
        phone: storage.ss.get("phone"),
        userid: storage.ss.get("userId"),
      })
        .then((res) => {
          if (res.infoCode == 1) {
            this.driverInfo = res.data;
            if (this.driverInfo?.licenseNum) {
              this.curNumberPlate = this.driverInfo.licenseNum.split("");
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    // 车牌键盘
    // 字母
    onKeyClick(el) {
      if (el !== "delete") {
        // 非删除字母
        if (this.curNumberPlate.length <= 7) {
          this.curNumberPlate.push(el);
        }
      } else {
        this.curNumberPlate.pop();
      }
      this.driverInfo.licenseNum = this.curNumberPlate.join("");
      // this.$emit('update:carNo', carNo)
    },
    // 关闭
    onClose() {
      this.isShowKeybord = false;
      // this.$emit('onClose')
    },
  },
};
</script>

<style scoped lang="scss">
.device-management {
  /deep/ .van-cell-group {
    background-color: rgba(239, 239, 239, 100%) !important;
    .van-cell {
      margin: 2px;
      .van-cell__title {
        text-align: left;
      }
      #btn {
        margin: 0 0 0 10px;
      }
    }
  }
  .van-popup {
    height: 46%;
    .choose {
      .van-row {
        #col-right {
          height: auto;
          margin: 54px 0 0 0;
          /deep/ .van-field__body {
            font-size: 16px;
          }
        }
      }
    }
  }
  .btns {
    .van-row {
      margin-top: 40px;
      padding: 0 46px;
      .van-button {
        height: 36px;
        width: 126px;
      }
    }
  }
  .btn-wrap {
    margin: 40px 20px 0;
  }
}
</style>

<style scoped lang="scss">
$color-theme-o: red;
.car-number-plate {
  display: flex;
  justify-content: center;
  width: 100%;
  &__input {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .number-items {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      width: 34px;
      height: 49px;
      background: #fff7f2;
      border: 1px solid $color-theme-o;
      border-radius: 3px;

      // font-size $font-size-large
      // color $color-base-text
      position: relative;
    }
    .number-items-dashed {
      text-align: center;
      border: 1px solid #00f05f;
      background: #83f0ae;
      .dsshed-new {
        font-size: 11px;
        padding: 0 6px;
        line-height: 14px;
        color: #ffffff;
      }
    }
    // 当前号码位置
    // .active {
    // &::before {
    //   content: "";
    //   position: absolute;
    //   right: 0px;
    //   bottom: 0;
    //   display: block;
    //   width: 0;
    //   height: 0;
    //   border: 3px solid;
    //   border-color: transparent $color-theme-o $color-theme-o transparent;
    // }
    // }
  }
  // keybord
  &__keybord {
    position: fixed;
    bottom: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 60vw;
    background-color: #ebebeb;
    .keybord-header {
      display: flex;
      justify-content: space-between;
      padding-left: 10px;
      height: 10vw;
      line-height: 10vw;
      text-align: right;
      background-color: #fff;

      & > span {
        margin-right: 10px;
        color: #5cacee;
        font-size: 28px;
      }
    }
    .keybord-body {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      background-color: #ebebeb;
      &__row {
        display: flex;
        flex-direction: row;
        justify-content: center;
        .row-item {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          width: 8vw;
          height: 10vw;
          margin: 0.7vw;
          font-size: 16px;
          background-color: #fff;
          border-radius: 4px;
        }
        .row-item-delete {
          width: 12vw;
          .icon-oil {
            font-size: 20px;
          }
          img {
            width: 30px;
            height: 20px;
          }
        }
      }
    }
    // swipe
    .my-swipe {
      width: 100%;
      height: 100%;
      /deep/ .van-swipe-item {
        width: 100%;
      }
      /deep/ .van-swipe__indicators {
        top: 60%;
        left: 5px;
        .van-swipe__indicator {
          background-color: #111;
        }
      }
    }
  }
}
</style>
