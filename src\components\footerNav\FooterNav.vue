<template>
  <van-tabbar>
    <van-tabbar-item v-for="(item, index) in tabBarObj" :key="index" @click="checkNav(item)" :name="item.name">
      <span :class="active == item.name ? 'inactive-text' : 'default_txt'">{{ item.text }}</span>
      <template #icon="">
        <img :src="active === item.name ? item.icon.inactive : item.icon.active" />
      </template>
    </van-tabbar-item>
  </van-tabbar>
</template>

<script>
import { Tabbar, TabbarItem } from "vant";
import { storage } from "Utils/common/";
export default {
  components: {
    [Tabbar.name]: Tabbar,
    [TabbarItem.name]: TabbarItem,
  },
  data() {
    return {
      bottomSafeArea: "",
      active: "gasstationmap",
      tabBarObj: [
        {
          name: "gasstationmap",
          text: "加油",
          path: "/gasstationmap",
          icon: {
            active: require("@/assets/images/winter/jiayou.png"),
            inactive: require("@/assets/images/winter/jiayou_select.png"),
          },
        },
        {
          name: "noBelowRefuel",
          text: "支付",
          path: "/noBelowRefuel",
          icon: {
            active: require("@/assets/images/winter/zhifu.png"),
            inactive: require("@/assets/images/winter/zhifu_select.png"),
          },
        },
        {
          name: "paymentCode",
          text: "扫码",
          path: "/paymentCode",
          icon: {
            active: require("@/assets/images/winter/zhifu.png"),
            inactive: require("@/assets/images/winter/zhifu_select.png"),
          },
        },
        {
          name: "gasNav",
          text: "导航",
          path: "/gasNav",
          icon: {
            active: require("@/assets/images/winter/daohang.png"),
            inactive: require("@/assets/images/winter/daohang_select.png"),
          },
        },
        {
          name: "my",
          text: "我的",
          path: "/my",
          icon: {
            active: require("@/assets/images/winter/person.png"),
            inactive: require("@/assets/images/winter/person_select.png"),
          },
        },
      ],
    };
  },
  watch: {
    $route: {
      handler(val, oldval) {
        this.active = val.name;
      },
      // 深度观察监听
      deep: true,
    },
  },
  methods: {
    checkNav(item, index) {
      this.active = item.name;
      storage.ss.set("acrive", item.name);
      console.log("checkNav",item.path);
      this.$router.push(item.path);
      // this.$router.replace(item.path);
    },
  },
  created() {},
  mounted() {
    if (storage.ss.get("paddingBottom")) {
      document.querySelector(".van-tabbar--fixed").style.paddingBottom = storage.ss.get("paddingBottom");
    }
    this.active = this.$route.name;
  },
};
</script>
<style lang="scss" scoped>
.inactive-text {
  color: #ff9200;
}
.default_txt {
  color: #646566;
}

.van-hairline--top-bottom {
  background: #fafafa;
}
// 激活tabber背景
.van-tabbar-item--active {
  background: #fafafa;
}
</style>
