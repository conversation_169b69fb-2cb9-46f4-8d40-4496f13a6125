<template>
  <div class="container">
    <baidu-map v-if="oilStationList.length > 0" :center="oilStationList" :currentOilStation="oilStationList[0]" :pinch-to-zoom="true" />
    <div class="gnc" id="gasNav">
      <gnc class="gncCont" v-if="oilStationList" :oilStationList="oilStationList" />
    </div>
    <van-empty v-if="oilStationList.length == 0" description="未获取到可加油油站" />
  </div>
</template>

<script>
import baiduMap from "./component/baiduMap.vue";
import gnc from "./component/gas_name_comt.vue";
import { mapState } from "vuex";
import { storage } from "Utils/common/";
import gasStationMixin from "./mixins/mixin.js";
import { useGetLocation } from '@/utils/useGetLocation';

export default {
  name: "gasNav",
  mixins: [gasStationMixin],
  components: {
    gnc,
    baiduMap,
  },
  computed: {
    ...mapState(["rstParams", "deviceInfo"]),
  },
  props: {},
  data() {
    return {
      // app监测相关
      hiddenProperty: null,
      visibilityChangeEvent: null,
    };
  },
  watch: {},
  computed: {},
  created() {
    const deviceInfo = storage.ss.get("deviceInfo");
    // 判断是否有定位信息，没有则获取
    if (deviceInfo?.province || deviceInfo?.city || deviceInfo?.district) {
      this.GetNavigationList();
    } else {
      setTimeout(() => {
        this.$cppeiBridge("getLocation", { route: 'gasNav'});
      }, 500);
    }
  },
  mounted() {
    console.log("gasNav mounted");
    window.onGetLocation = this.onGetLocation;
    // 监听锁屏开屏，屏上屏下切换
    this.watchScreenSwitching(true);
  },
  destroyed() {
    console.log("gasNav destroyed");
    this.watchScreenSwitching(false)
  },
  methods: {
    // 锁屏开屏，屏上屏下切换激发操作
    onVisibilityChange() {
      // console.log(JSON.stringify(this.$route));
      if (!document[this.hiddenProperty]) {
        // this.$toast('导航页面激活');
        this.$cppeiBridge("getLocation", { route: 'gasNav'});
      } else {
        // this.$toast('导航页面隐藏');
      }
    },
    /**
     * @description: 监听锁屏开屏，屏上屏下切换
     * @return {*}
     */

    watchScreenSwitching(bool) {
      this.hiddenProperty = "hidden" in document ? "hidden" : "webkitHidden" in document ? "webkitHidden" : "mozHidden" in document ? "mozHidden" : null;
      this.visibilityChangeEvent = this.hiddenProperty.replace(/hidden/i, "visibilitychange");
      if(bool){
        document.addEventListener(this.visibilityChangeEvent, this.onVisibilityChange);
      }else{
        document.removeEventListener(this.visibilityChangeEvent, this.onVisibilityChange);
      }
    },

    // 定位信息
    onGetLocation(params) {
      useGetLocation(params, this.callBackFn);      
    },

    /**
     * @description: 定位回调具体页面处理逻辑
     * @param {*} params
     * @return {*}
     */
    callBackFn(bool){
      this.GetNavigationList();
    },
  },
};
</script>
<style lang="scss" scoped>
.container {
  .map {
    width: 100vw;
    height: 100vh;
  }
  .gnc {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 60px !important;
    margin: auto;
    bottom: 0;
    height: 60vh;
    box-sizing: border-box;
    overflow-y: auto;
    &::-webkit-scrollbar {
      display: none;
    }
    -webkit-overflow-scrolling: touch;
    .dragging {
      width: 100%;
      height: 40px;
      background: #ffffff;
      top: 0;
    }
    .gncCont {
      height: calc(60vh - 40px);
      overflow: scroll;
    }
  }
}
</style>
