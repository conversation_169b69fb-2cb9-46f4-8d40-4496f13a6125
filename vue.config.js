const path = require("path");
const BundleAnalyzerPlugin = require("webpack-bundle-analyzer").BundleAnalyzerPlugin;
const SkeletonWebpackPlugin = require("vue-skeleton-webpack-plugin");
const CompressionPlugin = require("compression-webpack-plugin");
const HardSourceWebpackPlugin = require("hard-source-webpack-plugin");
const routes = require("./src/skeleton/skeletonRoutes");

// const Mock = require("./src/mock/index");

// 处理路径
function resolve(dir) {
  return path.resolve(__dirname, dir);
}

// 具体可参考vuecli文档中的配置项
module.exports = {
  // 公共路径(vue-cli3.3+新版本使用)
  publicPath: process.env.BASE_URL, 

  // 'dist', 生产环境构建文件的目录
  outputDir: process.env.VUE_APP_BUILD_FOLDER,

  // 生成的静态资源在它们的文件名中包含了 hash 以便更好的控制缓
  filenameHashing: true,

  // 是否开启保存文件eslint代码 eslint掌握不熟 关闭了
  lintOnSave: false,

  // 生产环境是否生成 sourceMap 文件，一般情况不建议打开
  productionSourceMap: true,

  // 对内部的 webpack 配置进行更细粒度的修改
  chainWebpack: (config) => {
    // 添加别名
    config.resolve.alias
      .set("Asseets", resolve("src/assets"))
      // .set("Pages", resolve("src/views/Pages"))
      .set('Views', resolve('src/views'))
      .set("Images", resolve("src/assets/images"))
      .set("Components", resolve("src/components"))
      .set("Utils", resolve("src/utils"))
      .set("Api", resolve("src/api"))
      .set("Styles", resolve("src/assets/styles"));

    // 其他可考虑不需要 减少webpack搜索范围
    config.resolve.extensions
      .add(".js")
      .add(".vue")
      .add(".scss")
      .add(".json")
      .prepend(".js");

    // config.resolve.mainFields = 'main';

    // 告诉 webpack 解析模块去哪个目录找
    // config.resolve.modules = [path.resolve(__dirname,'node_modules')]

    config.module
      .rule("vue")
      .test(/\.vue$/)
      .use("style-vw-loader")
      .loader("style-vw-loader")
      .options({
        viewportWidth: 375,
        unitPrecision: 3,
      });
    

    // 如果使用多页面打包，使用vue inspect --plugins查看html是否在结果数组中
    config.plugin("html").tap((args) => {
      args[0].filename = "index.html";
      args[0].template = process.env.NODE_ENV === "production" ?  "public/index-prod.html" : "public/index-dev.html";
      return args;
    });
  },

  // 调整 webpack 配置
  configureWebpack: (config) => {
    // 骨架屏方案
    if (process.env.VUE_APP_ISSKELETON === "true") {
      config.plugins.push(
        new SkeletonWebpackPlugin({
          webpackConfig: {
            entry: {
              app: path.join(__dirname, "./src/skeleton/index.js"),
            },
          },
          minimize: true,
          quiet: true,
          router: {
            mode: "hash",
            routes,
          },
        })
      );
    }

    // 生产环境webpack配置
    if (process.env.NODE_ENV === "production") {
      config.mode = "production";

      // 是否开启打包分析
      if (process.env.VUE_APP_ISREPORTER === "true")
        config.plugins.push(new BundleAnalyzerPlugin());

      // 生产环境下是否配置gzip压缩
      if (process.env.VUE_APP_iSGZIP === "true") {
        config.plugins.push(
          new CompressionPlugin({
            filename: "[path].gz[query]",
            algorithm: "gzip",
            test: new RegExp("\\.(js|css)$"),
            // 只处理大于xx字节 的文件，默认：0
            threshold: 5120,
            // 示例：一个1024b大小的文件，压缩后大小为768b，minRatio : 0.75
            minRatio: 0.8, // 默认: 0.8
            // 是否删除源文件，默认: false
            deleteOriginalAssets: false,
          })
        );
      }

      // 默认去掉console.log debugger等冗余代码
      if (process.env.VUE_APP_ISREMOVECODE === "true") {
        config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true;
        config.optimization.minimizer[0].options.terserOptions.compress.drop_debugger = true;
        config.optimization.minimizer[0].options.terserOptions.compress.pure_funcs = [
          "console.log",
        ];
      }

      // 配置不加入打包队列-生产环境
      config.externals = {
        vue: "Vue",
        "vue-router": "VueRouter",
        vuex: "Vuex",
      };
    } else {
      // 开发环境下webpack配置
      config.mode = "development";
      config.devtool = "eval-source-map";

      // 开发环境启用缓存策略 二次启动从原来的8s 直接到1s
      // config.plugins.push(new HardSourceWebpackPlugin());
    }
  },

  css: {
    loaderOptions: {
      sass: {
        // 这里配置的时候 注意下sass-loader的版本 需要非v7.0
        prependData: `@import "src/assets/styles/_variable.scss";@import "src/assets/styles/_mixin.scss";`,
      },
    },
  },

  devServer: {
    // host: "**************",
    open: true, // 启动后是否自动打开浏览器页面
    port: process.env.VUE_APP_PORT, // 服务监听端口
    proxy: {
      "/Api": {
        // 跟axios中的baseUrl相关联
        target: process.env.VUE_APP_API_URL, // 指定代理到的地址 一般为后端接口地址
        changeOrigin: true, // 是否跨越
        pathRewrite: {
          "^/Api": "",
        },
        logLevel: "debug",
      },
    },
    // before(app) {
    //   if (process.env.VUE_APP_MOCK === "true") {
    //     console.log("mock数据启动成功");
    //     Mock(app);
    //   }
    // },
  },

  pwa: {},

  transpileDependencies: ["swiper", "dom7", "ssr-window"],
};