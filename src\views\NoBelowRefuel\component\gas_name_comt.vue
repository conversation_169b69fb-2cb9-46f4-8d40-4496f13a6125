<template>
  <!-- // yyz 是否显示营业中文字 // details 是否显示详细文字 -->
  <div class="oilList" id="oilList">
    <!-- <div style="width: 100%; height: 30px; background: red" id='gasNameComt' v-dragging:[id]="'oilList'"></div> -->
    <div class="list" id="gasNameComtList" v-for="(item, index) in oilStationList" :key="index">
      <div class="list_li">
        <logo />
        <div class="list_center">
          <div class="oil_name">
            <div class="name">{{ item.stationName }}</div>
            <div class="tag" v-if="yyz">
              <van-tag type="success">{{ item.status | oilStatus }}</van-tag>
            </div>
          </div>
          <div class="oil_distance">距离你{{ item.distance }}公里</div>
          <div class="icon_list">
            <img v-if="item.status == '2'" src="@/assets/images/gasstationmap/isOpen.png" alt="" />
            <img v-if="item.status != '2'" src="@/assets/images/gasstationmap/isClosed.png" alt="" />
            <img v-if="item.isCaChe" src="@/assets/images/gasstationmap/isCaChe.png" alt="" />
            <img v-if="item.isFood" src="@/assets/images/gasstationmap/isFood.png" alt="" />
            <img v-if="item.isFreeBoiledWater" src="@/assets/images/gasstationmap/isFreeBoiledWater.png" alt="" />
            <img v-if="item.isFreeWater" src="@/assets/images/gasstationmap/isFreeWater.png" alt="" />
            <img v-if="item.isSelfHelp" src="@/assets/images/gasstationmap/isSelfHelp.png" alt="" />
            <img v-if="item.isWC" src="@/assets/images/gasstationmap/isWC.png" alt="" />
            <img v-if="item.isWeixiuJiJiu" src="@/assets/images/gasstationmap/isWeixiuJiJiu.png" alt="" />
          </div>
          <div></div>
        </div>
        <div
          class="list_go_detil"
          v-if="details"
          @click="
            $router.push({
              path: '/gasDetails',
              query: { list: JSON.stringify(item) },
            })
          "
        >
          详情 >
        </div>
      </div>
      <div class="list_btn">
        <van-button type="warning" class="go_oil" size="small" @click="goOiller(item)">加油</van-button>
        <van-button type="default" class="go_pos" size="small" @click="goMapNav(item)">到这里</van-button>
      </div>
    </div>
  </div>
</template>

<script>
import eventBus from "../_service/eventbus.js";

import { mapState } from "vuex";
import { storage } from "Utils/common";
import logo from "@/components/logo";
import { recordJsLog } from "@/utils/recordJsLog";

export default {
  name: "",
  components: {
    logo,
  },
  props: {
    yyz: {
      type: Boolean,
      default: true,
    },
    details: {
      type: Boolean,
      default: true,
    },
    oilStationList: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {};
  },
  watch: {},
  computed: {
    ...mapState(["deviceInfo"]),
  },
  methods: {
    goMapNav(item) {
      recordJsLog('startNavi', {
        // startLon: this.deviceInfo.lon,
        // startLat: this.deviceInfo.lat,
        startLon: storage.ss.get("deviceInfo").lon,
        startLat: storage.ss.get("deviceInfo").lat,
        endLon: Number(item.posx),
        endLat: Number(item.posy),
      });

      // alert("准备唤起百度导航" + item.posx + "   " + item.posy);
      this.$cppeiBridge("startNavi", {
        // startLon: this.deviceInfo.lon,
        // startLat: this.deviceInfo.lat,
        startLon: storage.ss.get("deviceInfo").lon,
        startLat: storage.ss.get("deviceInfo").lat,
        endLon: Number(item.posx),
        endLat: Number(item.posy),
      });
    },
    goOiller(item) {
      console.log('currentOilNo106');
      eventBus.$emit("currentOilNo", item);
      //调用router回退页面
      this.$router.push({
        path: "/noBelowRefuel",
        query: "",
      });
    },
  },
  created() {},
  mounted() {},
};
</script>
<style lang="scss" scoped>
.oilList {
  background: #ffffff;
  overflow: hidden;
  .list {
    margin: 40px auto;

    width: 90%;
    .list_li {
      width: 100%;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      .list_center {
        margin-left: 20px;
        flex: 1;
        text-align: left;
        .oil_name {
          color: #333333;
          font-size: 32px;
          margin-bottom: 10px;
          display: flex;
          width: 60vw;
          justify-content: flex-start;
          align-items: center;
          .name {
            max-width: 44vw;
          }
          .tag {
            margin-left: 2vw;
            width: 13vw;
          }
        }
        .oil_distance {
          color: #666666;
          font-size: 26px;
        }
        .icon_list {
          margin-top: 20px;
          img {
            display: inline-block;
            margin: 5px;
            width: 50px;
            height: 50px;
            &:nth-child(1) {
              margin-left: 0;
            }
          }
        }
      }
      .list_go_detil {
        align-self: center;
        color: #ff9200;
        font-size: 36px;
      }
    }
    .list_btn {
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
      width: 100%;
      button {
        width: 48%;
        border-radius: 20px;
        font-size: 30px;
      }
    }
  }
}
</style>
