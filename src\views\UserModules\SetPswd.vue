<template>
  <div class="login-code">
    <nav-bar title="设置登录密码" />
    <!-- 顶部图片 -->
    <!-- <div class="header">
      <img src="@/assets/images/login/bg_setPswd.png" />
    </div> -->
    <!-- 登录页 -->
    <div class="login-card">
      <van-form ref="form" @submit="onSubmit" validate-first label-width="100" :show-error-message="false" :show-error="false">
        <van-field v-model="password" label="请输入登录密码" clearable type="password" placeholder="6-20位字母数字组合" maxlength="20" center :rules="[{ validator: validatePwd, trigger: 'onBlur' }]">
          <template slot="left-icon">
            <img src="@/assets/images/icon/mima.png" class="icon" />
          </template>
        </van-field>
        <van-field v-model="passwordCheck" label="请确认登录密码" clearable type="password" placeholder="请确认密码" maxlength="20" center :rules="[{ validator: validateSame, trigger: 'onBlur' }]">
          <template slot="left-icon">
            <img src="@/assets/images/icon/mima.png" class="icon" />
          </template>
        </van-field>
        <!-- 错误提示 -->
        <div class="err-tips">{{ errorMsg }}</div>
      </van-form>
    </div>
    <!-- 完成按钮 -->
    <div class="btn-wrap">
      <van-button block round :color="$setting.themeColor" size="large" @click="doSetPswd" :disabled="isDisabled">完&nbsp;成</van-button>
    </div>
  </div>
</template>

<script>
import navBar from "@/components/navBar/NavHeader";
import { CellGroup, Toast } from "vant";
// 后端接口
import { updatePswd } from "./_service/";
import { storage } from "Utils/common/";
import md5 from "js-md5";

export default {
  props: {},
  components: { navBar },
  data() {
    return {
      // 表单数据
      user: {
        phone: storage.ss.get("phone"),
        password: "",
        // 1 单位 2司机
        type: this.$setting.APP_TYPE,
      },
      password: "",
      // 密码二次确认
      passwordCheck: "",
      // 默认选中
      checked: true,
      // 错误提示
      errorMsg: "",
    };
  },
  computed: {
    isDisabled() {
      return this.password && this.passwordCheck ? false : true;
    },
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    /**
     * @description: 表单校验
     * @param {*} val
     * @return {*}
     */

    validatePwd(val) {
      // console.log(88888);
      if (!val) {
        this.errorMsg = "*密码不能为空";
        return false;
      } else {
        let reg = /^(?!\D+$)(?![^a-zA-Z]+$)\S{6,20}$/;
        this.errorMsg = reg.test(val) ? "" : "*请填写6-20位字母数字组合";
        return reg.test(val);
      }
    },
    validateSame(val) {
      if (val != this.password) {
        this.errorMsg = "*两次密码输入不一致";
        return false;
      } else {
        this.errorMsg = "";
        return true;
      }
    },
    doSetPswd() {
      this.$refs.form.submit();
    },
    onSubmit() {
      if (storage.ss.get("isForget")) {
        // 忘记密码
        this.user.phone = storage.ss.get("setPhone");
      }
      this.user.password = md5(this.password);
      updatePswd(this.user)
        .then((res) => {
          if (1 === res.infoCode) {
            Toast("登录密码设置成功");
            var flag = storage.ss.get("isForget");
            if (flag) {
              setTimeout(() => {
                // 跳到登录页
                this.$router.replace({
                  path: "/loginCenter",
                });
              }, 1200);
            } else {
              setTimeout(() => {
                // 跳到实名认证
                this.$router.replace({
                  path: "/identityCardOCR",
                });
              }, 1200);
            }
          } else {
            Toast(res.info);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>

<style scoped lang="scss">
.login-code {
  .header {
    img {
      width: 100%;
    }
  }
  .login-card {
    height: 33vw;
    background: #fff;
    margin: 0 20px;
    margin-top: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    .other {
      text-align: right;
      color: #22a1d7;
      cursor: pointer;
      margin-right: 16px;
      line-height: 40px;
    }
    .icon {
      width: 27.5px;
      height: 29.5px;
      vertical-align: sub;
    }
    .err-tips {
      color: #ee0a24;
      // line-height: 1.4;
      padding: 20px 30px 0;
      text-align: left;
      min-height: 20px;
      font-size: 24px;
    }
    /deep/ .van-cell {
      background: none;
    }
  }
  .btn-wrap {
    padding: 0 20px;
    margin: 0 20px;
    margin-top: 50px;
    // position: relative;
    .van-button--disabled {
      // 按钮透明度
      // opacity: 1;
    }
  }
  .check-box {
    margin-top: 80px;
    display: flex;
    justify-content: center;
    .text {
      color: #f37f06;
      cursor: pointer;
    }
  }
}
</style>
