<template>
  <div class="order_pay_result">
    <div class="title">消费账单</div>
    <div class="stationName">—— 中国石油 {{tradeOrderMix.stationName}} ——</div>
    <div class="notice">请向加油员出示账单</div>
    <div class="orderDetail">
      <div class="pay_title">实付金额</div>
      <div class="pay_money">￥<b>{{tradeOrderMix.realAmt}}</b></div>
      <!-- <div class="time">
        <em>{{hour}}</em> :
        <em>{{minute}}</em> :
        <em>{{second}}</em> :
        <em class="millisecond">{{millisecond}}</em>
      </div> -->
      <van-cell-group>
        <van-cell title="订单金额" :value="'￥'+tradeOrderMix.totalAmt+'元'" />
        <van-cell title="折扣金额" v-if="tradeOrderMix.cardDiscAmt*1 > 0" :value="'￥'+tradeOrderMix.cardDiscAmt+'元'" class="discount" />
        <van-cell title="支付方式" value="移动支付" />
        <van-cell title="支付时间" :value="tradeOrderMix.tradeTime" />
        <van-cell title="订单编号" :value="tradeOrderMix.orderNo" />
        <van-cell title="油枪号" :value="tradeOrderMix.oilGunNo + '号枪'" />
        <!-- <van-cell title="单价" v-if="tradeOrderMix.oilPrice*1>0" :value="'￥'+tradeOrderMix.oilPrice + (tradeOrderMix.oilName.indexOf('氢气') != -1?'/千克':'/升')" /> -->
        <!-- <van-cell title="单价" v-if="tradeOrderMix.unitPrice*1>0" :value="'￥'+tradeOrderMix.unitPrice + '/升'" /> -->
        <van-cell title="数量" v-if="tradeOrderMix.oilQty*1>0" :value="tradeOrderMix.oilQty+(tradeOrderMix.oilName.indexOf('氢气') != -1?'千克':'升')" />
        <!-- 三种优惠 promotionAmt促销金额 couponAmt券金额  cardDiscAmt折扣金额  -->
        <van-cell title="券金额" v-if="tradeOrderMix.couponAmt*1 > 0" :value="'￥'+tradeOrderMix.couponAmt+'元'" class="discount" />
        <van-cell title="促销金额" v-if="tradeOrderMix.promotionAmt*1 > 0" :value="'￥'+tradeOrderMix.promotionAmt+'元'" class="discount" />
      </van-cell-group>
      <van-button type="primary" size="large" @click="goBackhome">确认并返回首页</van-button>
    </div>
  </div>
</template>
<script>
import eventBus from "@/views/NoBelowRefuel/_service/eventbus.js";
import { payRiskManagementFinish } from "Utils/payRiskManagementFinish";
import { mapState, mapMutations } from "vuex";
export default {
  data() {
    return {
      hour: 0,
      minute: 0,
      second: 0,
      millisecond: 0,
      timer: '',
      tradeOrderMix: {},
      OilStationinfo:{}, //油站所有信息
    }
  },
  created() {
    this.Init();
  },
  activated() {},
  methods: {
    ...mapMutations(['rightSlide_Fn']),
    // 进入页面初始化
    Init() {
      // 获取当前时间
      // this.hour = new Date().getHours()<10 ? '0'+new Date().getHours() : new Date().getHours();
      // this.minute = new Date().getMinutes()<10 ? '0'+new Date().getMinutes() : new Date().getMinutes();
      // this.second = new Date().getSeconds()<10 ? '0'+new Date().getSeconds() : new Date().getSeconds();
      // this.millisecond = new Date().getMilliseconds();
      // if(this.millisecond < 10 ) { this.millisecond = '00'+this.millisecond; }
      // if(this.millisecond < 100 && this.millisecond > 10) { this.millisecond = '0'+this.millisecond; }
      // this.timer = setInterval(this.countTimer,51);
      // 获取路由所传参数
      this.tradeOrderMix = JSON.parse(this.$route.query.tradeOrderMix);
      this.OilStationinfo = JSON.parse(this.$route.query.OilStationinfo);
      console.log(this.tradeOrderMix);
      console.log(this.OilStationinfo,'OilStationinfo65');
      console.log(this.tradeOrderMix?.orderNo,"this.tradeOrderMix?.orderNo");
      // try {
      //   payRiskManagementFinish(this.tradeOrderMix?.orderNo, 0, 3);
      // } catch (error) {
      //   // 这里处理调用 $http 方法之前的错误，比如参数校验失败等
      //   // 可以在这里显示一个全局错误提示或者记录到日志
      //   console.error("在调用支付风险管理请求前发生错误", error);
      // }
    },
    // 计时
    countTimer() {
      this.millisecond = this.millisecond*1 + 51;
      if(this.millisecond > 1000) {
        this.millisecond = this.millisecond - 1000;
        this.second = this.second*1 + 1;
      }
      if(this.second >= 60) {
        this.second = 0;
        this.minute = this.minute*1 + 1;
      }
      if(this.minute >= 60) {
        this.minute = 0;
        this.hour = this.hour*1 + 1;
      }
      this.millisecond = this.millisecond*1 < 10 ? '00' + this.millisecond*1 : this.millisecond;
      this.millisecond = this.millisecond*1 < 100 && this.millisecond*1 > 10 ? '0' + this.millisecond*1 : this.millisecond;
      this.second = this.second*1 < 10 ? '0' + this.second*1 : this.second;
      this.minute = this.minute*1 < 10 ? '0' + this.minute*1 : this.minute;
    },
    goBackhome() {
      this.rightSlide_Fn(true)
      this.$router.push({
        path: "/noBelowRefuel",
        query: {
          OilStationinfo: this.OilStationinfo,
        },
      });
      // console.log("currentOilNo89");
      // eventBus.$emit("currentOilPos", this.OilStationinfo);
      // clearInterval(this.timer)
      // this.timer = -1;
    }
  },
  destroyed() {
    clearInterval(this.timer)
    this.timer = -1;
  }
}
</script>
<style lang="scss" scoped>
.order_pay_result {
  // background: url("../../../assets/images/common/my_bg.png") no-repeat top center;
  background: url("~@/assets/images/winter/my_bg.png") no-repeat top center;
  background-size: contain;
  .title {
    margin-top: 26vw;
    color: #fff;
  }
  .stationName {
    margin-top: 6vw;
    color: #fff;
  }
  .notice {
    margin-top: 3vw;
    color: #fff;
    font-weight: bold;
    font-style: italic;
    letter-spacing: 2px;
    font-size: 1.8em;
    text-shadow: #fff 0px 0px 10px;
  }
  .orderDetail {
    margin: 4vw 3vw 0;
    background: #fff;
    border-radius: 3vw;
    @include useScrollByBrowser(0);
    .pay_title {padding-top: 6vw;}
    .pay_money {
      padding-top: 2vw;
      b { font-size: 2em; }
    }
    .time {
      padding: 3vw 0 4vw;
      em {
        font-style: normal;
        border: 1px solid #ff9200;
        padding: 1vw 2vw;
        border-radius: 1.5vw;
        background: #FFEBCD;
        
        &.millisecond {
          width: 7vw;
          display: inline-block;
        }
      }
    }
    .van-cell {
      .van-cell__title {
        color: #969799;
        text-align: left;
      }
      .van-cell__value {
        color: #323233;
        min-width: 70%;
        span {
          display: inline-block;
          text-align: left;
          word-break: break-all;
        }
      }
      &.discount {
        .van-cell__value { color: #ff9200; }
      }
    }
    .van-button--large {
      background-color: #ff9200;
      border: 1px solid #ff9200;
      margin: 4vw 0;
      width: 90%;
      border-radius: 2vw;
      height: 40px;
    }
  }
}
</style>