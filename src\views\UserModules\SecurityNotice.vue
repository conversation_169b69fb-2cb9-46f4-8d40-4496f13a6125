<template>
  <div class="safe_notice">
    <nav-bar title="安全验证" />
    <van-image :src="require('@/assets/images/login/safe.png')" width="90" style="margin-right: 8px;">
      <template v-slot:error>加载失败</template>
    </van-image>
    <div class="text">
      <p>安全验证</p>
      <em>您正在一台新设备登录</em>
      <em>为了您的账号安全，请进行安全验证</em>
    </div>
    <van-button size="large" round @click="goTocheck">开始验证</van-button>
  </div>
</template>
<script>
import navBar from "@/components/navBar/NavHeader";
export default {
  components: { navBar },
  data() {
    return {};
  },
  methods: {
    goTocheck() {
      this.$router.push({ name: "safeChecking" });
    },
  },
};
</script>
<style lang="scss" scoped>
.safe_notice {
  .van-image {
    margin-top: 20vw;
  }
  .text {
    p {
      font-size: 1rem;
    }
    em {
      display: block;
      margin-bottom: 1vw;
      font-style: normal;
    }
  }
  .van-button {
    background: #ff9200;
    color: #fff;
    margin-top: 6vw;
    width: 92vw;
  }
}
</style>
