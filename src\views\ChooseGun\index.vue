<template>
  <div class="page-choose-gun">
    <baidu-map class="bm-view" :center="center" :zoom="zoom" @ready="handlerMap" ak="oOYYVuVO2GKi0Clafd1VfWME">
    </baidu-map>
    <div class="gas-tip">
      <i class="gas-tip-icon"></i>
      <span class="gas-tip-content">请在车内操作手机，站内严禁接打电话！</span>
    </div>
    <div class="gas-station">
      <div class="gas-station-label">当前加油站</div>
      <div class="gas-station-content">
        <img class="gas-station-logo" src="@/assets/images/OrderList/logo.png" alt="">
        <div class="gas-station-name">{{ oilInfo.StationName }}</div>
        <div class="gas-station-edit" @click="chooseStation">更换加油站</div>
      </div>
    </div>
    <div class="gas-gun">
      <div class="gas-gun-title">请选择您加油使用的油枪（建议咨询工作人员）</div>
      <div class="gas-gun-list">
        <span class="gas-gun-item" :class="{active : gunIndex===v.value}" @click="chooseGun(v.value)"
              v-for="(v,k) in gunList" :key="k">{{ v.name }}</span>
      </div>
      <div class="gas-gun-item-other" :class="{active : gunOther!==''}" @click="chooseGunOther">其他枪号</div>
    </div>
    <van-popup
        v-model="gunOtherShow"
        position="bottom"
        :style="{ height: 'auto' }"
    >
      <div class="gas-gun-other-input">
        <van-field v-model="gunOther" type="digit">
          <template #button>
            <span class="gas-gun-other-input__label">号枪</span>
            <van-button size="small" @click="goGas(gunOther)" type="warning">确定</van-button>
          </template>
        </van-field>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { Dialog, Toast } from "vant"
import BaiduMap from "vue-baidu-map/components/map/Map.vue"
import { storage, GetQueryString } from "Utils/common";
import { GetStationInfos } from "Views/StationList/_service";

// 暂不接微信sdk
// import wx from 'weixin-js-sdk';

export default {
  props: {},
  components: {
    BaiduMap
  },
  data() {
    return {
      center: {lng: 0, lat: 0},
      zoom: 15,
      gunList: [],
      gunIndex: '',
      gunOther: '',
      gunOtherShow: false,
      oilInfo: storage.ss.get('oilInfo') || {StationName: '', OliStationCode: ''},
    };
  },
  computed: {},
  created() {
    let openid = decodeURIComponent(GetQueryString('openid'))

    console.log('openid: ' + openid)
    storage.ls.set("openid", openid || storage.ls.get('openid'))
  },
  mounted() {
    for (let i = 0; i < 15; i++) {
      this.gunList.push({name: (i + 1) + '号', value: i + 1});
    }
  },
  watch: {
    $route(to, from) {
      this.getOilInfo()
    }
  },
  methods: {
    getOilInfo() {
      this.oilInfo = storage.ss.get('oilInfo') || this.oilInfo
    },
    handlerMap({BMap, map}) {
      // this.center.lng = 116.404
      // this.center.lat = 39.915
      // this.zoom = 15
      // if (this.$route.query.oilstationcode) return
      let geolocation = new BMap.Geolocation();
      let self = this;
      geolocation.getCurrentPosition(function (r) {
        if (this.getStatus() === "BMAP_STATUS_SUCCESS") {
          let allOverlay = map.getOverlays()
          if (r.accuracy == null) return Toast("无法获取您的定位，已为您选择默认城市。", "提示");
          for (let i = 0; i < allOverlay.length; i++) {
            map.removeOverlay(allOverlay[i]);
          }
        } else {
          // TODO: 0129 暂时注释提示
          // Toast("无法获取您的定位，已为您选择默认城市。");
        }
        let posx = r.point.lng;
        let posy = r.point.lat;
        self.center.lng = posx;
        self.center.lat = posy;
        storage.ss.set('gps', {lng: posx, lat: posy})
        let point = new BMap.Point(posx, posy);
        map.centerAndZoom(point, 15);
        map.enableScrollWheelZoom();
        let marker = new BMap.Marker(point);
        map.addOverlay(marker);
        map.panTo(point);
        marker.enableDragging();
        if (storage.ss.get('oilInfo')) return
        self.GetStationList()
      });
    },
    RegisterWXMap(data) {
      this.$wechat.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: data.appId, // 必填，公众号的唯一标识
        timestamp: data.timestamp, // 必填，生成签名的时间戳
        nonceStr: data.nonceStr, // 必填，生成签名的随机串
        signature: data.signature,// 必填，签名，见附录1
        jsApiList: [
          'checkJsApi',
          'getLocation',
          'openLocation'
        ]
      });
      wx.checkJsApi({
        jsApiList: ['getLocation'],
        success: function (res) {
          if (!res.checkResult.getLocation) Toast('你的微信版本太低，不支持微信JS接口，请升级到最新的微信版本！');
        },
        fail: function (res) {
          console.info('checkJsApi fail=' + JSON.stringify(res))
        }
      });
      wx.ready(function () {
        wx.getLocation({
          type: "wgs84", // gcj02 火星坐标
          success: function (res) {
            const latitude = res.latitude; // 纬度，浮点数，范围为90 ~ -90
            const longitude = res.longitude; // 经度，浮点数，范围为180 ~ -180。
            const speed = res.speed; // 速度，以米/每秒计
            const accuracy = res.accuracy; // 位置精度
            console.log(res)
          },
          cancel: function (res) {
            Toast('查看附件机器，首先要授权位置信息')
          },
          fail: function (res) {
            console.info('ready getLocation fail=' + JSON.stringify(res))
            Toast('出现故障，请联系管理员')
          }
        });
      })
    },
    chooseGun(v) {

      this.gunOther = '';
      this.gunIndex = v;
      this.goGas(this.gunIndex);

    },
    chooseGunOther() {
      this.gunIndex = '';
      this.gunOtherShow = true;
    },
    goGas(oilgunno) {
      console.log('oilgunno: ' + oilgunno)

      if (oilgunno === undefined || oilgunno === '' || oilgunno === ' ') {
        Dialog.alert({
          title: '提示',
          message: '油枪号不能为空'
        })
      } else {
        console.log('distance: ' + this.oilInfo.Distance)

        // 放开1公里限制
        // this.$router.push({path: '/orderlist', query: {oilgunno, oilstationcode: this.oilInfo.OliStationCode}});

        // 1公里限制
        if (this.oilInfo && this.oilInfo.Distance !== undefined && this.oilInfo.Distance !== '' && this.oilInfo.Distance < 1) {
          console.log('IN I KM')
          this.$router.push({path: '/orderlist', query: {oilgunno, oilstationcode: this.oilInfo.OliStationCode}});
        } else {
          Dialog.alert({
            title: '提示',
            message: '距离太远，请到油站内使用'
          })
        }
      }
    },
    chooseStation() {
      this.$router.replace({path: '/stationList', query: {lng: this.center.lng, lat: this.center.lat}});
    },
    GetStationList() {
      GetStationInfos({
        "posx": this.center.lng,
        "posy": this.center.lat,
        "distance": 20,
        "type": 0, // 一般网点 0 ，发卡网点 1
        "pageindex": 1,
        "pagesize": 5,
        "cityCode": "1",
      }).then(data => {
        if (data.InfoCode === 1) {
          if (data.Data.GasStationList.length === 0) {
            return Dialog.alert({
              title: "提示",
              message: "附近暂无加油站~"
            })
          }
          this.oilInfo = data.Data.GasStationList[0]
          storage.ss.set('oilInfo', this.oilInfo)
        } else {
          Toast(data.Info)
        }
      }).catch(err => {
        Toast("网络请求失败")
      })
    },
  },
};
</script>
<style>
iframe {
  display: inherit;
}
</style>

<style scoped lang="scss">

.bm-view {
  width: 100%;
  //height: 300px;
}

.page-choose-gun {
  background-color: #f5f5f5 !important;
}

.gas-tip {
  text-align: left;
  margin: 10px 12px;
  padding: 9px 12.5px;
  background: #FFFBE6;
  border: 1px solid #FFE58F;
  border-radius: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  @include ftbc;

  &-icon {
    width: 13px;
    height: 13px;
    display: inline-block;
    margin-right: 8px;
    background: url("~@/assets/images/OrderList/tips.png");
    background-size: 100%;
  }
}

.gas-station {
  padding: 12px 12px 30px 12px;
  color: #fff;
  margin-bottom: 10px;
  background: url("~@/assets/images/OrderList/background-card-1.png") center center no-repeat;
  background-size: 100% 100%;

  &-label {
    text-align: left;
    font-size: 12px;
    margin-bottom: 10px;
    color: rgba(255, 255, 255, .8);
  }

  &-content {
    @include flb;
  }

  &-logo {
    width: 34px;
    height: 34px;
    display: inline-block;
    margin-right: 10px;
    border-radius: 50%;
    overflow: hidden;
  }

  &-name {
    font-size: 18px;
    text-align: left;
    flex: 1;
    line-height: 1.5;
    @include ellipsis(52px, 2);
  }

  &-edit {
    font-size: 12px;
    background: #fff;
    color: #666;
    box-shadow: inset 0 -1px 3px 1px rgba(255, 127, 65, 0.50);
    border-radius: 24.5px;
    margin-left: 48px;
    padding: 6px 10px;

    @include bgActive(#eee);

    &:active {
      color: #000;
      box-shadow: inset 0 -1px 3px 1px rgba(255, 81, 52, 0.5);
    }
  }
}

.gas-gun {
  padding: 15px 12px 50px;
  background-color: #fff;

  &-title {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    text-align: left;
    margin-bottom: 18px;
  }

  &-list {
    display: flex;
    flex-wrap: wrap;
  }

  &-item {
    width: 109px;
    font-size: 14px;
    color: #333;
    display: inline-block;
    padding: 10px 0;
    background-color: #f5f5f5;
    margin-right: 12px;
    margin-bottom: 12px;
    border-radius: 4px;

    &.active {
      background: url("~@/assets/images/chooseGun/selected.png") right bottom no-repeat;
      background-size: 20px;
      background-color: #FFD9C3;
      color: #FF7727;
    }

    &:nth-child(3n) {
      margin-right: 0;
    }

    &-other {
      background-color: #f5f5f5;
      font-size: 15px;
      padding: 9px 0;
      border-radius: 4px;
      margin-top: 5px;

      &.active {
        background: url("~@/assets/images/chooseGun/selected.png") right bottom no-repeat;
        background-size: 20px;
        background-color: #FFD9C3;
        color: #FF7727;
      }
    }
  }
}

.gas-gun-other-input__label {
  margin: 0 10px;
  vertical-align: sub;
}
</style>
