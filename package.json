{"name": "fleetcard_driver_vue2", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build-test": "vue-cli-service build --mode staging", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "start": "nodemon --watch ./src/mock --exec \"vue-cli-service serve\"", "deploy:dev": "npm run build && cross-env NODE_ENV=dev node ./deploy", "deploy:prod": "npm run build && cross-env NODE_ENV=prod node ./deploy"}, "dependencies": {"axios": "^0.19.2", "better-scroll": "^1.15.2", "core-js": "^3.6.4", "cssnano": "^4.1.10", "exif-js": "^2.3.0", "js-big-decimal": "^1.2.2", "lrz": "^4.9.41", "moment": "^2.29.1", "postcss-aspect-ratio-mini": "^1.0.1", "postcss-cssnext": "^3.1.0", "postcss-px-to-viewport": "^1.1.1", "postcss-viewport-units": "^0.1.6", "qs": "^6.9.1", "register-service-worker": "^1.6.2", "touch": "^3.1.0", "vant": "^2.12.48", "vconsole": "^3.4.0", "vue": "^2.6.11", "vue-baidu-map": "^0.21.22", "vue-barcode": "^1.3.0", "vue-lazyload": "^1.3.3", "vue-ls": "^4.2.0", "vue-router": "^3.1.5", "vue-wechat-title": "^2.0.5", "vuex": "^3.1.2"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.2.0", "@vue/cli-plugin-pwa": "^4.2.0", "@vue/cli-plugin-router": "^4.2.0", "@vue/cli-plugin-vuex": "^4.2.0", "@vue/cli-service": "^4.2.0", "babel-plugin-import": "^1.13.0", "compression-webpack-plugin": "^3.1.0", "cross-env": "^7.0.3", "cssnano-preset-advanced": "^4.0.7", "hard-source-webpack-plugin": "^0.13.1", "js-md5": "^0.7.3", "mockjs": "^1.1.0", "node-sass": "^4.12.0", "nodemon": "^2.0.2", "qrcode": "^1.4.4", "sass-loader": "^8.0.2", "scp2": "^0.5.0", "style-vw-loader": "^1.0.2", "vue-skeleton-webpack-plugin": "^1.2.2", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^3.7.0"}}