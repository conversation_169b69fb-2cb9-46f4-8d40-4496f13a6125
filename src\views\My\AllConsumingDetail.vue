<template>
  <div class="order_pay_result">
    <div class="title">消费明细</div>
    <div class="stationName">—— 中国石油 {{ recordDetail.gasStationName }} ——</div>
    <div class="orderDetail">
      <div class="pay_title">实付金额</div>
      <div class="pay_money">
        ￥<b>{{ recordDetail.realAmount }}</b>
      </div>
      <van-cell-group>
        <van-cell title="订单号" :value="recordDetail.orderNo" />
        <!-- 纯油消费 -->
        <van-cell title="商品" v-if="recordDetail.oilName" :value="recordDetail.oilName" />
        <van-cell title="油枪号" v-if="recordDetail.oilGunNo" :value="recordDetail.oilGunNo+'号'" />
        <template v-if="recordDetail.oilName">
          <van-cell title="单价" v-if="recordDetail.oilPrice * 1 > 0" :value="'￥' + recordDetail.oilPrice + (recordDetail.oilName.indexOf('氢气') != -1 ? '/千克' : '/升')" />
          <van-cell title="数量" v-if="recordDetail.oilQuantity * 1 > 0" :value="recordDetail.oilQuantity + (recordDetail.oilName.indexOf('氢气') != -1 ? '千克' : '升')" />
        </template>
        <!-- tradeType 1 -混合交易（油+非油）2油品交易 3非油交易,whereType 1预约加油 2室内支付 3 不下车加油    -->
        <van-cell title="商品明细" v-if="recordDetail.tradeType != 2 && recordDetail.whereType == 2" value="查看明细" is-link @click="goCommodityDetail" />
        <!-- <van-cell title="商品明细" value="查看明细" is-link @click="goCommodityDetail" /> -->
        <!-- 共有 -->
        <van-cell title="订单金额" :value="'￥' + recordDetail.orderAmount + '元'" />
        <van-cell title="折扣金额" v-if="recordDetail.cardDiscountAmount * 1 > 0" :value="'￥' + recordDetail.cardDiscountAmount + '元'" class="discount" />
        <van-cell title="支付方式" :value="recordDetail.tradeModel == 10 ? '加油卡':'移动支付'" />
        <van-cell title="支付时间" :value="recordDetail.paymentTime" />
        <!-- 油品+非油品消费 -->
        <van-cell title="车牌号" v-if="recordDetail.plateNo" :value="recordDetail.plateNo" />
      </van-cell-group>
      <van-button type="primary" size="large" @click="goBackConsume">确认并返回</van-button>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      recordDetail: {},
    };
  },
  created() {
    this.Init();
  },
  activated() {},
  methods: {
    // 进入页面初始化
    Init() {
      // 获取路由所传参数
      this.recordDetail = JSON.parse(this.$route.query.recordDetail);
    },
    goBackConsume() {
      this.$router.push({ path: "/my/allConsumingRecords" });
    },
    goCommodityDetail() {
      this.$router.push({ path: "/my/AllcommodityDetail", query: { recordDetail: JSON.stringify(this.recordDetail) } });
    },
  },
};
</script>
<style lang="scss" scoped>
.order_pay_result {
  // background: url("../../../assets/images/common/my_bg.png") no-repeat top center;
  background: url(~Images/winter/my_bg.png) no-repeat top center;
  background-size: contain;
  .title {
    margin-top: 20vw;
    color: #fff;
    font-size: 4vw;
  }
  .stationName {
    margin-top: 6vw;
    color: #fff;
  }
  .notice {
    margin-top: 3vw;
    color: #fff;
    font-weight: bold;
    font-style: italic;
    letter-spacing: 2px;
    font-size: 1.8em;
    text-shadow: #fff 0px 0px 10px;
  }
  .orderDetail {
    margin: 4vw 3vw 0;
    background: #fff;
    border-radius: 3vw;
    @include useScrollByBrowser(0);
    max-height: 82vh;
    .pay_title {
      padding-top: 6vw;
    }
    .pay_money {
      padding: 2vw;
      b {
        font-size: 2em;
      }
    }
    .van-cell {
      .van-cell__title {
        color: #969799;
        text-align: left;
      }
      .van-cell__value {
        color: #323233;
        min-width: 70%;
        span {
          display: inline-block;
          text-align: left;
          word-break: break-all;
        }
      }
      &.discount {
        .van-cell__value {
          color: #ff9200;
        }
      }
    }
    .van-button--large {
      background-color: #ff9200;
      border: 1px solid #ff9200;
      margin: 4vw 0;
      width: 90%;
      border-radius: 2vw;
      height: 40px;
    }
  }
}
</style>
