<template>
  <div class="recharge_agreement">
    <nav-bar title="网上充值协议" />
    <div class="title">
      <p>中国石油昆仑加油卡客户网上充值协议</p>
    </div>
    <div class="content">
      <div class="content-item">
        <p class="content-2-level">
          欢迎阅读中国石油昆仑加油卡客户网上充值协议（以下简称“本协议”），本协议是您与中油车队端APP(以下简称“本APP”)所有者之间就中国石油昆仑加油电子卡卡网上充值服务（以下简称“网上充值服务”）等相关事宜所订立的协议，请您仔细阅读本协议，您勾选“我已阅读并接受《中国石油昆仑加油电子卡客户网上充值协议》”复选框后，本协议即构成对双方有约束力的法律文件。
        </p>
      </div>
      <div class="content-item">
        <p class="content-1-level">1 协议的确认和接纳</p>
        <p class="content-2-level">
          1.1本APP的网上充值服务由中国石油天然气股份公司（以下简称“中国石油”）进行管理和运作。协议条款是处理双方权利义务的协议，始终有效，法律另有强制性规定或双方另有特别约定的除外。
        </p>
        <p class="content-2-level">
          1.2 您勾选充值页面下“我已阅读并接受《中国石油昆仑加油电子卡客户网上充值协议》”复选框后，即视为您已阅读、了解并完全同意本协议的各项内容，并具有独立承担法律责任的能力。
        </p>
        <p class="content-2-level">
          1.3 中国石油在必要时可修改本协议，并在本APP进行公告，即公告之日起生效。如您继续使用网上充值服务，则视为您已接受修订的充值协议。
        </p>
        <p class="content-2-level">
          1.4 在网络安全，中国石油依法保留拒绝服务、关闭网上注册账户、清除或编辑内容及取消订单的权利。（拒绝服务的情况，风险，网络安全）
        </p>
      </div>
      <div class="content-item">
        <p class="content-1-level">2 服务内容</p>
        <p class="content-2-level">
          2.1 中国石油通过互联网依法为用户提供线上充值、线下充值、订单查询等服务，用户在完全同意本协议及本APP规定的情况下，方有权使用本APP的网上充值服务。
        </p>
        <p class="content-2-level">
          2.2 网上充值服务仅支持中国石油发行的个人记名卡和单位卡。
        </p>
        <p class="content-2-level">
          2.3 个人记名卡需完成本APP注册，即可启用网上充值服务；单位卡需完成与本单位的管理卡绑定后，进行本APP的登录，即可启用网上充值服务。
        </p>
        <p class="content-2-level">
          2.4 线下充值完成后请您携带昆仑加油卡到中国石油任意发卡充值网点，将网上充值资金圈存到加油卡中，即可使用充值资金。线上充值直接充值到卡片备用金中使用，无需圈存，充值后即可线上使用充值资金。
        </p>
        <p class="content-2-level">
          2.5 线下充值发票需要到开户地指定加油站服务网点索取。线上充值发票，如果是开具增值税普通发票，根据卡片开票类型可进行线上开具。
        </p>
        <p class="content-2-level">
          2.6 如有疑问，请咨询客服热线95504。
        </p>
      </div>
      <div class="content-item">
        <p class="content-1-level">3 责任限制及不承诺事项</p>
        <p class="content-2-level">3.1本APP不承担任何责任，包括但不仅限于：</p>
        <p class="content-2-level">
          1）因不可抗力、系统故障、通讯故障、网络拥堵、供电系统故障、恶意攻击等造成本APP未能及时、准确、完整地提供网上充值服务。
        </p>
        <p class="content-2-level">
          2）如用户利用系统差错、故障或其他原因导致的漏洞，损害本APP及任何第三方的权益，本APP将中止为该用户提供网上充值服务，并保留法律追究的权利。
        </p>
        <p class="content-2-level">
          3）本APP负责对APP上的信息进行审核与更新，但并不就信息的时效性、准确性以及服务功能的完整性和可靠性承担任何义务和赔偿责任。
        </p>
        <p class="content-2-level">
          4) 如果本APP发现有影响用户信息安全的行为，本APP有权采取对用户信息实施保护的措施。
        </p>
        <p class="content-2-level">
          3.2本APP对以下情形不做任何承诺，包括但不仅限于：服务一定能符合您的要求；服务不会受中断；服务的安全性、及时性、完整性和准确性；所涉及第三方的服务。
        </p>
        <p class="content-2-level">
          3.3本APP具有第三方网站链接。本APP无法就第三方网站和APP的内容或可用性予以控制或对其负责。如果您决定访问任何与本APP相链接的第三方网站或APP，则应完全自行承担相应风险和责任。
        </p>
        <p class="content-2-level">
          3.4 您应妥善保管账户、密码等信息资料，非因本APP原因造成您的用户信息泄露，由此给您带来的损失本APP不承担任何责任。
        </p>
        <p class="content-2-level">
          3.5 您在使用本APP服务时，因个人原因产生的损失，本APP不承担任何责任。
        </p>
        <p class="content-2-level">
          3.6 您同意保障和维护本APP的利益，并承担您或其他人使用您的用户资料造成本APP或任何第三方的损失或损害的赔偿责任。
        </p>
      </div>
      <div class="content-item">
        <p class="content-1-level">4 知识产权</p>
        <p class="content-2-level">
          本APP所有内容和资源的版权归本APP所有者所有(除非本APP已经标明版权所有人)，页面所有信息受《中华人民共和国著作权法》及相关法律法规和中国加入的所有知识产权方面的国际条约的保护。未经本APP事先的书面许可，任何单位和个人不得就本APP上的相关资源以任何方式、任何文字做全部或局部复制、修改、发送、储存、发布、交流或分发，或利用本APP上的相关资源创建其他商业用途的资源。否则本APP将追究其法律责任。
        </p>
      </div>
      <div class="content-item">
        <p class="content-1-level">5 法律适用和管辖</p>
        <p class="content-2-level">
          本协议之效力、解释、变更、执行与争议解决均适用中华人民共和国法律。如缔约方就本协议内容或其执行发生任何争议，双方应尽力友好协商解决；协商不成时，任何一方均可向有管辖权的中华人民共和国大陆地区法院提起诉讼。本APP保留对协议的最终解释权与修改权
        </p>
      </div>
    </div>
  </div>
</template>
<script>
import navBar from "@/components/navBar/NavHeader";
export default {
  components: { navBar },
}
</script>
<style scoped lang="scss">
.recharge_agreement {
  background-color: white !important;
  .title {
    color: red;
    font-weight: bold;
    font-size: 4.5vw;
  }
  .content {
    @include useScrollByBrowser(0);
    padding: 0 20px;
    .content-item {
      padding: 5px 0;
      margin: 10px 10px 0 10px;
      .content-1-level {
        font-size: 4vw;
      }
      .content-2-level {
        text-indent: 2em;
      }
      .content-3-level {
        text-indent: 2em;
      }
      p {
        // p标签换行
        word-wrap: break-word;
        word-break: break-all;
        text-align: left;
        font-family: "Microsoft YaHei", "微软雅黑", "MicrosoftJhengHei",
          "华文细黑", "STHeiti", "MingLiu";
        font-size: 20px;
        margin: 8px 0;
        line-height: 5vw;
      }
    }
  }
}
</style>