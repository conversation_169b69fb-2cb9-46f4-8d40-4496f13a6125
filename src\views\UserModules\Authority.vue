<!--
 * @Author: Yongon
 * @Date: 2022-07-01 17:08:05
 * @LastEditors: ya<PERSON><PERSON> dyya<PERSON><EMAIL>
 * @LastEditTime: 2023-08-02 10:41:37
 * @Description: 隐私权限设置
-->
<template>
  <div class="Authority">
    <!-- 顶部图片 -->
    <section class="header">
      <div class="img"></div>
    </section>

    <!-- 首次提示 -->
    <section class="panel_container" v-if="!isAgreement">
      <div class="panel first_prompt">
        <div class="title">
          <p>中油车队端APP</p>
          <p>隐私政策及用户协议提示</p>
        </div>
        <div class="content">
          欢迎使用中国石油中油车队端APP！在您使用时，需要连接数据网络或者WLAN网络，产生的流量费用请咨询当地运营商。我们非常重视您的个人信息和隐私保护，在您使用中油车队端APP服务前，请阅读<span @click="goPath(1)">《中油车队端隐私政策》</span>和<span @click="goPath(2)">《中油车队端用户协议》</span
          >全部条款，您同意并接收全部条款后再开始使用我们的服务。
        </div>
        <div class="btn">
          <van-button class="no_btn" color="#f2f2f2" @click="noAgreement(true)">不同意</van-button>
          <van-button class="yes_btn" color="#0380c8" @click="agreement()">同意</van-button>
        </div>
      </div>
    </section>

    <!-- 点击不同意软提醒 -->
    <section class="panel_container" v-else>
      <div class="panel first_prompt">
        <div class="title">
          <p>尊敬的用户</p>
        </div>
        <div class="content">您需同意<span @click="goPath(1)">《中油车队端隐私政策》</span>和<span @click="goPath(2)">《中油车队端用户协议》</span>方可使用本软件中的产品和服务。</div>
        <div class="btn">
          <van-button class="no_btn" color="#f2f2f2" @click="quitSystem()">退出</van-button>
          <van-button class="yes_btn" color="#0380c8" @click="noAgreement(false)">我知道了</van-button>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import { recordJsLog } from "@/utils/recordJsLog";

export default {
  components: {},
  data() {
    return {
      // isShow: false,
      isType: "1", // 1同意  2不同意
    };
  },
  computed: {
    ...mapState(["isAgreement"]),
  },
  mounted() {},
  methods: {
    ...mapMutations(["isAgreement_Fn"]),

    /**
     * @description: 同意
     * @return {*}
     */
    agreement() {
      recordJsLog("saveAgreePrivacy", {
        agreePrivacy: true,
      });

      this.$cppeiBridge("saveAgreePrivacy", {
        agreePrivacy: true,
      });
      this.$router.replace({ path: "/loginCenter" });
    },

    /**
     * @description: 不同意
     * @return {*}
     */

    noAgreement(bool) {
      this.isAgreement_Fn(bool);
    },

    /**
     * @description: 退出系统
     * @return {*}
     */
    quitSystem() {
      recordJsLog("saveAgreePrivacy", {
        agreePrivacy: false,
      });

      this.$cppeiBridge("saveAgreePrivacy", {
        agreePrivacy: false,
      });
    },

    /**
     * @description: 跳转页面
     * @return {*}
     */
    goPath(path) {
      if (path == 1) {
        this.$router.push({ path: "/privacyPolicy" });
      } else {
        this.$router.push({ path: "/userAgreement" });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
$themeColor: #ff9200;

.mode-height {
  min-height: 14vw;
}

.Authority {
  height: 100vh;
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;

  .header {
    height: 70vw;
    width: 100vw;

    .img {
      height: 100%;
      width: 100%;
      // 背景图片
      background-image: url(~Images/winter/login_bj.png);
      background-size: 100% 100%;

      .title {
        font-size: 45px;
        color: #fff;
        line-height: 335px;
        padding-left: 45px;
        background: url(~Images/login/logo_3.png) no-repeat;
        background-size: 45px 45px;
      }
    }

    // margin-bottom: 20px;
  }

  .panel_container {
    margin-top: -100px;

    .panel {
      margin: 0 auto;
      width: 80vw;
      background-color: #fff;
      border-radius: 20px;
      padding: 20px 30px 40px 30px;
      box-sizing: border-box;

      .title {
        border-bottom: 1px solid #dadada;

        p {
          line-height: 15px;
        }
      }

      .content {
        font-size: 28px !important;
        text-align: left; // 多行文本居左显示
        text-indent: 2em;
        line-height: 40px;
        margin: 40px 0 50px 0;

        span {
          color: $sysAppColor;
        }
      }

      .btn {
        width: 100%;
        margin-top: 30px;
        text-align: center;
        .van-button {
          width: 110px;
          height: 40px;
        }
        .no_btn {
          color: $sysAppColor !important;
        }
        .yes_btn {
          color: #fff !important;
          margin-left: 20px;
        }
      }
    }
  }
}
</style>
