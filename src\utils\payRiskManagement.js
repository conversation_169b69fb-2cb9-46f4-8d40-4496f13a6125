/*
 * @Author: Yongon
 * @Date: 2022-04-19 17:10:20
 * @LastEditors: ltw <EMAIL>
 * @LastEditTime: 2024-05-13 14:07:38
 * @Description: file content
 */
import request from './http'; // 导入axios封装函数
import { storage } from "Utils/common/";
import { Toast, Dialog } from "vant";

const payRiskManagement = async function (orderNo, paystatus, tradeType ,deviceId = "") {
  const deviceInfo = storage.ss.get("deviceInfo");
  let isInterrupt = false;  // 是否阻断下一步操作：如唤起支付密码键盘 false：不阻断，true 阻断
  let params = {// 司机信息
    address: deviceInfo?.province + ";" + deviceInfo?.city + ";" + deviceInfo?.district,
    appid: 2,
    apptoken: storage.ss.get("apptoken"),
    companyCode: storage.ss.get("defaultCompanyCode"),
    driverPhone: storage.ss.get("phone"),
    latitude: deviceInfo?.lat ?? "39.920248",
    longitude: deviceInfo?.lon ?? "116.407718",
    orderNo: orderNo,
    status: paystatus, // 事前为空，事后0成功 1失败
    tradeType: tradeType, //1预约加油，2室内支付 // 3不下车加油
    // deviceId: storage.ss.get("deviceInfo")? storage.ss.get("deviceInfo").deviceId: "",
    deviceId: deviceId,
  };
  console.log(params,"payRiskManagement---params");
  await request('POST',`/appcenter/riskManagement/v1/payRiskManagement`, params , true, true, true,false,false).then(res => {
    if(res.infoCode != 1) {
      if (res.infoCode == 0) {
        isInterrupt = true;
      }
      Dialog.alert({
        title: '温馨提示',
        message: "当前交易存在风险异常情况,交易失败",
      }).then(() => {});
    }
  }).catch(err => {
    Dialog.alert({
      title: '温馨提示',
      message: err,
    }).then(() => {});
  })
  return isInterrupt
}

export {
  payRiskManagement
}
