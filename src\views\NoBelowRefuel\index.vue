<!--
 * @Author: Yongon
 * @Date: 2022-07-18 16:15:24
 * @LastEditors: ltw <EMAIL>
 * @LastEditTime: 2024-05-30 13:55:24
 * @Description: 首页
-->
<template>
  <div class="container">
    <!-- <no-service-station></no-service-station> -->
    <!-- 顶部企业切换 -->
    <change-company ref="changeCompanyRef" @updateInfo="GetUserCardInfo" />

    <!-- 百度地图 -->
    <!-- v-if="oilStationList.length > 0" -->
    <!-- <baidu-map v-if="oilStationList.length > 0" :center="oilStationList" :currentOilStation="currentOilStation" /> -->
    <baidu-map
      v-if="Object.keys(currentOilStation).length > 0"
      :center="oilStationList"
      :currentOilStation="currentOilStation"
    />

    <!------------- 不下车加油部分  --------------->
    <van-popup
      v-if="addoiltype == 2"
      class="popupoilpre2"
      v-model="popupVisible"
      :overlay="false"
      position="bottom"
      round
      :style="{ bottom: bottomHeight + 'px' }"
    >
      <oil-no-card
        :currentOilStation="currentOilStation"
        :oilStationList="oilStationList"
        :oilType="oilType"
        :cardInfo="cardInfo"
        @tabOilState="tabOilState"
      ></oil-no-card>
    </van-popup>
  </div>
</template>

<script>
import { storage } from "Utils/common/";
import { mapState, mapMutations } from "vuex";
import eventBus from "./_service/eventbus.js";
// import noBelowMixin from "./mixins/mixin.js";

// import Transfer from "@/components/transfer/index.vue";
import changeCompany from "./component/changeCompany.vue";
import baiduMap from "./component/baiduMap.vue";
import oilNoCard from "./component/oilNoCard";
import noServiceStation from "./component/noServiceStation";

import { GetUserCardInfo, GetNavigationList } from "./_service";
import { useGetLocation } from "@/utils/useGetLocation";
import { recordJsLog } from "@/utils/recordJsLog";

export default {
  name: "noBelowRefuel",
  // mixins: [noBelowMixin],
  components: {
    changeCompany,
    // Transfer,
    baiduMap,
    oilNoCard,
    noServiceStation,
  },
  data() {
    return {
      showkeyboard: false, //显示键盘与否
      cardInfo: {}, //加油卡信息
      oilType: _const.oilType,
      showModel: true,
      preOrderInfo: {}, //预授权下单信息
      fishPreOrderInfo: {}, //完成预下单信息
      ifPollingBoole: true, //是否轮询
      pollingTimes: 0, //轮循次数
      countDownVal: "", //预下单成功后倒计时
      preOrderStationInfo: {}, //预下单的加油站信息
      checkOilStation: false, //是否切换油站 防止切换油站回来再次请求GetNavigationList
      timer: null,
      popupVisible: true, // 底部弹窗，默认直接展示
      gunNoarr: "", // 油品对应的枪号数据
      gunNo: "", // 手动输入的枪号
      oilGunNum: 0, // 选中的枪号下标,10000为手动输入油枪号
      bottomHeight: 60 + storage.ss.get("deviceInfo").bottomHeight ?? 0, // 首页的加油与底部的安全距离
      // app监测相关
      hiddenProperty: null,
      visibilityChangeEvent: null,
      isDisabled: false,

      currentOilStation: {},
      oilStationList: [],
      addoiltype: 2,
      navState: false,
    };
  },
  computed: {
    ...mapState(["deviceInfo", "rightSlide"]),
  },
  watch: {
    // isQianYi() {
    //   console.log("NoBelowRefuel watch, isQianYi: " + this.isQianYi);
    //   this.has2Reapply();
    // },
  },
  created() {
    console.log("NoBelowRefuel created");
  },
  mounted() {
    console.log("NoBelowRefuel mounted");
  },
  activated() {
    console.log("NoBelowRefuel activated");
    window.onGetLocation = this.onGetLocation;
    window.onSetDefaultCard = this.onSetDefaultCard;
    console.log(
      storage.ss.get("deviceInfo").bottomHeight,
      'storage.ss.get("deviceInfo").bottomHeight'
    );
    this.activeMethods();
  },
  deactivated() {
    console.log("NoBelowRefuel deactivated");
    // this.watchScreenSwitching(false);
  },
  methods: {
    ...mapMutations([
      "deviceInfo_fn",
      "cardNo_fn",
      "carNo_fn",
      "rightSlide_Fn",
    ]),
    /**
     * @description: 切换企业 - 更改sdk默认电子卡号
     * @param {*} params
     * @return {*}
     */

    onSetDefaultCard(params) {
      recordJsLog("onSetDefaultCard", params);
      console.log(
        params.route + " ==> onSetDefaultCard ====>",
        JSON.stringify(params)
      );

      if (params.route === "changeCompany") {
        this.$refs.changeCompanyRef.onSetDefaultCardFn(params);
      }
      if (params.route === "transfer") {
        this.$refs.transferHomeRef.onSetDefaultCardFn(params);
      }
    },

    /**
     * @description: SDK回调定位信息
     * @param {*} params
     * @return {*}
     */

    onGetLocation(params) {
      console.log(params, "params");
      useGetLocation(params, this.callBackFn);
    },

    /**
     * @description: 定位回调具体页面处理逻辑
     * @param {*} params
     * @return {*}
     */
    callBackFn(bool) {
      if (bool) {
        this.GetUserCardInfo();
        // }
      } else {
        // 手动打开，重新赋值默认经纬度，请求油站地图信息
        // this.GetNavigationList();
        this.GetUserCardInfo();
      }
    },
    /**
     * @description: keep-alive数据缓存执行方法
     * @return {*}
     */
    activeMethods() {
      setTimeout(() => {
        document.querySelector(
          ".van-tabbar--fixed"
        ).style.paddingBottom = storage.ss.get("paddingBottom");
        document.querySelector(".companybox2").style.paddingTop =
          Number(storage.ss.get("statusHeight") ?? 40) + "px";
      }, 500);
      console.log(this.rightSlide, "rightSliderightSliderightSlide");
      console.log(
        this.$route.query.isStoplocation,
        "this.$route.query.isStoplocation"
      );
      console.log(this.oilType, "oilType246");
      this.navState = this.$route.query.navState;
      if (this.rightSlide == true) {
        this.checkOilStation = true;
        console.log(this.$route, "routeroute");
        this.currentOilStation = this.$route.query?.OilStationinfo;
        console.log(this.currentOilStation, "this.currentOilStation");
        this.rightSlide_Fn(false);
        console.log(this.rightSlide, "rightSlide_Fn2222");
        if (this.checkOilStation) {
          // this.getOilProductlist();
          this.GetUserCardInfo();
          this.navState = false;
          console.log(this.navState, "this.navState 246");
          // this.GetNavigationList();
        }
      } else {
        console.log(221);
        setTimeout(() => {
          recordJsLog("getLocation", { route: "noBelowRefuel" });

          this.$cppeiBridge("getLocation", { route: "noBelowRefuel" });
        }, 100);
      }
    },

    /**
     * @description: 监听锁屏开屏，屏上屏下切换
     * @return {*}
     */
    watchScreenSwitching(bool) {
      this.hiddenProperty =
        "hidden" in document
          ? "hidden"
          : "webkitHidden" in document
          ? "webkitHidden"
          : "mozHidden" in document
          ? "mozHidden"
          : null;
      this.visibilityChangeEvent = this.hiddenProperty.replace(
        /hidden/i,
        "visibilitychange"
      );
      if (bool) {
        document.addEventListener(
          this.visibilityChangeEvent,
          this.onVisibilityChange
        );
      } else {
        document.removeEventListener(
          this.visibilityChangeEvent,
          this.onVisibilityChange
        );
      }
    },

    /**
     * @description: 锁屏开屏，屏上屏下切换激发操作
     * @return {*}
     */
    onVisibilityChange() {
      // console.log(JSON.stringify(this.$route));
      if (!document[this.hiddenProperty]) {
        // this.$toast("首页激活");
        recordJsLog("getLocation", { route: "noBelowRefuel" });

        this.$cppeiBridge("getLocation", { route: "noBelowRefuel" });
      } else {
        // this.$toast("首页隐藏");
      }
    },
    /**
     * @description: 加油卡信息
     * @return {*}
     */

    GetUserCardInfo() {
      return GetUserCardInfo({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
      }).then((res) => {
        console.log("GetUserCardInfo553");
        if (res.infoCode == 1) {
          this.cardInfo = res.data;
          this.carNo_fn(res.data.carno);
          this.GetNavigationList();
          console.log(123321);
        }
      });
    },
    /**
     * @description: 获取导航信息
     * @return {*}
     */
    GetNavigationList() {
      return GetNavigationList({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        distance: _const.distance,
        posx: storage.ss.get("deviceInfo")?.lon || 116.407718,
        // posx: 116.243778,
        posy: storage.ss.get("deviceInfo")?.lat || 39.920248,
        // posy: 40.169878,
        cityCode: "",
        type: "",
        tradeType: "3",
      })
        .then((res) => {
          if (res?.data?.gasStationList?.length > 0) {
            const filtergasStation = res?.data?.gasStationList;
            console.log(filtergasStation, "filtergasStation2222");
            if (filtergasStation.length > 0) {
              filtergasStation.map(function(item, index, self) {
                if (item.oilStationCode == "V911") {
                  item.stationName = "中国石油河北张家口太子城服务区加氢站";
                }
              });
              const gasStationList = filtergasStation;
              setTimeout(() => {
                this.oilStationList = gasStationList;
              }, 500);
              console.log(
                this.checkOilStation,
                "checkOilStationcheckOilStation222"
              );
              // todo 如果当前油站（油站列表默认取第一个油站）的经纬度为空，则给油站赋默认地图（北京）的经纬度
              if (!gasStationList[0].posx && !gasStationList[0].posy) {
                this.currentOilStation = gasStationList[0];
                this.currentOilStation.posx = "116.39752";
                this.currentOilStation.posy = "39.908754";
              } else {
                if (!this.checkOilStation) {
                  this.currentOilStation = gasStationList[0];
                }
              }
              console.log(
                "addoiltype:1预授权加油；2不下车加油 ====>  addoiltype ==",
                this.addoiltype
              );
              console.log("mixins: getOilProductlist22 触发");
              this.getOilProductlist();
            }
          } else {
            // 解决油站为空地图不展示问题【优化】
            if (!storage.ss.get("deviceInfo")?.city) {
              this.$set(this.currentOilStation, "posx", "116.39752");
              this.$set(this.currentOilStation, "posy", "39.908754");
            } else {
              this.$set(
                this.currentOilStation,
                "posx",
                storage.ss.get("deviceInfo")?.lon
              );
              this.$set(
                this.currentOilStation,
                "posy",
                storage.ss.get("deviceInfo")?.lat
              );
            }
          }
        })
        .catch((err) => {});
    },

    /**
     * @description: // 不下车加油 获取油品对应的油枪列表
     * @return {*}
     */

    getOilProductlist() {
      console.log(
        this.currentOilStation?.oilStationCode,
        "this.currentOilStation?.oilStationCode"
      );
      const params = {
        appid: 2, //this.rstParams.appid
        apptoken: storage.ss.get("apptoken"),
        stationCode: this.currentOilStation?.oilStationCode,
        // stationCode: "BA01",
      };
      this.$http(
        "POST",
        `/appcenter/trade/v1/getOilProductList`,
        params,
        true,
        true,
        true,
        false,
        true
      )
        .then((res) => {
          if (res.infoCode == 1) {
            this.oilType = res.data.oilProductGunNoList;
            if (this.oilType.length > 0) {
              this.gunNoarr = this.oilType[0].oilGunNo.split(",");
            } else {
              this.gunNoarr = [];
            }
          } else {
            this.$Dialog.alert({
              title: "提示",
              message: res.info,
            });
          }
          console.log(this.gunNoarr, "this.gunNoarr411");
          console.log(this.currentOilStation, "this.currentOilStation412");
        })
        .catch((err) => {
          this.$Dialog.alert({
            title: "提示",
            message: err,
          });
        });
    },
    tabOilState(state) {
      this.tabOil = state;
    },
  },
};
</script>

<style lang="scss" scopeed>
.container {
  position: relative !important;
  width: 100vw;
  height: 100vh;
  .map {
    width: 100vw;
    height: 100vh;
  }
  .choose_license {
    width: 100vw;
    position: absolute;
    bottom: 100px;
    height: auto;
  }
  .gnc {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 90px !important;
    margin: auto;
    bottom: 0;
    height: 60vh;
    overflow: scroll;
    border: 1px solid #cdc8c8;
  }
}
.popupoilpre2 {
  z-index: 0 !important;
  height: auto;
  background: transparent;
}
.popupoilpay {
  z-index: 0 !important;
  height: 120vw;
}
.popupoilpay {
  background: #f5f5f5;
  height: 112vw;
  .staion {
    background: #fff;
    margin: 0 3vw 3vw;
    padding: 4vw;
    border-radius: 4vw;
    text-align: left;
    position: relative;
    .staioninfo {
      background: #fff;
      display: flex;
      align-items: center;
      margin-bottom: 1vw;
      b {
        font-size: 1.3em;
        margin-right: 2vw;
        max-width: 50vw;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .distance {
      display: block;
      margin-top: 1vw;
    }
    .carno {
      margin-top: 6vw;
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        font-size: 0.6em;
        background: url("~@/assets/images/gasstationmap/addcar.png") no-repeat
          left -1px;
        padding-left: 5vw;
        background-size: contain;
      }
    }
    .baidumaping {
      width: 20vw;
      height: 20vw;
      background: url("~@/assets/images/gasstationmap/navigation.png") no-repeat;
      background-size: contain;
      position: absolute;
      top: 3vw;
      right: 1vw;
    }
  }
  .staionorder {
    padding: 4vw 3vw 3vw;
    border-radius: 4vw;
    margin: 0 3vw;
    background: #fff;
    > div {
      text-align: left;
    }
    /deep/ .van-grid {
      margin: 2vw 0;
      .van-grid-item {
        &__content {
          padding: 8px;
          background: transparent
            url("~@/assets/images/gasstationmap/youpinB.png");
          background-size: 100% 100%;
        }
        &.activedStaion {
          .van-grid-item__content {
            // background: url("~@/assets/images/gasstationmap/youpinA.png");
            background: url("~@/assets/images/winter/btn.png");
            background-size: 100% 100%;
          }
        }
      }
      .other {
        font-size: 12px;
      }
    }
    .gun {
      height: 31vw;
      margin-bottom: 2vw;
      overflow-y: auto;
      > div {
        span {
          float: right;
          color: #989898;
          font-size: 0.8em;
        }
      }
      /deep/ .van-field__control {
        border: 1px solid #ff9200;
        padding-left: 10px;
        border-radius: 1vw;
      }
    }
    .van-button--large {
      height: 40px;
      background-color: #ff9200;
      border: 1px solid #ff9200;
    }
  }
}
</style>
