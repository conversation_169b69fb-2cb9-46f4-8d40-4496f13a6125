/** 
  全局scss变量定义
  一般是根据UI图来设置个别样式：如颜色，字体大小等
  所有变量我们均以$开头 命名遵循驼峰法
*/

/** 应用基础变量 */

// 应用主题颜色
$sysAppColor: #0380c8;
// 应用字体颜色
$sysFontColor: #333;
// 导航栏左右离边界的间距
$sysHeaderSpacing: 16px;
// 应用主体背景色
$sysPageBg: #e5e5e5;

/** 应用业务变量 */
$bussBuyCar: #eeb422;
$bussDivisionColor: #d6d6d6;
$bussFontColor: #ebebeb;

$themeColor:#ff5d00;
$backgoundColor:#ffffff;
$font-size:13px;
$bgActive: rgba(176,196,222,.2);

$commonBtnColor: #0887f2;
$commonBackground: #f6f6f6;
 
$compActiveColor: #1672ff;
$compDefaultFontColor: #9b9b93;
$compActiveFontColor: #333;

// TODO：mini工程变量
$btnBackGroundColorPlain:#26881F;
$btnBackgroundColor:#ECFBEF;
$width700:700px;