/**
 * 封装http状态码
 * @param {后端http状态码} code
 * @param {响应主体} response
 */
export const throwErrorCode = (code, response) => {
  let message = '请求错误';
  switch (code) {
    case 400:
      message = '请求错误';
      break;
    case 401:
      message = '未授权，请登录';
      break;
    case 403:
      message = '拒绝访问';
      break;
    case 404:
      message = `请求地址出错: ${response.config.url}`;
      break;
    case 408:
      message = '请求超时';
      break;
    case 500:
      message = '服务器内部错误';
      break;
    case 501:
      message = '服务未实现';
      break;
    case 502:
      message = '网关错误';
      break;
    case 503:
      message = '服务不可用';
      break;
    case 504:
      message = '网关超时';
      break;
    case 505:
      message = 'HTTP版本不受支持';
      break;
    default:
      message = '未知错误';
  }
  return message;
};

// TODO: 0113 新增业务相关code
/**
 * 封装业务接口状态码
 * @param {后端接口状态码} code
 * @param {响应主体} response
 */
export const throwErrorBussCode = (code, response) => {
  let message = '';
  switch (code) {
    case 23:
      message = '当前用户不存在';
      break;
    case 24:
      message = '当前卡号已被他人绑定';
      break;
    case 25:
      message = '当前卡号已被绑定,请勿重复绑定!';
      break;
    case 26:
      message = `验证码已发送,请查收!`;
      break;
    case 27:
      message = '您办卡时未预留手机号，请持加油卡和办卡证件到中国石油发卡充值网点更新手机号!';
      break;
    case 28:
      message = '您已进行过绑卡校验,谢谢！';
      break;
    case 29:
      message = '绑定失败,请您重新获取验证码!';
      break;
    case 30:
      message = '此卡为单位主卡，不能绑定!';
      break;
    case 31:
      message = '证件类型不匹配';
      break;
    case 32:
      message = '证件姓名不匹配';
      break;
    case 33:
      message = '证件号码不匹配';
      break;
    case 34:
      message = '卡号不匹配';
      break;
    case 35:
      message = '您未绑定该卡，请您绑卡后再进行操作！';
      break;
    case 36:
      message = '注册用户失败';
      break;
    default:
      message = '';
  }
  return message;
};
