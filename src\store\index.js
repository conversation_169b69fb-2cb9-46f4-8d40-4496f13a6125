import Vue from "vue";
import Vuex from "vuex";
import { storage } from "Utils/common/";

Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    isQianYi: 0, //是否显示迁移  迁移状态，0-默认状态, 1-需要迁移, 2-已迁移
    isLoadingShow: false, // 迁移判断loading显示
    cancelTokenArr: [], // 取消请求token数组
    rstParams: {
      appid: 2,
      apptoken: "",
      cardNo: "wrtw", //卡号
      companyUserId: "string",
      userid: "string",
      channel: "922", //渠道编码 922-车队卡APP预授权加油
      cancelReason: "暂时不需要了", //取消预下单原因
      preAuthMode: "10", //31 -微信主扫      332 -支付宝主扫      10 -中油IC卡支付
      carNo: "", //车牌号
      preAuthType: "2", //预授权类型 2-车队APP预授权加油
      queryType: 1, //1：查询预授权成功订单2：查询扣款成功订单
    },
    deviceInfo: {
      type: "", // 设备型号iOS或Android(String)
      name: "", // 设备名称(String)
      version: "1.0.0", // App当前版本号
      statusHeight: "", // 安全区域状态栏高度(Double)
      bottomHeight: "", // 安全区域底部高度(Double)
      isVirBar: "", //是否有虚拟导航（true 有，false没有）
      lon: "", // 经度(Double)
      lat: "", // 纬度(Double)
      province: "", // 省(String)
      city: "", // 城市(String)
    },
    globalTest: "我是根节点下的vuex数据",
    navigations: storage.ss.get("navigations") || [],
    // 当前路由方式：forward(前进)，back(后退)
    curRouterType: "",

    //添加车辆界面信息
    addVehicle: {
      carBrand: "",
    },
    // 相机，相册权限
    permissions: {
      camera: false, //相机
      album: false, //相册
    },
    // 权限页面返回上一级弹窗判断
    isAgreement: false,
    // 登录页面权限协议页面返回上一个页面
    isLoginMode: true,
    // 当前登录的手机号
    currentPhone: "",
    // 判断是否安卓右滑返回导致数据丢失
    rightSlide:false,
  },
  mutations: {
    pushToken(state, payload) {
      state.cancelTokenArr.push(payload.cancelToken);
    },
    clearToken({ cancelTokenArr }) {
      cancelTokenArr.forEach((item) => {
        item("路由跳转取消请求");
      });
      cancelTokenArr = [];
    },
    // apptoken
    apptoken_fn(state, param) {
      state.rstParams.apptoken = param;
    },
    // 卡号
    cardNo_fn(state, param) {
      state.rstParams.cardNo = param;
    },
    // 车牌号
    carNo_fn(state, param) {
      state.rstParams.carNo = param;
    },
    // 迁移
    isQianYi_fn(state, param) {
      state.isQianYi = param;
    },
    isQianYi_Loading_fn(state, param) {
      state.isLoadingShow = param;
    },
    //手机信息
    deviceInfo_fn(state, obj) {
      state.rstParams.deviceInfo = obj;
    },
    // 更新路由方式
    Upadate_curRouterType(state, payload) {
      state.curRouterType = payload.type;
    },
    // 删除当前路由后面的路径
    Delete_otherRouterPath(state, payload) {
      state.navigations = state.navigations.slice(0, payload.index + 1);
      storage.ss.set("navigations", state.navigations);
    },
    // 更新路由集合
    Update_navigations(state, payload) {
      let arr = state.navigations;
      arr.push(payload);
      state.navigations = arr;
      storage.ss.set("navigations", state.navigations);
    },
    // 重置路径集合
    Reset(state, payload) {
      state.navigations = [];
    },

    //配置添加车辆界面的车型
    SetAddVehicle_carBrand(state, brand) {
      state.addVehicle = {
        carBrand: brand,
      };
    },
    //充值车辆界面信息
    ReSetAddVehicle(state) {
      state.addVehicle = {
        carBrand: "",
      };
    },
    // 隐私权限是否授权
    Update_permissions(state, params) {
      state.permissions = {
        camera: params.camera ?? false,
        album: params.album ?? false,
      };
    },
    isAgreement_Fn(state, param) {
      state.isAgreement = param;
    },
    isLoginMode_Fn(state, param) {
      state.isLoginMode = param;
    },
    currentPhone_Fn(state, param) {
      state.currentPhone = param;
    },
    rightSlide_Fn(state,param){
      state.rightSlide = param;
    },
  },
  actions: {},
  modules: {},
});
