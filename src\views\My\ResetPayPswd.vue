<template>
  <div class="reset-pay-pswd">
    <nav-bar title="重置支付密码" />
    <!-- 表单 -->
    <div class="reset-card">
      <van-form validate-first label-width="70" :show-error-message="false" :show-error="false">
        <van-field v-model="item.name" label="姓名" placeholder="请输入姓名" input-align="right"> </van-field>
        <van-field v-model="holderIdTypeStr" label="证件类型" placeholder="请选择数据类型" input-align="right"> </van-field>
        <van-field v-model="item.idNo" label="证件号码" placeholder="请输入证件号码" input-align="right"> </van-field>
        <van-field v-model="item.cardNo" label="加油卡号" placeholder="请输入加油卡号" input-align="right"> </van-field>
        <van-field v-model="verifyCode" label="验证码" placeholder="请输入短信验证码" maxlength="6" input-align="right">
          <template slot="button">
            <van-button style="width: 85px; height: 28px" size="small" :color="$setting.themeColor" plain :disabled="!showSend" @click="checkAndSend">
              <!-- 第一次发送显示: 获取验证码 -->
              <div v-show="showSend" v-if="0 === clickNum">获取验证码</div>
              <!-- 以后都显示: 再次发送 -->
              <div v-show="showSend" v-else>再次发送</div>
              <!-- 倒计时60秒 -->
              <span v-show="!showSend">{{ count }}&nbsp;s</span>
            </van-button>
          </template>
        </van-field>
      </van-form>
    </div>
    <!-- 提示 -->
    <div class="tips">温馨提示：验证码短信会发送到您的办卡预留手机上</div>
    <!-- 按钮 -->
    <div class="btn-wrap">
      <van-button block round :color="$setting.themeColor" size="large" :disabled="isDisabled" @click="doResetPayPswd">重置密码</van-button>
    </div>
  </div>
</template>

<script>
import navBar from "@/components/navBar/NavHeader";
import { Toast } from "vant";
import { storage } from "Utils/common/";
import { recordJsLog } from "@/utils/recordJsLog";

//更改倒计时时间
const TIME_COUNT = 59;

export default {
  props: {},
  components: { navBar },
  data() {
    return {
      item: storage.ss.get("driverInfo"),
      // 验证码
      verifyCode: "",
      holderIdType: 1,
      holderIdTypeStr: "身份证",
      // 初始启用按钮
      showSend: true,
      // 点击发送验证码次数
      clickNum: 0,
      // 初始化次数
      count: "",
    };
  },
  computed: {
    isDisabled() {
      return this.verifyCode ? false : true;
    },
  },
  created() {
    this.item = storage.ss.get("driverInfo");
  },
  activated() {
    this.item = storage.ss.get("driverInfo");
  },
  mounted() {
    /// 在window上挂载和Native统一的方法名，onLocalAuthentication（如在mounted中进行挂载）
    /// 右侧this.onLocalAuthentication为Vue端具体实现接收的方法
    window.onSendVerifyCode = this.onSendVerifyCode;
    window.onResetPin = this.onResetPin;
  },
  watch: {},
  methods: {
    onSendVerifyCode(res) {
      recordJsLog('onSendVerifyCode', res);

      if (res.error === 0) {
        Toast("短信验证码发送成功");
      } else {
        this.$Dialog.alert({
          title: "获取短信验证码失败",
          message: res.error + "-" + res.message,
        });
      }
    },
    onResetPin(res) {
      recordJsLog('onResetPin', res);

      if (res.error === 0) {
        Toast("重置成功");
        setTimeout(() => {
          this.$router.go(-1);
        }, 1200);
      } else {
        this.$Dialog.alert({
          title: "重置支付密码失败",
          message: res.error + "-" + res.message,
        });
      }
    },
    // 校验&发短信
    checkAndSend() {
      // check ......
      // 发送验证码
      this.sendCodeByPhone();
    },
    // 发送短信验证码
    sendCodeByPhone() {
      // 倒计时60秒
      this.countdown();
      recordJsLog('sendVerifyCode', {
        type: 4,
      });

      this.$cppeiBridge("sendVerifyCode", {
        type: 4,
      });
    },
    // 倒计时60秒
    countdown() {
      this.clickNum++;
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.showSend = false;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.showSend = true;
            clearInterval(this.timer); // 清除定时器
            this.timer = null;
          }
        }, 1000);
      }
    },
    doResetPayPswd() {
      recordJsLog("resetPin", {
        verifyCode: this.verifyCode,
        pan: this.item.cardNo,
        holderIdType: this.holderIdType,
        holderId: this.item.idNo,
        holderName: this.item.name,
      });

      this.$cppeiBridge("resetPin", {
        verifyCode: this.verifyCode,
        pan: this.item.cardNo,
        holderIdType: this.holderIdType,
        holderId: this.item.idNo,
        holderName: this.item.name,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.reset-pay-pswd {
  .reset-card {
    height: auto;
    /deep/ .van-cell {
      margin-bottom: 1px;
      height: 48px;
    }
  }
  .tips {
    color: #747474;
    padding: 30px;
    text-align: left;
    font-size: 25px;
  }
  .btn-wrap {
    padding: 0 20px;
    margin: 0 20px;
    margin-top: 30px;
  }
}
</style>
