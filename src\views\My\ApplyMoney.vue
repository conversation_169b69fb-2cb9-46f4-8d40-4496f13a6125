<template>
  <div class="apply_money">
    <nav-bar title="资金申请" />
    <div class="card_balance">
      <div>您当前卡余额（元）</div>
      <div class="balance">￥{{driverInfo.balance / 100}}</div>
    </div>
    <van-button type="primary" size="large" round @click="applyMoneyVisible = true">资金申请</van-button>
    <div class="notice">您最多可向企业申请资金5000元，申请后需要企业管理员审批，审批通过后资金到账，如遇问题，请与企业管理员联系。</div>

    <van-dialog v-model="applyMoneyVisible" title="向企业申请资金" show-cancel-button :before-close='beforeCloseapplyMoney'>
      <van-form>
        <van-field v-model="applymoney" type="number" :formatter="formatter" label="申请资金" placeholder="请输入需要申请的金额(元)" label-width="16vw" size="normal" />
      </van-form>
    </van-dialog>
  </div>
</template>
<script>
import navBar from "@/components/navBar/NavHeader";
import {isCancellation} from "Utils/isCancellation";
// import { getActivatedList, CancelECardApply } from "./_service";
import { storage } from "Utils/common";
export default {
  components: { navBar },
  data() {
    return {
      driverInfo: storage.ss.get('driverInfo'),
      applyMoneyVisible: false,
      applymoney: '',
    }
  },
  created() {
    // isCancellation().then(result => {
    //     // result=false时为注销状态,中断后续验证及请求
    //     if(!result.isCancel) {
    //       return
    //     }
    //   })
  },
  activated() {},
  mounted() {},
  computed: {},
  methods: {
    beforeCloseapplyMoney (action, done) {
      if (action === 'confirm') {
        if(this.applymoney) {
          this.applyMoney(done);
          done();
        } else {
          this.$toast('请输入申请金额!');
          done(false);
        }
      } else {
        done();
      }
    },
    formatter(value) { // 对输入的数字保留两位小数
      return value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3')
    },
    applyMoney(done) {
      if(this.applymoney < 0) {
        this.$toast('申请金额不能为负数!');
        done(false);
        return false
      }
      if(this.applymoney*1 > 5000) {
        this.$toast('申请金额不能大于5000！');
        done(false);
        return false
      }
      let money = this.moneyMul(this.applymoney);
      const params =  {
        launcherUniqueId: storage.ss.get('defaultCompanyCode'),
        apptoken: storage.ss.get('apptoken'),
        appid: 2,
        businessType: 2, // 0销户申请，1下级企业进行资金申请，2司机资金申请
        requestCnt: '资金申请' + money,
        attribute1: money,
      };
      this.$http('POST',`/card/approve/v1/launch`, params, true, true, true,false,true).then(res => {
        if(res.infoCode == 1) {
          this.applyMoneyVisible = false;
          this.applymoney = '';
          this.$Dialog.alert({title: '资金申请成功，企业管理员审批后到账',});
        }
      }).catch(err => {})
    },
    moneyMul(val) {
      let index = val.indexOf('.');
      let result = '';
      if (index !== -1) {
        result = val.substring(0, index) + (val.substring(index+1).length == 2 ? val.substring(index+1) : val.substring(index+1) +'0')
      } else {
        result = val.substring(0) * 100
      }
      return result
    }
  },
}
</script>
<style lang="scss" scoped>
.apply_money {
  background: #fff !important;
  margin: 0 4vw;
  .card_balance {
    color: #ff9200;
    font-size: 1rem;
    margin-top: 10vw;
    .balance {
      font-size: 1.5rem;
      margin: 6vw 0 18vw;
    }
  }
  .van-button--primary {
    background-color: #ff9200;
    border: 1px solid #ff9200;
    margin-bottom: 6vw;
  }
  .notice {
    text-align: left;
    color: #a7a1a1;
    line-height: 1.3rem;
  }
}
</style>