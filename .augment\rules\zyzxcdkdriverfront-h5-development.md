---
type: "manual"
---

# AI工具使用规范 - 中油智行车队卡司机端前端开发

## 🎯 项目技术栈识别
- **项目名称**: petro-soti-zyzxcdkdriverfront-h5 (中油智行-车队卡-司机端-H5应用)
- **实际包名**: fleetcard_driver_vue2
- **版本**: 0.1.0
- **框架**: Vue 2.6.11 + Vue CLI 4.2.0
- **开发语言**: 原生JavaScript (严禁使用TypeScript)
- **状态管理**: Vuex 3.1.2
- **路由管理**: Vue Router 3.1.5
- **构建工具**: Vue CLI 4.2.0 + Webpack 4.47.0
- **样式**: SCSS + CSS3 (node-sass 4.12.0, sass-loader 8.0.2)
- **平台支持**: H5 Web应用
- **HTTP库**: Axios 0.19.2 (通过统一封装)
- **Mock工具**: MockJS 1.1.0
- **调试工具**: VConsole 3.4.0 (开发环境集成)

## 🔧 AI工具核心约束

### 强制性技术约束
1. **框架版本**: 必须使用Vue 2.6.11，严禁使用Vue 3或Composition API
2. **HTTP请求**: 必须使用项目统一封装的axios实例，基础路径为 `/Api`
3. **状态管理**: 必须使用Vuex，不得使用Pinia或其他状态管理库
4. **样式语言**: 使用SCSS，支持全局变量和混入
5. **Mock数据**: 开发环境使用MockJS进行数据模拟

### 项目结构识别
- **页面目录**: `src/views/` (业务页面，直接在views下按功能分类)
- **组件目录**: `src/components/` (公共组件)
- **工具目录**: `src/utils/` (工具函数)
- **样式目录**: `src/assets/styles/` (全局样式，包含_variable.scss和_mixin.scss)
- **资源目录**: `src/assets/` (静态资源)
- **Mock目录**: `src/mock/` (Mock数据和接口，当前未启用)
- **配置文件**: `src/config/setting.js` (项目配置)

## 🔧 API开发规范

### HTTP请求配置
- **基础URL**: 通过环境变量 `VUE_APP_API_URL` 配置
- **代理路径**: `/Api` (开发环境通过webpack代理)
- **请求方式**: 使用统一封装的axios实例
- **错误处理**: 统一的响应拦截和错误处理机制

### API响应数据格式规范
```javascript
// 标准API响应格式
{
  Value: true,           // 请求是否成功
  InfoCode: 1,          // 业务状态码 (1成功，0失败)
  Info: '操作成功',      // 提示信息
  Data: null            // 业务数据
}
```

注意：当前项目Mock功能已关闭，直接使用真实API接口

### 环境配置规范
- **开发环境**: `.env.development` - Mock数据开关通过 `VUE_APP_MOCK` 控制 (当前为false)
- **生产环境**: `.env.production` - 包含gzip压缩、代码优化等配置
- **API地址**: 开发环境使用 `https://cdkdev.kunlunjyk.com/`
- **端口配置**: 开发环境默认端口8888
- **基础路径**: `/fleetcard_driver`
- **构建目录**: `fleetcard_driver`

## 🎨 Vue组件开发规范

### 组件结构约束
- **页面组件**: 位于 `src/views/` 目录，按功能模块分类
- **公共组件**: 位于 `src/components/` 目录
- **命名规范**: 使用PascalCase命名组件文件和组件名
- **文件结构**: 单文件组件(.vue)，包含template、script、style三部分

### Vue 2 Options API规范
```javascript
// 标准组件结构
export default {
  name: 'ComponentName',
  components: {},
  props: {},
  data() {
    return {};
  },
  computed: {},
  watch: {},
  methods: {},
  created() {},
  mounted() {},
  destroyed() {}
};
```

### 样式规范
```vue
<style scoped lang="scss">
// 使用SCSS语法
// 必须添加scoped属性避免样式污染
// 可以使用全局变量和混入
@import "~@/assets/styles/variable";
@import "~@/assets/styles/mixin";
</style>
```

## 🔄 AI工具开发流程

### API开发流程
1. **分析需求**: 确定API功能和数据结构
2. **封装API**: 创建对应的API调用函数
3. **集成调用**: 在组件或Vuex中使用API函数
4. **测试验证**: 确保接口调用正常
5. **错误处理**: 添加适当的错误处理和用户提示

注意：当前项目Mock功能未启用，直接对接真实API接口

### 组件开发流程
1. **确定组件类型**: 页面组件或公共组件
2. **创建组件**: 按照命名规范创建.vue文件
3. **使用Options API**: 严格按照Vue 2 Options API结构编写
4. **样式编写**: 使用SCSS + scoped样式
5. **状态管理**: 通过Vuex管理组件状态
6. **路由配置**: 在router中配置页面路由

### 构建配置流程
1. **webpack配置**: 通过 `vue.config.js` 进行自定义配置
2. **别名配置**: 使用@、Components、Utils等别名
3. **代理配置**: 开发环境API代理配置
4. **优化配置**: 生产环境代码压缩、gzip等优化

## 🚫 AI工具使用限制

### 严格禁止的操作
- **启动项目**: 禁止使用 `npm run serve` 等启动命令
- **框架混用**: 禁止使用Vue 3语法或Composition API
- **依赖变更**: 禁止修改package.json或安装新依赖
- **配置修改**: 禁止修改vue.config.js等核心配置文件
- **环境变量**: 禁止修改.env文件中的环境配置
- **构建命令**: 禁止执行build、deploy等构建命令

### 代码质量约束
- 单个方法不超过50行代码
- 必须使用描述性的变量和函数名
- 复杂逻辑必须添加注释
- 遵循DRY原则，避免代码重复
- 组件必须使用scoped样式
- Mock数据必须包含完整的业务数据结构

### 环境和构建约束
- **开发环境**: 当前配置为development环境
- **应用类型**: APP_TYPE为2，代表司机端应用
- **主题配置**: 主题色为#0380c8
- **构建目录**: 生产环境构建到fleetcard_driver目录
- **移动端适配**: 使用postcss-px-to-viewport进行vw适配，设计稿宽度750px

## 📋 AI工具检查清单

### 代码生成前检查
- [ ] 确认使用Vue 2.6.11 + Options API
- [ ] 确认使用项目统一的HTTP请求方式
- [ ] 确认组件使用正确的命名规范
- [ ] 确认使用Vuex进行状态管理
- [ ] 确认遵循项目目录结构
- [ ] 确认样式使用SCSS + scoped

### 代码生成后验证
- [ ] 检查语法是否符合Vue 2.6.11规范
- [ ] 检查API调用是否正确
- [ ] 检查样式是否使用SCSS + scoped
- [ ] 检查是否遵循项目目录结构
- [ ] 检查Vuex模块是否正确集成
- [ ] 检查路由配置是否正确

## 🎯 AI工具最佳实践

### 代码生成策略
1. **渐进式开发**: 先生成基础结构，再逐步完善功能
2. **模块化思维**: 将复杂功能拆分为多个小模块
3. **复用优先**: 优先使用现有组件和工具函数
4. **接口对接**: 直接对接真实API接口进行测试
5. **响应式设计**: 考虑H5移动端适配

### 错误处理原则
1. **API错误**: 统一使用axios拦截器处理
2. **组件错误**: 在组件中添加适当的错误边界
3. **数据验证**: 对API返回数据进行必要的验证
4. **用户反馈**: 提供清晰的错误提示信息
5. **网络异常**: 处理网络超时和连接异常情况

### 性能优化建议
1. **代码分割**: 使用路由懒加载减少首屏加载时间
2. **状态管理**: 合理使用Vuex模块避免状态污染
3. **样式优化**: 使用scoped样式避免全局污染
4. **资源优化**: 图片压缩、代码压缩等优化措施
5. **缓存策略**: 合理使用浏览器缓存和HTTP缓存

## 📝 提交规范

项目使用标准的Git提交规范，支持以下类型：

- **feat**: 新特性、新功能
- **fix**: 修改bug
- **docs**: 文档修改
- **style**: 代码格式修改
- **refactor**: 代码重构
- **perf**: 性能优化
- **test**: 测试用例修改
- **build**: 构建相关修改
- **ci**: 持续集成修改
- **chore**: 其他修改

## 🌐 交互语言

AI工具必须始终使用简体中文与用户交互，包括代码注释和文档说明。

## ✅ 配置验证清单

### 项目基础信息验证
- [x] 项目名称已修正为司机端 (petro-soti-zyzxcdkdriverfront-h5)
- [x] 技术栈版本已更新 (Vue Router 3.1.5, Vuex 3.1.2)
- [x] 目录结构已修正 (src/views/ 而非 src/views/Pages/)
- [x] 应用类型已修正 (APP_TYPE=2 司机端)
- [x] 构建目录已修正 (fleetcard_driver)
- [x] 端口配置已修正 (8888)

### 环境配置验证
- [x] 开发环境配置完整 (.env.development)
- [x] 生产环境配置完整 (.env.production)
- [x] 环境变量说明详细
- [x] API代理配置正确
- [x] 移动端适配配置完整

### 构建和质量规则验证
- [x] Webpack构建规则完整
- [x] 代码质量检查规则完整
- [x] 安全规则配置完整
- [x] 性能优化规则完整
- [x] ESLint规则说明 (当前已关闭)

### Mock配置验证
- [x] Mock功能状态已明确 (当前未启用)
- [x] API响应格式规范已定义
- [x] 相关流程已更新为直接对接真实API

### 依赖库验证
- [x] 核心依赖版本已更新
- [x] UI组件库已添加 (Vant 2.12.48)
- [x] 工具库版本已确认
- [x] 开发依赖已确认

## 📚 项目特定知识

### 关键依赖说明
- **Vue**: 核心框架，版本2.6.11
- **Vue Router**: 路由管理，版本3.1.5
- **Vuex**: 状态管理，版本3.1.2
- **Webpack**: 构建工具，版本4.47.0
- **MockJS**: Mock数据生成，版本1.1.0 (当前未启用)
- **node-sass**: SCSS编译器，版本4.12.0
- **Vant**: UI组件库，版本2.12.48
- **Axios**: HTTP请求库，版本0.19.2

### 环境配置说明
- **开发环境**: development，支持热重载和Mock数据 (当前Mock未启用)
- **生产环境**: production，启用代码压缩和优化
- **API地址**: 开发环境使用cdkdev.kunlunjyk.com
- **构建目录**: fleetcard_driver
- **基础路径**: /fleetcard_driver
- **开发端口**: 8888

### 特殊功能说明
- **骨架屏**: 支持骨架屏功能，通过VUE_APP_ISSKELETON控制
- **Bundle分析**: 支持打包分析，通过VUE_APP_ISREPORTER控制
- **Gzip压缩**: 生产环境支持gzip压缩
- **代码缓存**: 开发环境使用HardSourceWebpackPlugin加速构建
- **移动端适配**: 使用postcss-px-to-viewport进行vw适配，设计稿宽度750px

### 环境变量配置详细说明
#### 开发环境变量 (.env.development)
- **NODE_ENV**: "development" - 环境标识
- **BASE_URL**: "/fleetcard_driver" - 公共路径
- **VUE_APP_SERVER_ID**: 1 - 服务器ID (自动部署用)
- **VUE_APP_API_URL**: "https://cdkdev.kunlunjyk.com/" - API基础地址
- **VUE_APP_MOCK**: 'false' - Mock数据开关 (当前关闭)
- **VUE_APP_PORT**: "8888" - 开发服务器端口
- **VUE_APP_AXIOS_URL**: "/Api" - 图片验证码路径前缀
- **VUE_APP_ISSKELETON**: "false" - 骨架屏开关
- **VUE_APP_ISREMOVECODE**: 'false' - 是否移除console.log等调试代码

#### 生产环境变量 (.env.production)
- **NODE_ENV**: 'production' - 环境标识
- **BASE_URL**: '/fleetcard_driver' - 公共路径
- **VUE_APP_SERVER_ID**: 0 - 生产服务器ID
- **VUE_APP_BUILD_FOLDER**: 'fleetcard_driver' - 构建目录
- **VUE_APP_ISREPORTER**: 'false' - 打包分析开关
- **VUE_APP_iSGZIP**: 'true' - Gzip压缩开关
- **VUE_APP_ISSKELETON**: "false" - 骨架屏开关
- **VUE_APP_ISREMOVECODE**: 'false' - 调试代码移除开关

## 🔧 构建规则配置

### Webpack构建规则
- **别名配置**: 使用@、Components、Utils、Api、Styles等别名简化导入
- **代理配置**: 开发环境/Api代理到VUE_APP_API_URL
- **移动端适配**: style-vw-loader配置，视口宽度375px，精度3位小数
- **PostCSS配置**: px转vw，设计稿宽度750px，排除.van、.ignore、.hairlines类
- **文件名哈希**: 生产环境启用文件名哈希以便缓存控制
- **源码映射**: 开发环境使用eval-source-map，生产环境可选

### 代码分割策略
- **路由懒加载**: 使用动态import进行路由级别代码分割
- **第三方库分离**: Vue、Vue Router、Vuex在生产环境作为外部依赖
- **公共组件**: 抽取公共组件避免重复打包

## 📊 代码质量检查规则

### ESLint规则 (当前已关闭)
- **语法检查**: 严格按照Vue 2.6.11语法规范
- **代码风格**: 使用标准JavaScript代码风格
- **Vue特定规则**: 组件命名、props定义、事件命名等
- **最佳实践**: 避免直接修改props、正确使用key等

### 代码质量约束
- **函数复杂度**: 单个方法不超过50行代码
- **命名规范**: 使用描述性的变量和函数名
- **注释要求**: 复杂逻辑必须添加中文注释
- **DRY原则**: 避免代码重复，提取公共函数
- **组件规范**: 必须使用scoped样式避免污染
- **数据结构**: Mock数据必须包含完整的业务数据结构

### 性能检查规则
- **组件大小**: 单个组件文件不超过500行
- **依赖检查**: 避免引入不必要的第三方库
- **图片优化**: 图片资源必须压缩优化
- **懒加载**: 大型组件和路由必须使用懒加载

## 🔒 安全规则配置

### 数据安全
- **敏感信息**: 禁止在代码中硬编码敏感信息
- **环境变量**: 敏感配置通过环境变量管理
- **API安全**: 所有API请求必须通过统一的axios实例
- **数据验证**: 对用户输入和API返回数据进行验证

### 代码安全
- **XSS防护**: 避免使用v-html，必要时进行内容过滤
- **CSRF防护**: API请求包含必要的安全头
- **依赖安全**: 定期检查依赖包的安全漏洞
- **构建安全**: 生产环境移除调试代码和console输出

## ⚡ 性能优化规则

### 运行时性能
- **组件优化**: 合理使用computed和watch
- **事件处理**: 及时移除事件监听器避免内存泄漏
- **图片懒加载**: 使用vue-lazyload进行图片懒加载
- **列表优化**: 长列表使用虚拟滚动或分页

### 构建性能
- **缓存策略**: 开发环境使用HardSourceWebpackPlugin
- **压缩优化**: 生产环境启用Gzip压缩
- **Tree Shaking**: 移除未使用的代码
- **代码分割**: 按需加载减少首屏加载时间

### 移动端性能
- **视口适配**: 使用vw单位进行移动端适配
- **触摸优化**: 合理设置touch-action属性
- **滚动优化**: 使用better-scroll优化滚动体验
- **网络优化**: 合理使用缓存策略减少网络请求

## 🤖 AI工具专用开发规则

### 强制性开发约束
1. **严格遵循Vue 2.6.11**: 禁止使用任何Vue 3语法
2. **统一HTTP请求**: 使用项目封装的axios实例
3. **Mock数据管理**: 所有Mock数据必须在 `src/mock/` 目录管理
4. **组件命名规范**: 使用PascalCase命名
5. **样式规范**: 必须使用SCSS + scoped

### 标准开发模板

#### API调用模板
```javascript
// 在组件中调用API
methods: {
  async fetchData() {
    try {
      const response = await this.$http.post('/Api/Module/Action', params);
      if (response.data.Value) {
        // 处理成功响应
        this.data = response.data.Data;
      } else {
        // 处理业务错误
        this.$message.error(response.data.Info);
      }
    } catch (error) {
      // 处理网络错误
      console.error('API调用失败:', error);
    }
  }
}
```

#### Vuex模块模板
```javascript
// store/modules/example.js
const state = {
  data: null,
  loading: false
};

const mutations = {
  SET_DATA(state, data) {
    state.data = data;
  },
  SET_LOADING(state, loading) {
    state.loading = loading;
  }
};

const actions = {
  async fetchData({ commit }, params) {
    commit('SET_LOADING', true);
    try {
      // API调用逻辑
    } finally {
      commit('SET_LOADING', false);
    }
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
```

### AI工具使用检查点
- [ ] 是否使用了正确的Vue 2.6.11语法
- [ ] 是否遵循了组件命名规范
- [ ] 是否使用了scoped样式
- [ ] 是否正确配置了Vuex状态管理
- [ ] 是否遵循了项目目录结构
- [ ] 是否使用了统一的HTTP请求方式
- [ ] 是否添加了必要的错误处理
- [ ] 是否考虑了移动端适配

## 📋 配置优化总结

### 主要修改内容
1. **项目信息修正**: 将管理端改为司机端，更新项目名称和应用类型
2. **技术栈版本更新**: 修正Vue Router和Vuex版本号
3. **目录结构调整**: 更新页面目录结构和样式目录路径
4. **环境配置完善**: 详细说明开发和生产环境变量
5. **构建规则补充**: 添加完整的构建、质量、安全、性能规则
6. **Mock配置调整**: 明确当前Mock功能未启用状态
7. **依赖库更新**: 补充Vant UI库和其他关键依赖信息

### 配置文件优化效果
- ✅ 项目基础信息与实际情况完全匹配
- ✅ 技术栈版本信息准确无误
- ✅ 开发规范详细完整
- ✅ 环境配置清晰明确
- ✅ 构建和质量规则全面
- ✅ 安全和性能规则完善
- ✅ AI工具使用指导明确

### 后续建议
1. 定期检查依赖库版本更新
2. 根据项目发展调整配置规则
3. 考虑启用ESLint提升代码质量
4. 根据需要考虑启用Mock功能
5. 持续优化移动端适配方案
