---
type: "manual"
---

# AI工具使用规范 - 中油智行车队卡管理前端开发

## 🎯 项目技术栈识别
- **项目名称**: petro-soti-zyzxcdkmanagefront-h5 (中油智行-车队卡-中油车队端-车队卡2.0)
- **版本**: 0.1.0
- **框架**: Vue 2.6.11 + Vue CLI 4.2.0
- **开发语言**: 原生JavaScript (严禁使用TypeScript)
- **状态管理**: Vuex 4.2.0
- **路由管理**: Vue Router 4.2.0
- **构建工具**: Vue CLI 4.2.0 + Webpack 4.47.0
- **样式**: SCSS + CSS3 (node-sass 4.12.0, sass-loader 8.0.2)
- **平台支持**: H5 Web应用
- **HTTP库**: Axios (通过统一封装)
- **Mock工具**: MockJS 1.1.0
- **调试工具**: 开发环境集成

## 🔧 AI工具核心约束

### 强制性技术约束
1. **框架版本**: 必须使用Vue 2.6.11，严禁使用Vue 3或Composition API
2. **HTTP请求**: 必须使用项目统一封装的axios实例，基础路径为 `/Api`
3. **状态管理**: 必须使用Vuex，不得使用Pinia或其他状态管理库
4. **样式语言**: 使用SCSS，支持全局变量和混入
5. **Mock数据**: 开发环境使用MockJS进行数据模拟

### 项目结构识别
- **页面目录**: `src/views/Pages/` (业务页面)
- **组件目录**: `src/components/` (公共组件)
- **工具目录**: `src/utils/` (工具函数)
- **样式目录**: `src/styles/` (全局样式，包含_variable.scss和_mixin.scss)
- **资源目录**: `src/assets/` (静态资源)
- **Mock目录**: `src/mock/` (Mock数据和接口)
- **配置文件**: `src/config/setting.js` (项目配置)

## 🔧 API开发规范

### HTTP请求配置
- **基础URL**: 通过环境变量 `VUE_APP_API_URL` 配置
- **代理路径**: `/Api` (开发环境通过webpack代理)
- **请求方式**: 使用统一封装的axios实例
- **错误处理**: 统一的响应拦截和错误处理机制

### Mock数据规范
```javascript
// src/mock/data/example.js 标准格式
var Mockjs = require('mockjs');

const data = {
  Value: true,           // 请求是否成功
  InfoCode: 1,          // 业务状态码
  Info: '操作成功',      // 提示信息
  Data: null            // 业务数据
};

const exampleApi = function(app) {
  app.post('/Api/Example/Action', function(req, res) {
    res.json(data);
  });
};

module.exports = exampleApi;
```

### 环境配置规范
- **开发环境**: `.env.development` - Mock数据开关通过 `VUE_APP_MOCK` 控制
- **生产环境**: `.env.production` - 包含gzip压缩、代码优化等配置
- **API地址**: 开发环境使用 `https://cdkdev.kunlunjyk.com/`
- **端口配置**: 开发环境默认端口80

## 🎨 Vue组件开发规范

### 组件结构约束
- **页面组件**: 位于 `src/views/Pages/` 目录
- **公共组件**: 位于 `src/components/` 目录
- **命名规范**: 使用PascalCase命名组件文件和组件名
- **文件结构**: 单文件组件(.vue)，包含template、script、style三部分

### Vue 2 Options API规范
```javascript
// 标准组件结构
export default {
  name: 'ComponentName',
  components: {},
  props: {},
  data() {
    return {};
  },
  computed: {},
  watch: {},
  methods: {},
  created() {},
  mounted() {},
  destroyed() {}
};
```

### 样式规范
```vue
<style scoped lang="scss">
// 使用SCSS语法
// 必须添加scoped属性避免样式污染
// 可以使用全局变量和混入
@import "~@/styles/variable";
@import "~@/styles/mixin";
</style>
```

## 🔄 AI工具开发流程

### API开发流程
1. **分析需求**: 确定API功能和数据结构
2. **创建Mock**: 在 `src/mock/data/` 目录创建对应的Mock文件
3. **注册Mock**: 在 `src/mock/index.js` 中注册Mock接口
4. **封装API**: 创建对应的API调用函数
5. **集成调用**: 在组件或Vuex中使用API函数
6. **测试验证**: 确保Mock数据和接口调用正常

### 组件开发流程
1. **确定组件类型**: 页面组件或公共组件
2. **创建组件**: 按照命名规范创建.vue文件
3. **使用Options API**: 严格按照Vue 2 Options API结构编写
4. **样式编写**: 使用SCSS + scoped样式
5. **状态管理**: 通过Vuex管理组件状态
6. **路由配置**: 在router中配置页面路由

### 构建配置流程
1. **webpack配置**: 通过 `vue.config.js` 进行自定义配置
2. **别名配置**: 使用@、Components、Utils等别名
3. **代理配置**: 开发环境API代理配置
4. **优化配置**: 生产环境代码压缩、gzip等优化

## 🚫 AI工具使用限制

### 严格禁止的操作
- **启动项目**: 禁止使用 `npm run serve` 等启动命令
- **框架混用**: 禁止使用Vue 3语法或Composition API
- **依赖变更**: 禁止修改package.json或安装新依赖
- **配置修改**: 禁止修改vue.config.js等核心配置文件
- **环境变量**: 禁止修改.env文件中的环境配置
- **构建命令**: 禁止执行build、deploy等构建命令

### 代码质量约束
- 单个方法不超过50行代码
- 必须使用描述性的变量和函数名
- 复杂逻辑必须添加注释
- 遵循DRY原则，避免代码重复
- 组件必须使用scoped样式
- Mock数据必须包含完整的业务数据结构

### 环境和构建约束
- **开发环境**: 当前配置为development环境
- **应用类型**: APP_TYPE为1，代表企业端应用
- **主题配置**: 主题色为#0380c8
- **构建目录**: 生产环境构建到fleetcard_manage目录

## 📋 AI工具检查清单

### 代码生成前检查
- [ ] 确认使用Vue 2.6.11 + Options API
- [ ] 确认使用项目统一的HTTP请求方式
- [ ] 确认组件使用正确的命名规范
- [ ] 确认使用Vuex进行状态管理
- [ ] 确认Mock数据已正确配置
- [ ] 确认遵循项目目录结构
- [ ] 确认样式使用SCSS + scoped

### 代码生成后验证
- [ ] 检查语法是否符合Vue 2.6.11规范
- [ ] 检查API调用是否正确
- [ ] 检查样式是否使用SCSS + scoped
- [ ] 检查是否遵循项目目录结构
- [ ] 检查Vuex模块是否正确集成
- [ ] 检查Mock数据结构是否正确
- [ ] 检查路由配置是否正确

## 🎯 AI工具最佳实践

### 代码生成策略
1. **渐进式开发**: 先生成基础结构，再逐步完善功能
2. **模块化思维**: 将复杂功能拆分为多个小模块
3. **复用优先**: 优先使用现有组件和工具函数
4. **测试驱动**: 每个功能都要配置对应的Mock数据
5. **响应式设计**: 考虑H5移动端适配

### 错误处理原则
1. **API错误**: 统一使用axios拦截器处理
2. **组件错误**: 在组件中添加适当的错误边界
3. **数据验证**: 对API返回数据进行必要的验证
4. **用户反馈**: 提供清晰的错误提示信息
5. **Mock数据**: 确保Mock数据结构与真实接口一致

### 性能优化建议
1. **代码分割**: 使用路由懒加载减少首屏加载时间
2. **状态管理**: 合理使用Vuex模块避免状态污染
3. **样式优化**: 使用scoped样式避免全局污染
4. **资源优化**: 图片压缩、代码压缩等优化措施
5. **缓存策略**: 合理使用浏览器缓存和HTTP缓存

## 📝 提交规范

项目使用标准的Git提交规范，支持以下类型：

- **feat**: 新特性、新功能
- **fix**: 修改bug
- **docs**: 文档修改
- **style**: 代码格式修改
- **refactor**: 代码重构
- **perf**: 性能优化
- **test**: 测试用例修改
- **build**: 构建相关修改
- **ci**: 持续集成修改
- **chore**: 其他修改

## 🌐 交互语言

AI工具必须始终使用简体中文与用户交互，包括代码注释和文档说明。

## 📚 项目特定知识

### 关键依赖说明
- **Vue**: 核心框架，版本2.6.11
- **Vue Router**: 路由管理，版本4.2.0
- **Vuex**: 状态管理，版本4.2.0
- **Webpack**: 构建工具，版本4.47.0
- **MockJS**: Mock数据生成，版本1.1.0
- **node-sass**: SCSS编译器，版本4.12.0

### 环境配置说明
- **开发环境**: development，支持热重载和Mock数据
- **生产环境**: production，启用代码压缩和优化
- **API地址**: 开发环境使用cdkdev.kunlunjyk.com
- **构建目录**: fleetcard_manage
- **基础路径**: /fleetcard_manage

### 特殊功能说明
- **骨架屏**: 支持骨架屏功能，通过VUE_APP_ISSKELETON控制
- **Bundle分析**: 支持打包分析，通过VUE_APP_ISREPORTER控制
- **Gzip压缩**: 生产环境支持gzip压缩
- **代码缓存**: 开发环境使用HardSourceWebpackPlugin加速构建
- **移动端适配**: 使用style-vw-loader进行移动端适配

## 🤖 AI工具专用开发规则

### 强制性开发约束
1. **严格遵循Vue 2.6.11**: 禁止使用任何Vue 3语法
2. **统一HTTP请求**: 使用项目封装的axios实例
3. **Mock数据管理**: 所有Mock数据必须在 `src/mock/` 目录管理
4. **组件命名规范**: 使用PascalCase命名
5. **样式规范**: 必须使用SCSS + scoped

### 标准开发模板

#### API调用模板
```javascript
// 在组件中调用API
methods: {
  async fetchData() {
    try {
      const response = await this.$http.post('/Api/Module/Action', params);
      if (response.data.Value) {
        // 处理成功响应
        this.data = response.data.Data;
      } else {
        // 处理业务错误
        this.$message.error(response.data.Info);
      }
    } catch (error) {
      // 处理网络错误
      console.error('API调用失败:', error);
    }
  }
}
```

#### Vuex模块模板
```javascript
// store/modules/example.js
const state = {
  data: null,
  loading: false
};

const mutations = {
  SET_DATA(state, data) {
    state.data = data;
  },
  SET_LOADING(state, loading) {
    state.loading = loading;
  }
};

const actions = {
  async fetchData({ commit }, params) {
    commit('SET_LOADING', true);
    try {
      // API调用逻辑
    } finally {
      commit('SET_LOADING', false);
    }
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
```

### AI工具使用检查点
- [ ] 是否使用了正确的Vue 2.6.11语法
- [ ] 是否正确配置了Mock数据
- [ ] 是否遵循了组件命名规范
- [ ] 是否使用了scoped样式
- [ ] 是否正确配置了Vuex状态管理
- [ ] 是否遵循了项目目录结构
- [ ] 是否使用了统一的HTTP请求方式
- [ ] 是否添加了必要的错误处理
