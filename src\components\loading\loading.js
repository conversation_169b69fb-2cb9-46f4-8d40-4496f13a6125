/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-04-06 15:01:31
 * @LastEditors: Yongon
 * @LastEditTime: 2022-06-23 08:50:18
 * @Description: loading回传参数
 */
import Vue from 'vue'
import loadingComponent from './loading.vue'

const LoadingConstructor = Vue.extend(loadingComponent)

const instance = new LoadingConstructor({
  el: document.createElement('div')
})

instance.show = false // 默认隐藏
const loading = {
  show(msg, time) { // 显示方法
    instance.show = true
    instance.msg = msg || '迁移判断中，请稍等...'
    instance.time = time || 10000
    document.body.appendChild(instance.$el)
  },
  hide() { // 隐藏方法
    instance.show = false
  }
}

export default {
  install() {
    if (!Vue.$loading) {
      Vue.$loading = loading
    }
    Vue.mixin({
      created() {
        this.$loading = Vue.$loading
      }
    })
  }
}