<!--
 * @Author: Yongon
 * @Date: 2022-07-22 09:46:57
 * @LastEditors: Yongon
 * @LastEditTime: 2022-07-22 10:12:33
 * @Description: file content
-->
<template>
  <div>
    <!-- 开启顶部安全区适配 -->
    <van-nav-bar safe-area-inset-top />
    <van-nav-bar title="测试页" left-text="返回首页" left-arrow @click-left="onClickLeft" />

    <van-cell-group>
      <van-cell title="姓名" :value="userInfo.name" />
      <van-cell title="年龄" :value="userInfo.age" />
    </van-cell-group>
  </div>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {
        name: null,
        age: 18,
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    onClickLeft() {
      this.$router.replace({ path: "/gasstationmap" });
    },
  },
};
</script>

<style lang="scss"></style>
