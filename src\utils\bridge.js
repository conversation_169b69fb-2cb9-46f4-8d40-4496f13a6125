/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-04-19 17:10:20
 * @LastEditors: Yongon
 * @LastEditTime: 2022-07-21 16:44:48
 * @Description: vue与原生端通信，桥接处理
 */
import { getUserAgent } from "Utils/common/";

const cppeiBridge = function(method, params) {
  let userAgent = getUserAgent();
  // let isAndroid = u.indexOf("Android") > -1 || u.indexOf("Linux") > -1; //g
  let isAndroid = userAgent?.type == "Android" ? true : false;
  // let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
  if (isAndroid) {
    return android[method](JSON.stringify(params));
  } else {
    return window.webkit.messageHandlers[method].postMessage(JSON.stringify(params));
  }
};

export default cppeiBridge;
