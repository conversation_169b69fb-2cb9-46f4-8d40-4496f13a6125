<!--
 * @Author: Yongon
 * @Date: 2022-08-03 15:30:58
 * @LastEditors: ltw <EMAIL>
 * @LastEditTime: 2024-01-11 09:21:41
 * @Description: 百度地图
-->
<template>
  <baidu-map v-bind="$attrs" class="map" :zoom="15" :center="{ lng: currentOilStation.posx, lat: currentOilStation.posy }" :ak="selfKey" :scroll-wheel-zoom="true" @ready="mapReady">
    <bm-info-window :position="{ lng: currentOilInfo.posx, lat: currentOilInfo.posy }" :title="currentOilInfo.stationName" :show="infoWindow.show" @close="infoWindowClose">
      <p>详细地址:{{ currentOilInfo.address }}</p>
      <p>联系电话:{{ currentOilInfo.stationPhone }}</p>
      <p>营业时间:{{ currentOilInfo.openingTime }}-{{ currentOilInfo.closingTime }}</p>
      <div class="flex">
        <van-button class="orderOil" type="default" @click="goMapNav(currentOilInfo)">去这里</van-button>
        <van-button
          class="orderOil"
          type="default"
          @click="
            infoWindowClose();
            goOiller(currentOilInfo);
          "
          >预授权加油</van-button
        >
      </div>
    </bm-info-window>
    <bm-marker v-for="(item, index) in center" :key="index" :position="{ lng: item.posx, lat: item.posy }" :dragging="false" animation="BMAP_ANIMATION_BOUNCE">
      <bm-label :content="item.stationName" :offset="{ width: -35, height: 30 }" :labelStyle="labelStyle" @click="infoWindowOpen(item)"></bm-label>
    </bm-marker>
    <bm-navigation type="BMAP_NAVIGATION_CONTROL_SMALL" anchor="BMAP_ANCHOR_TOP_RIGHT" :offset="{ width: 20, height: 110 }"></bm-navigation>
  </baidu-map>
</template>

<script>
// import BaiduMap from "vue-baidu-map/components/map/Map.vue";
import { BaiduMap, BmGeolocation, BmLocalSearch, BmNavigation, BmMarker, BmView, BmLabel, BmInfoWindow } from "vue-baidu-map";
import eventBus from "../_service/eventbus.js";
import { storage } from "Utils/common";
import { recordJsLog } from "@/utils/recordJsLog";

export default {
  name: "",
  components: {
    // 地图
    BaiduMap,
    // 手动定位控件
    BmGeolocation,
    // 检索控件
    BmLocalSearch,
    // 地图放大缩小控件
    BmNavigation,
    // marker控件
    BmMarker,
    BmView,
    BmLabel,
    BmInfoWindow,
  },
  data() {
    return {
      // 百度地图key
      selfKey: _const.selfKey,
      // 地图你解析方法实例
      myGeo: null,
      // 搜索关键字
      keyword: "",
      currentOilInfo: "", //在地图上点击标记的详细信息
      infoWindow: {
        show: false,
        contents: "Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
      },
      labelStyle: { color: "red", fontSize: "14px" },
    };
  },
  props: {
    center: {
      type: Array,
      default: () => {
        return [
          {
            posx: "116.39752",
            posy: "39.908754",
          },
        ];
      },
    },
    currentOilStation: {
      type: Object,
      default: () => {
        return [
          {
            posx: "116.39752",
            posy: "39.908754",
          },
        ];
      },
    },
  },
  watch: {},
  computed: {},
  methods: {
    goOiller(item) {
      console.log("23123123");
      eventBus.$emit("currentOilNo", item);
      //调用router回退页面
      this.$router.push({
        path: "noBelowRefuel",
        query: "",
      });
    },
    goMapNav(item) {
      recordJsLog('startNavi', {
        // startLon: this.deviceInfo.lon,
        // startLat: this.deviceInfo.lat,
        startLon: storage.ss.get("deviceInfo")?.lon || 116.407718,
        startLat: storage.ss.get("deviceInfo")?.lat || 39.920248,
        endLon: Number(item.posx),
        endLat: Number(item.posy),
      });

      // alert("准备唤起百度导航" + item.posx + "   " + item.posy);
      this.$cppeiBridge("startNavi", {
        // startLon: this.deviceInfo.lon,
        // startLat: this.deviceInfo.lat,
        startLon: storage.ss.get("deviceInfo")?.lon || 116.407718,
        startLat: storage.ss.get("deviceInfo")?.lat || 39.920248,
        endLon: Number(item.posx),
        endLat: Number(item.posy),
      });
    },
    infoWindowClose(e) {
      this.infoWindow.show = false;
      console.log(11 + "   " + this.infoWindow.show);
    },
    infoWindowOpen(item) {
      this.currentOilInfo = item;
      this.infoWindow.show = true;
      console.log(22 + "   " + this.infoWindow.show,);
    },
    // clear() {
    //   this.infoWindow.contents = "";
    // },
    mapReady({ BMap, map }) {
      // new Promise((resolve, reject) => {
      //   // 获取自动定位方法
      //   var geolocation = new BMap.Geolocation();
      //   // 获取逆解析方法实例
      //   this.myGeo = new BMap.Geocoder();
      //   // 获取自动定位获取的坐标信息
      //   geolocation.getCurrentPosition(
      //     (r) => {
      //       this.center = {
      //         lng: r.point.lng,
      //         lat: r.point.lat,
      //       };
      //       this.point = {
      //         lng: r.point.lng,
      //         lat: r.point.lat,
      //       };
      //       // if(this.$attrs.mapData){
      //       //   this.$emit('GetNavigationList',this.center)
      //       // }
      //     },
      //     { enableHighAccuracy: true }
      //   );
      // });
    },
    // getClickInfo(e) {
    //   console.log(e.point.lng);
    //   console.log(e.point.lat);
    //   this.center.lng = e.point.lng;
    //   this.center.lat = e.point.lat;
    // },
  },
  created() {},
  mounted() {},
};
</script>
<style lang="scss" scoped>
.map {
  width: 100vw;
  height: 100vh;
}
</style>
