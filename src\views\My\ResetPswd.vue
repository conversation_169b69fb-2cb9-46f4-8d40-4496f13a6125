<template>
  <div class="reset-pswd">
    <nav-bar title="修改账户密码" />
    <div class="reset-card">
      <van-form ref="form" validate-first label-width="70" :show-error-message="false" :show-error="false" @submit="onSubmit">
        <van-field v-model="pwdCurrentmd" label="原密码" clearable type="password" placeholder="请输入原密码" maxlength="20" :rules="[{ validator: validatePwdCurrent, trigger: 'onBlur' }]"></van-field>
        <van-field v-model="pwdNewmd" label="新密码" clearable type="password" placeholder="6-20位字母数字组合" maxlength="20" :rules="[{ validator: validatePwdNew, trigger: 'onBlur' }]"></van-field>
        <van-field v-model="pwdNewCheck" label="确认新密码" clearable type="password" placeholder="请再次输入新密码" maxlength="20" :rules="[{ validator: validateSame, trigger: 'onBlur' }]"></van-field>
        <!-- 错误提示 -->
        <div class="err-tips">{{ errorMsg }}</div>
      </van-form>
    </div>
    <!-- 完成按钮 -->
    <div class="btn-wrap">
      <van-button block round :color="$setting.themeColor" size="large" @click="doResetPswd" :disabled="isDisabled"> 保&nbsp;存</van-button>
    </div>
  </div>
</template>

<script>
import navBar from "@/components/navBar/NavHeader";
import { Toast } from "vant";
import { storage } from "Utils/common/";
// 后端接口
import { resetPswd } from "./_service/";
import md5 from "js-md5";

export default {
  props: {},
  components: {
    navBar,
  },
  data() {
    return {
      // 表单数据
      user: {
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        phone: storage.ss.get("driverInfo").phone,
        userid: "1",
        pwdCurrent: "",
        pwdNew: "",
        // 1 企业端 2 司机端
        channel: this.$setting.APP_TYPE + "",
      },
      pwdCurrentmd: "",
      pwdNewmd: "",
      pwdNewCheck: "",
      // 错误提示
      errorMsg: "",
    };
  },
  computed: {
    isDisabled() {
      return this.pwdCurrentmd && this.pwdNewmd && this.pwdNewCheck ? false : true;
    },
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    /**
     * @description: 表单校验
     * @return {*}
     */
    validatePwdCurrent(val) {
      if (!val) {
        this.errorMsg = "*原密码不能为空";
        return false;
      } else {
        this.errorMsg = "";
        return true;
      }
    },
    validatePwdNew(val) {
      if (!val) {
        this.errorMsg = "*新密码不能为空";
        return false;
      } else {
        let reg = /^(?!\D+$)(?![^a-zA-Z]+$)\S{6,20}$/;
        this.errorMsg = reg.test(val) ? "" : "*请填写6-20位字母数字组合";
        return reg.test(val);
      }
    },
    validateSame(val) {
      if (val != this.pwdNewmd) {
        this.errorMsg = "*两次密码输入不一致";
        return false;
      } else {
        this.errorMsg = "";
        return true;
      }
    },
    doResetPswd() {
      this.$refs.form.submit();
    },
    onSubmit() {
      this.user.pwdNew = md5(this.pwdNewmd);
      this.user.pwdCurrent = md5(this.pwdCurrentmd);
      resetPswd(this.user)
        .then((res) => {
          if (1 === res.infoCode) {
            Toast("修改账户密码成功");
            setTimeout(() => {
              this.$router.go(-2);
            },1000);
          } else {
            Toast(res.info);
          }
        })
        .catch((err) => {
          Toast(err);
        });
    },
  },
};
</script>

<style scoped lang="scss">
.reset-pswd {
  .reset-card {
    height: 46vw;
    background: #fff;
    margin: 0 20px;
    margin-top: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 12px;

    .icon {
      width: 20px;
      height: 18px;
      vertical-align: sub;
    }

    .err-tips {
      color: #ee0a24;
      // line-height: 1.4;
      padding: 30px 30px 0;
      text-align: left;
      min-height: 20px;
      font-size: 24px;
    }

    /deep/ .van-cell {
      background: none;
    }
  }

  .btn-wrap {
    padding: 0 20px;
    margin: 0 20px;
    margin-top: 50px;

    // position: relative;
    .van-button--disabled {
      // 按钮透明度
      // opacity: 1;
    }
  }

  .check-box {
    margin-top: 80px;
    display: flex;
    justify-content: center;

    .text {
      color: #f37f06;
      cursor: pointer;
    }
  }
}
</style>
