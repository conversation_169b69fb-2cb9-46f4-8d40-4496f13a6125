<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title></title>
<!--    <style>-->
<!--      @keyframes van-rotate {-->
<!--        from {-->
<!--          transform: rotate(0deg);-->
<!--        }-->

<!--        to {-->
<!--          transform: rotate(360deg);-->
<!--        }-->
<!--      }-->
<!--      @-moz-keyframes van-rotate {-->
<!--        from {-->
<!--          transform: rotate(0deg);-->
<!--        }-->

<!--        to {-->
<!--          transform: rotate(360deg);-->
<!--        }-->
<!--      }-->
<!--      @-webkit-keyframes van-rotate {-->
<!--        from {-->
<!--          transform: rotate(0deg);-->
<!--        }-->

<!--        to {-->
<!--          transform: rotate(360deg);-->
<!--        }-->
<!--      }-->
<!--      @-o-keyframes van-rotate {-->
<!--        from {-->
<!--          transform: rotate(0deg);-->
<!--        }-->

<!--        to {-->
<!--          transform: rotate(360deg);-->
<!--        }-->
<!--      }-->
<!--    </style>-->
  </head>

  <body>
    <noscript>
      <strong>We're sorry but vue-app-framework doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->

    <!-- cdn引入vue全家桶-生产环境使用 -->
<!--    <script src="https://cdn.bootcss.com/vue/2.6.11/vue.min.js"></script>-->
<!--    <script src="https://cdn.bootcss.com/vuex/3.1.2/dist/vuex.min.js"></script>-->
<!--    <script src="https://cdn.bootcss.com/vue-router/3.1.3/dist/vue-router.min.js"></script>-->

    <!-- 当cdn崩溃了 引用本地静态文件 -->
    <script>
      window.Vue || document.write('<script src="./static/vue.min.js"><\/script>');
      window.VueRouter || document.write('<script src="./static/vue-router.min.js"><\/script>');
      window.Vuex || document.write('<script src="./static/vuex.min.js"><\/script>');
    </script>

    <!-- 解决vw的兼容性问题 -->
<!--    <script src="//g.alicdn.com/fdilab/lib3rd/viewport-units-buggyfill/0.6.2/??viewport-units-buggyfill.hacks.min.js,viewport-units-buggyfill.min.js"></script>-->
    <!-- 本地引入vw-hack -->
    <script>
      window.viewportUnitsBuggyfill || document.write('<script src="./static/viewport-units-buggyfill.hacks.js"><\/script>');
    </script>
    <script>
      window.onload = function() {
        window.viewportUnitsBuggyfill.init({
          hacks: window.viewportUnitsBuggyfillHacks,
        });
      };
    </script>
    <!-- 针对active样式的兼容性 -->
    <script>
      document.body.addEventListener('touchstart', function() {});
    </script>
  </body>
</html>
