/** 
    全局宏mixin定义
    基础的常用的写进来 方便维护
*/

/* 基础相关 */
// /水平居中/ 
@mixin  margin($top:0,$right:0,$bottom:0,$left:0) {
  @if($left==0&&$right==0){
    margin:$top auto;
  }@else{
    margin:$top $right $bottom $left;

  }
}
/*
    宽高
    @param $width css宽度
    @param $height css高度
*/
@mixin wh($width, $height) {
  width: $width;
  height: $height;
}

// 清除浮动
@mixin clear {
  &:after {
    content: ".";
    clear: both;
    display: block;
    width: 0;
    height: 0;
    visibility: hidden;
  }
}

// 点击div,span等背后出现蒙层,推荐使用rgba来描述颜色
@mixin bgActive($color: rgba(242, 243, 245, .2)) {
  user-select: none;

  &:active {
    background-color: $color !important;
  }
}

// 添加阴影效果这里先默认添加底部
@mixin addShadow($x: 0, $y:2px, $r:5px, $s:2px, $color:#c9c9c9) {
  box-shadow: $x, $y, $r, $s, $color;
}

// 下部分割线
@mixin division($height, $color) {
  position: relative;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    display: block;
    width: 100%;
    height: $height;
    background-color: $color;
  }
}

// 上部分割线
@mixin division-up($height, $color) {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    width: 100%;
    height: $height;
    background-color: $color;
  }
}

// 局部使用浏览器自带滚动条
@mixin useScrollByBrowser($h: 1px) {
  overflow-x: hidden;
  overflow-y: auto;
  height: calc(100% - #{$h});
  box-sizing: border-box;

  &::-webkit-scrollbar {
    display: none;
  }

  -webkit-overflow-scrolling:touch;
}

// 几行后显示省略号
$mh:30px;
$col:2;

@mixin ellipsis($mh:$mh, $col:$col) {
  display: -webkit-box;
  overflow: hidden;
  max-height: $mh;

  -webkit-box-orient: vertical;
  -webkit-line-clamp: $col;
}

/* flex相关 */

/*
    flex 水平垂直居中
    @param $direction 方向，具体参考flex布局
*/
@mixin fcc($direction: row) {
  display: flex;
  flex-direction: $direction;
  justify-content: center;
  align-items: center;
}

// flex 上下居中
@mixin ftbc {
  display: flex;
  align-items: center;
}

// flex 左右居中
@mixin flrc {
  display: flex;
  justify-content: center;
}

// flex 两边
@mixin flb {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

// flex row,靠两边
$d:row;

@mixin frsb($d:$d) {
  display: flex;
  flex-direction: $d;
  justify-content: space-between;
}

/*
    flex 基础布局
    @param $direction 方向，具体参考flex布局
    @param $wrap 是否换行，具体参考flex布局
*/
@mixin flex($direction: row, $wrap :nowrap) {
  display: flex;
  flex-direction: $direction;
  flex-wrap: $wrap;
}

/*
    1px border
    @param $direction 方向，bottom,top,right,left,若为全边框则传(bottom,top,right,left)
    @param $color 边框颜色 默认#333
    @param $radius 四个角圆角值 默认值无
    @param $position css伪类 after或before 默认after
*/
@mixin border-1px($directionMaps: bottom, $color:#333, $radius:(0, 0, 0, 0), $position: after) {
  // 是否只有一个方向
  $isOnlyOneDir: string==type-of($directionMaps);

  @if ($isOnlyOneDir) {
    $directionMaps: ($directionMaps);
  }

  // 循环部署border
  @each $directionMap in $directionMaps {
    border-#{$directionMap}: 1px solid $color;
  }

  // 判断圆角是list还是number
  @if (list==type-of($radius)) {
    border-radius: nth($radius, 1) nth($radius, 2) nth($radius, 3) nth($radius, 4);
  }

  @else {
    border-radius: $radius;
  }

  // 像素比 2
  @media only screen and (-webkit-min-device-pixel-ratio: 2) {
    & {
      position: relative;

      // 删除1像素密度比下的边框
      @each $directionMap in $directionMaps {
        border-#{$directionMap}: none;
      }
    }

    &:#{$position} {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      display: block;
      box-sizing: border-box;
      width: 200%;
      height: 200%;
      padding: 1px;
      border: 0 solid $color;
      transform: scale(.5);
      transform-origin: 0 0;
      pointer-events: none;

      @each $directionMap in $directionMaps {
        border-#{$directionMap}-width: 1px;
      }

      // 判断圆角是list还是number
      @if (list==type-of($radius)) {
        border-radius: nth($radius, 1) * 2 nth($radius, 2) * 2 nth($radius, 3) * 2 nth($radius, 4) * 2;
      }

      @else {
        border-radius: $radius * 2;
      }
    }
  }

  // 像素比 3
  @media only screen and (-webkit-min-device-pixel-ratio: 3) {
    &:#{$position} {
      width: 300%;
      height: 300%;
      transform: scale(.3333);

      // 判断圆角是list还是number
      @if (list==type-of($radius)) {
        border-radius: nth($radius, 1) * 3 nth($radius, 2) * 3 nth($radius, 3) * 3 nth($radius, 4) * 3;
      }

      @else {
        border-radius: $radius * 3;
      }
    }
  }
}

/*
    1px border
*/
$border-poses:top,
right,
bottom,
left;
$color:red;
$style:solid;
$borderRadius:0;

@mixin border-1px-new($borderRadius, $color:$color, $style:$style, $poses:$border-poses) {
  position: relative;

  &::after {
    content: '';
    display: block;
    position: absolute;
    width: 100%;

    @if $borderRadius != 0 {
      border-radius: $borderRadius;
    }


    @each $pos in $poses {
      border-#{$pos}: 1px $style $color;
      #{$pos}: 0;
    }
  }
}

@media (-webkit-min-device-pixel-ratio:1.5),
(min-device-pixel-ratio: 1.5) {
  .border-1px &::after {
    -webkit-transform: scaleY(0.7);
    transform: scaleY(0.7);
  }
}

@media (-webkit-min-device-pixel-ratio:2),
(min-device-pixel-ratio: 2) {
  .border-1px &::after {
    -webkit-transform: scaleY(0.5); //像素比为2的时候，我们设置缩放为0.5
    transform: scaleY(0.5);
  }
}

@media (-webkit-min-device-pixel-ratio:3),
(min-device-pixel-ratio: 3) {
  .border-1px &::after {
    -webkit-transform: scaleY(0.333333); //像素比为3的时候，我们设置缩放为0.33333
    transform: scaleY(0.333333);
  }
}
// ***************        短信发送dialog通用样式           ***************************
@mixin sms-tip(){
  margin-top: 10px;
  padding: 10px;
  font-size: 12pt;
  text-align: left;
}
@mixin count-time() {
  display: flex;
  flex-direction: row;
  width: 90px;
  height: 34px;
  align-items: center;
  justify-content: center;
  background: #eeeeee;
  border-radius: 5px;
}
@mixin count-info() {
  color: #ff6602;
  font-size: 14px;
  margin-right: 3px;
}
@mixin send-btn() {
  display: flex;
  flex-direction: row;
  width: 90px;
  height: 34px;
  align-items: center;
  justify-content: center;
  background: $commonBtnColor;
  border-radius: 5px;
  color: white;
  font-size: 13px;
}
// ***************        短信发送dialog通用样式           ***************************


@media (-webkit-min-device-pixel-ratio:1.5),
(min-device-pixel-ratio: 1.5) {
  .border-1px &::after {
    -webkit-transform: scaleY(0.7);
    transform: scaleY(0.7);
  }
}

@media (-webkit-min-device-pixel-ratio:2),
(min-device-pixel-ratio: 2) {
  .border-1px &::after {
    -webkit-transform: scaleY(0.5); //像素比为2的时候，我们设置缩放为0.5
    transform: scaleY(0.5);
  }
}

@media (-webkit-min-device-pixel-ratio:3),
(min-device-pixel-ratio: 3) {
  .border-1px &::after {
    -webkit-transform: scaleY(0.333333); //像素比为3的时候，我们设置缩放为0.33333
    transform: scaleY(0.333333);
  }
}
/**
 * @description: 组件公共样式混入
 * @return {*}
 */
@mixin pos {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}
@mixin oilPosTit {
  text-align: left;
  color: #333333;
  padding-left: 10px;
}
@mixin oilPosTitP {
  font-size: 32px;
  margin: 0 auto 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}