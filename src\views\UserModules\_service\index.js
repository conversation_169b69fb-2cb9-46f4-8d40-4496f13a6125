/*
 * @Author: <PERSON>on
 * @Date: 2022-06-08 14:22:27
 * @LastEditors: ltw <EMAIL>
 * @LastEditTime: 2024-04-26 17:24:28
 * @Description: file content
 */
import request from 'Utils/http/'; // 导入axios封装函数


// 第一个false,代表不加载loading图标
// 登录发送短信验证码
export async function sendCode(params) {
  return request('post', '/card/appUserSms/v1/getLoginSms', params, false, true, true, false, true)
}

// 修改密码发送短信验证码
export async function sendCode4ForgetPswd(params) {
  return request('post', '/card/appUserSms/v1/getLoginPasswordSms', params, false, true, true, false, true)
}
// 修改密码下一步校验短信验证码
export async function checkCode4ForgetPswd(params) {
  return request('post', '/card/appUserSms/v1/checkLoginPasswordBySms', params, false, true, true, false, true)
}

// 短信验证码登录
export async function loginBySms(params) {
  return request('post', '/user/v1/loginBySms', params, true, true, true, false, true)
}

// 账号密码登录
export async function loginByAccount(params) {
  return request('post', '/user/v1/loginByAccount', params, true, true, true, false, true)
}

// 弃用
export async function findLoginPassword(params) {
  return request('post', '/appUser/v1/findLoginPassword', params, true, true, true, false, true)
}

// 修改登录密码(初始设置密码)
export async function updatePswd(params) {
  return request('post', '/card/appUser/v1/updatePassword', params, true, true, true, false, true)
}

// 重置登录密码
export async function resetPswd(params) {
  return request('post', '/user/v1/resetPassword', params, true, true, true, false, true)
}

// 获取未激活企业列表
export async function getUnActivatedList(params) {
  return request('post', '/user/v1/getUnActivatedList', params, true, true, true, false, true)
}

// 获取已开通未激活企业
export async function getOpenCardList(params) {
  return request('post', '/card/appUser/v1/getOpenCardList', params, true, true, true, false, true)
}

// 获取图片验证码随机字符串
export async function getCaptchaText(params) {
  return request('post', '/card/appUser/v1/getCaptchaText',params , true, true, true, false, true)
  // return request('get', '/card/appUser/v1/getCaptchaText?deviceName=' + deviceId, {}, true, true, true, false, true)
}

// 查询手机号是否存在
export async function isPhoneExist(params) {
  return request('post', '/user/v1/isPhoneExist', params, true, true, true, false, true)
}

export async function getCompanyInfoByCardNo(params) {
  return request('post', '/card/appUser/v1/getCompanyInfoByCardNo', params, true, true, true, false, true)
}
// 使用token获取用户信息 保持登录
export async function GetUserInfoByToken(params) {
  return request('post', '/user/v1/getUserInfoByToken', params, true, true, true, false, true)
}

// 登录之后查询绑定设备关系 单点登录
export async function isChangeEquipment(params) {
  return request('post', '/user/v1/isChangeEquipment', params, true, true, true, false, true)
}

// 换设备验证成功之后进行设备绑定 单点登录
export async function bindUserEquipment(params) {
  return request('post', '/user/v1/bindUserEquipment', params, true, true, true, false, true)
}

// 软件更新
export async function GetAppVersion(params) {
  return request('post', '/user/v1/getAppVersion', params, true, true, true, false, true)
}

// 车队卡司机/企业端登录风控接口【新增】 雷天伟
export async function loginRiskManagement(params) {
  return request('post', '/appcenter/riskManagement/v1/loginRiskManagement', params, true, true, true, false, true)
}