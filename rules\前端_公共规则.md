---
description: 前端公共规则
globs: .ts,.vue,.js,.less
alwaysApply: true
---

### 总体描述
该文档对前端项目进行了全面概述，涵盖前端所用的技术栈、工程结构等信息，为前端项目的整体架构和开发提供清晰的指引。

#### 应用范围
本文档适用于所有前端项目的开发人员、项目经理和相关技术人员，帮助他们了解前端项目的整体架构、技术栈。

#### 使用要求
开发人员在进行前端项目开发时，需要参考本文档中的技术栈和工程结构进行项目搭建。

#### 规则1 技术栈

列出前端开发所依赖的核心技术框架、工具及库，明确项目技术选型。

应用范围：

如何使用，使用要求

示例：

- vue3 + TypeScript
- Vite构建
- 路由：Vue-router
- 状态管理：Pinia
- 网络请求工具：Axios
- 样式：Less
- UI库：element-plus

#### 规则2 工程结构

展示前端项目的目录层级与文件分类，明确各目录的功能定位。

应用范围：

如何使用，使用要求

示例：

```
project-name                 # 项目名称
├── public/                  # 静态资源
├── src/
│   ├── api/                 # API接口
│   ├── assets/              # 资源文件
│   ├── components/          # 公共组件
│   ├── composables/         # 组合式函数
│   ├── config/              # 配置文件
│   ├── directives/          # 自定义指令
│   ├── enums/               # 枚举定义
│   ├── hooks/               # 自定义Hooks
│   ├── lang/                # 国际化资源
│   ├── router/              # 路由配置
│   ├── views/               # 页面组件
│   ├── stores/              # Pinia状态管理
│   ├── types/               # TS类型定义
│   ├── utils/               # 工具函数
│   ├── App.vue              # 根组件
│   └── main.js              # 入口文件
├── index.html               # 入口HTML
└── package.json             # 依赖配置
```

#### 规则3.命名规范

##### 3.1 组件命名

- 单文件组件（SFC）采用大驼峰命名法，例如`UserProfile.vue`。
- 目录名采用短横线命名法，例如`user-profile/`

##### 3.2 JS 文件命名

- 工具函数文件用小写字母加下划线，例如`date_utils.js`。
- 路由文件使用`routes.js`。
- 状态管理文件使用`store.js`。

##### 3.3 CSS/SCSS 文件命名

- 全局样式文件用`global.scss`。
- 组件样式文件用`component-name.scss`。

#### 规则4.代码风格规范

##### 4.1 HTML 模板规范

- 属性采用短横线命名法，例如`v-bind:is-loading="isLoading"`。
- 指令缩写统一使用`:`代替`v-bind:`，`@`代替`v-on:`。
- 组件嵌套层级不超过 4 层。

##### 4.2 JavaScript 规范

- 使用 ES6 + 语法，如箭头函数、解构赋值、扩展运算符。

- 每行代码长度不超过 120 个字符。

- 注释遵循 JSDoc 规范

  ```typescript
  /**
   * 获取用户信息
   * @param {string} userId - 用户ID
   * @returns {Promise<UserInfo>} 用户信息
   */
  async function getUserInfo(userId) {
    // ...
  }
  ```

##### 4.3 CSS 规范

- 使用 SCSS 或 CSS Modules。
- 类名采用 BEM 命名法，例如`.user-profile__avatar`。
- 避免使用内联样式。