---
description: 中油智行车队卡司机端H5前端公共开发规则
globs: .vue,.js,.scss
alwaysApply: true
---

# 前端公共开发规则

## 📋 总体描述

本文档定义了中油智行车队卡司机端H5项目的前端公共开发规则，包括技术栈规范、工程结构、编码规范、命名规范等核心内容，确保代码质量和团队协作效率。

## 🎯 应用范围

- **所有前端开发人员**：必须严格遵循本规范
- **代码审查人员**：以本规范为审查标准
- **新团队成员**：入职培训必学内容
- **项目维护人员**：代码维护和重构参考

## 📖 使用要求

1. **强制执行**：所有代码必须符合本规范要求
2. **代码审查**：不符合规范的代码不允许合并
3. **持续改进**：规范随项目发展持续优化
4. **团队共识**：规范变更需要团队讨论确认

## 🏗️ 规则1：项目技术栈

### 核心技术选型

**应用范围**：整个项目的技术基础架构

**使用要求**：严格按照以下技术栈进行开发，不得随意更换

**技术栈清单**：

- **Vue.js**: 2.6.11 (渐进式JavaScript框架)
- **Vue CLI**: 4.2.0 (Vue.js标准工具链)
- **Vue Router**: 3.1.5 (官方路由管理器)
- **Vuex**: 3.1.2 (集中式状态管理)
- **Webpack**: 4.47.0 (模块打包器)
- **Axios**: 0.19.2 (HTTP客户端库)
- **SCSS**: CSS预处理器
- **Vant**: 2.12.48 (移动端UI组件库)

### 开发工具链

- **包管理器**: npm (推荐) 或 yarn
- **代码编辑器**: VSCode + Vetur插件
- **调试工具**: Chrome DevTools + VConsole
- **版本控制**: Git + GitLab/GitHub

## 📁 规则2：工程结构规范

### 目录结构定义

**应用范围**：整个项目的文件组织结构

**使用要求**：严格按照以下目录结构组织代码，不得随意创建或修改目录

**标准目录结构**：

```text
petro-soti-zyzxcdkdriverfront-h5/    # 项目根目录
├── public/                          # 静态资源目录
│   ├── favicon.ico                  # 网站图标
│   ├── index-dev.html               # 开发环境HTML模板
│   ├── index-prod.html              # 生产环境HTML模板
│   └── static/                      # 静态文件
├── src/                             # 源代码目录
│   ├── App.vue                      # 根组件
│   ├── main.js                      # 应用入口文件
│   ├── assets/                      # 资源文件
│   │   ├── images/                  # 图片资源
│   │   ├── styles/                  # 全局样式
│   │   └── font/                    # 字体文件
│   ├── components/                  # 公共组件
│   ├── views/                       # 页面组件
│   ├── router/                      # 路由配置
│   ├── store/                       # Vuex状态管理
│   ├── utils/                       # 工具函数
│   ├── config/                      # 配置文件
│   ├── filters/                     # 过滤器
│   └── skeleton/                    # 骨架屏
├── .env.development                 # 开发环境变量
├── .env.production                  # 生产环境变量
├── vue.config.js                    # Vue CLI配置
├── postcss.config.js                # PostCSS配置
├── babel.config.js                  # Babel配置
└── package.json                     # 项目依赖配置
```

### 目录功能说明

| 目录 | 功能描述 | 文件类型 |
|------|----------|----------|
| `src/components/` | 公共组件，可复用的UI组件 | .vue文件 |
| `src/views/` | 页面组件，业务页面 | .vue文件 |
| `src/utils/` | 工具函数，通用方法 | .js文件 |
| `src/assets/styles/` | 全局样式，变量和混入 | .scss文件 |
| `src/router/` | 路由配置，页面路由定义 | .js文件 |
| `src/store/` | 状态管理，全局状态 | .js文件 |

## 📝 规则3：命名规范

### 3.1 组件命名规范

**应用范围**：所有Vue单文件组件

**命名规则**：
- **组件文件**：使用PascalCase命名法
- **组件目录**：使用PascalCase命名法
- **组件name属性**：与文件名保持一致

**正确示例**：
```javascript
// 文件：src/components/NavBar/NavHeader.vue
export default {
  name: 'NavHeader',
  // ...
}

// 文件：src/views/UserModules/Authority.vue
export default {
  name: 'Authority',
  // ...
}
```

**错误示例**：
```javascript
// ❌ 错误：使用了kebab-case
// 文件：nav-header.vue
// ❌ 错误：name与文件名不一致
export default {
  name: 'navHeader',
  // ...
}
```

### 3.2 JavaScript文件命名

**应用范围**：所有.js文件

**命名规则**：
- **工具函数**：使用camelCase命名法
- **配置文件**：使用camelCase命名法
- **路由文件**：固定命名`routes.js`、`index.js`
- **状态管理**：固定命名`index.js`

**正确示例**：
```text
src/utils/bangbangEncrypt.js      # 工具函数
src/utils/recordJsLog.js          # 工具函数
src/config/setting.js             # 配置文件
src/router/routes.js               # 路由配置
src/store/index.js                 # 状态管理
```

### 3.3 样式文件命名

**应用范围**：所有SCSS文件

**命名规则**：
- **全局样式**：使用下划线前缀，如`_variable.scss`
- **组件样式**：在Vue组件内部使用`<style>`标签
- **工具样式**：使用下划线前缀，如`_mixin.scss`

**正确示例**：
```text
src/assets/styles/_variable.scss   # 全局变量
src/assets/styles/_mixin.scss      # 全局混入
```

### 3.4 变量命名规范

**JavaScript变量**：
- **普通变量**：camelCase，如`userName`、`isLoading`
- **常量**：UPPER_SNAKE_CASE，如`API_BASE_URL`
- **私有变量**：下划线前缀，如`_privateMethod`

**CSS类名**：
- **BEM命名法**：`block__element--modifier`
- **组件类名**：与组件名相关，如`.nav-header`
- **工具类名**：语义化命名，如`.text-center`

**正确示例**：
```javascript
// JavaScript变量
const userName = 'admin';
const API_BASE_URL = 'https://api.example.com';
const _privateData = {};

// CSS类名
.nav-header { }
.nav-header__logo { }
.nav-header__logo--active { }

## 🎨 规则4：代码风格规范

### 4.1 Vue模板规范

**应用范围**：所有Vue组件的template部分

**编写规则**：

1. **属性命名**：使用kebab-case命名法
2. **指令缩写**：统一使用缩写形式
3. **组件嵌套**：不超过4层嵌套
4. **属性顺序**：按照Vue官方推荐顺序

**正确示例**：
```vue
<template>
  <div class="user-profile">
    <!-- 使用kebab-case属性名 -->
    <van-button
      :is-loading="isLoading"
      @click="handleClick"
      type="primary"
      size="large"
    >
      提交
    </van-button>

    <!-- 条件渲染 -->
    <div v-if="showContent" class="content">
      <span>{{ userName }}</span>
    </div>
  </div>
</template>
```

**错误示例**：
```vue
<template>
  <div class="user-profile">
    <!-- ❌ 错误：使用了v-bind和v-on完整形式 -->
    <van-button
      v-bind:isLoading="isLoading"
      v-on:click="handleClick"
    >
      提交
    </van-button>
  </div>
</template>
```

### 4.2 JavaScript规范

**应用范围**：所有JavaScript代码

**编写规则**：

1. **语法版本**：使用ES6+语法特性
2. **代码长度**：每行不超过120个字符
3. **注释规范**：使用JSDoc格式
4. **函数定义**：优先使用箭头函数

**正确示例**：
```javascript
export default {
  name: 'UserProfile',
  data() {
    return {
      userName: '',
      isLoading: false
    };
  },
  methods: {
    /**
     * 获取用户信息
     * @param {string} userId - 用户ID
     * @returns {Promise<Object>} 用户信息
     */
    async getUserInfo(userId) {
      try {
        this.isLoading = true;
        const response = await this.$http.post('/Api/User/GetInfo', { userId });
        if (response.data.Value) {
          this.userName = response.data.Data.name;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      } finally {
        this.isLoading = false;
      }
    },

    // 使用箭头函数处理简单逻辑
    handleClick: () => {
      console.log('按钮被点击');
    }
  }
};
```

### 4.3 SCSS样式规范

**应用范围**：所有样式代码

**编写规则**：

1. **预处理器**：统一使用SCSS
2. **作用域**：组件样式必须使用scoped
3. **命名方法**：使用BEM命名法
4. **嵌套层级**：不超过3层嵌套

**正确示例**：
```vue
<style scoped lang="scss">
@import "~@/assets/styles/variable";
@import "~@/assets/styles/mixin";

.user-profile {
  padding: 20px;
  background-color: $bg-color;

  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    &--active {
      border-left: 3px solid $primary-color;
    }
  }

  &__content {
    font-size: 14px;
    line-height: 1.5;
    color: $text-color;
  }
}

// 工具类
.text-center {
  text-align: center;
}
</style>
```

**错误示例**：
```vue
<style lang="scss">
/* ❌ 错误：未使用scoped */
/* ❌ 错误：嵌套层级过深 */
.user-profile {
  .header {
    .title {
      .text {
        .content {
          color: red; /* 5层嵌套，过深 */
        }
      }
    }
  }
}
</style>
```

### 4.4 代码格式化

**工具配置**：
- **Prettier**：统一代码格式
- **ESLint**：代码质量检查（当前项目已关闭）
- **EditorConfig**：编辑器配置统一

**格式化规则**：
```json
{
  "semi": true,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 120
}
```