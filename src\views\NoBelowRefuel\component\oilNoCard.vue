<!--
 * @Author: Ltw
 * @Date: 2022-06-17 08:53:54
 * @LastEditors: ltw <EMAIL>
 * @LastEditTime: 2024-05-22 15:30:19
 * @Description: file content
-->
<template>
  <div>
    <div class="staion">
      <div class="staioninfo">
        <b>{{ currentOilStation.stationName }}</b>
        <van-button
          plain
          round
          size="mini"
          type="warning"
          @click="
            $router.push({
              path: '/nobelowlist',
              query: { list: JSON.stringify(oilStationList) },
            })
          "
        >
          更改油站
        </van-button>
      </div>
      <span>
        <van-icon name="location" />
        {{ currentOilStation.address }}
      </span>
      <span class="distance">距离 {{ currentOilStation.distance }} 公里</span>
      <div class="carno">
        <b>常用车辆：{{ cardInfo.carno }}</b>
        <!-- <span @click="$router.push({path: '/my/companyDetail',})">更换车辆</span> -->
      </div>
      <div class="baidumaping" @click="baiduNav2"></div>
    </div>
    <div class="staionorder">
      <div>
        <b>油品号</b>
        <van-grid :gutter="10" :border="false">
          <van-grid-item
            v-for="(item, index) in oilType"
            :key="index"
            :text="item.oilProductName"
            @click="getOingunarr(index)"
            :class="index == oilTypeNum ? 'activedStaion' : ''"
          />
          <span v-if="oilType.length == 0">暂无可用油品号</span>
        </van-grid>
      </div>
      <div class="gun">
        <div>
          <b>选择油枪</b>
          <span>请向加油员确认枪号</span>
        </div>
        <van-grid :gutter="10" :border="false">
          <van-grid-item
            v-for="(item, index) in gunNoarr"
            :key="index"
            :text="item"
            @click="oilGunNum = index"
            :class="index == oilGunNum ? 'activedStaion' : ''"
          />
          <van-grid-item
            class="other"
            @click="customOilGun"
            :class="oilGunNum == 10000 ? 'activedStaion' : ''"
            >手动输入</van-grid-item
          >
        </van-grid>
        <van-field
          v-show="oilGunNum == 10000"
          ref="gunNo"
          v-model="gunNo"
          type="number"
          label-width="50"
          label="油枪号"
          placeholder="请输入油枪号数字"
        />
      </div>
      <van-button
        class="orderOil"
        type="primary"
        round
        size="large"
        @click="OilStationOrder"
        >查询订单</van-button
      >
    </div>
  </div>
</template>

<script>
import { storage } from "Utils/common/";
import { recordJsLog } from "@/utils/recordJsLog";

export default {
  name: "",
  components: {},
  props: {
    //加油信息
    currentOilStation: {},
    /* 父组件获取加油卡信息 */
    cardInfo: {},
    //油品信息
    oilType: {
      type: Array,
      default: [],
    },

    oilStationList: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      oilGunNum: 0, // 选中的枪号下标,10000为手动输入油枪号
      gunNoarr: "", // 油品对应的枪号数据
      gunNo: "", // 手动输入的枪号
      oilTypeNum: 0,
    };
  },
  watch: {
    oilType(val, oldval) {
      console.log(val, "21212");
      if (val.length > 0) {
        this.oilTypeNum = 0;
        this.gunNoarr = this.oilType[0].oilGunNo.split(",");
      } else {
        this.oilTypeNum = 0;
        this.gunNoarr = [];
      }
      // console.log(val,'oilType136');
    },
  },
  computed: {},
  methods: {
    //  不下车加油--唤起百度地图导航
    baiduNav2() {
      recordJsLog("startNavi", {
        startLon: storage.ss.get("deviceInfo").lon,
        startLat: storage.ss.get("deviceInfo").lat,
        endLon: Number(this.currentOilStation?.posx),
        endLat: Number(this.currentOilStation?.posy),
      });

      this.$cppeiBridge("startNavi", {
        startLon: storage.ss.get("deviceInfo").lon,
        startLat: storage.ss.get("deviceInfo").lat,
        endLon: Number(this.currentOilStation?.posx),
        endLat: Number(this.currentOilStation?.posy),
      });
    },
    // 根据加油站、油品编号和油枪号查询是否有订单
    OilStationOrder() {
      console.log(
        parseFloat(this.currentOilStation.distance),
        "parseFloat(this.currentOilStation.distance)"
      );
      if (parseFloat(this.currentOilStation.distance) > 0.5) {
        this.$Dialog.alert({
          title: "提示",
          message: "距离加油站超过500米，请到达加油站内选择油枪",
        });
        return;
      }
      let gunNo;
      try {
        gunNo =
          (this.oilGunNum == 10000
            ? this.gunNo
            : this.gunNoarr[this.oilGunNum].match(/(\S*)号/)[1]) * 1;
      } catch (error) {
        console.log(error, "error");
      }
      console.log(gunNo, "gunNo");
      if (
        typeof gunNo === "number" &&
        !isNaN(gunNo) &&
        Number.isInteger(gunNo)
      ) {
        // gunNo 是一个整数
      } else {
        // gunNo 没有值或者不是一个整数
        this.$Dialog.alert({
          title: "提示",
          message: "请选择或输入正确的油枪号",
        });
        return;
      }
      const params = {
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        standardCode: this.currentOilStation?.oilStationCode,
        // standardCode: "BA01",
        gunNo,
        channel: "932",
      };
      this.$http(
        "POST",
        `/appcenter/trade/v1/getTradeOrderList`,
        params,
        true,
        true,
        true,
        false,
        true
      )
        .then((res) => {
          if (res.infoCode == 1) {
            this.$router.push({
              name: "chooseOrder",
              params: {
                OilStationinfo: JSON.stringify(this.currentOilStation),
                OilGunTrade: JSON.stringify(res?.data),
                oilGunNo: gunNo,
                oilProductName: this.oilType[this.oilTypeNum].oilProductName,
              },
            });
          }
        })
        .catch((err) => {
          // this.$toast("该油枪未查询到订单");
        });
    },
    // 手动输入油枪号显示并获取焦点
    customOilGun() {
      this.oilGunNum = 10000; // 手动输入油枪号专属标识
      this.gunNo = "";
      this.$nextTick(() => {
        this.$refs.gunNo.focus();
      });
    },
    // 更改油品获取对应油枪
    getOingunarr(index) {
      this.oilTypeNum = index;
      this.gunNoarr = this.oilType[index].oilGunNo.split(",");
    },
  },
  created() {},
  mounted() {},
};
</script>

<style lang="scss" scoped>
.staion {
  background: #fff;
  margin: 0 3vw 0vw;
  padding: 4vw;
  border-radius: 4vw 4vw 0vw 0vw;
  text-align: left;
  position: relative;
  // height: 40vw;
  .staioninfo {
    background: #fff;
    display: flex;
    align-items: center;
    margin-bottom: 1vw;
    b {
      font-size: 1.3em;
      margin-right: 2vw;
      max-width: 50vw;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .distance {
    display: block;
    margin-top: 1vw;
  }
  .carno {
    margin-top: 6vw;
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      font-size: 0.6em;
      background: url("~@/assets/images/gasstationmap/addcar.png") no-repeat
        left -1px;
      padding-left: 5vw;
      background-size: contain;
    }
  }
  .baidumaping {
    width: 20vw;
    height: 20vw;
    background: url("~@/assets/images/gasstationmap/navigation.png") no-repeat;
    background-size: contain;
    position: absolute;
    top: 3vw;
    right: 1vw;
  }
}
.staionorder {
  padding: 4vw 3vw 3vw;
  border-radius: 0 0 4vw 4vw;
  margin: 0 3vw;
  background: #fff;
  > div {
    text-align: left;
  }
  /deep/ .van-grid {
    margin: 2vw 0;
    .van-grid-item {
      &__content {
        padding: 8px 0px;
        background: transparent
          url("~@/assets/images/gasstationmap/youpinB.png");
        background-size: 100% 100%;
      }
      &.activedStaion {
        .van-grid-item__content {
          // background: url("~@/assets/images/gasstationmap/youpinA.png");
          background: url("~@/assets/images/winter/btn.png");
          background-size: 100% 100%;
        }
      }
    }
    .other {
      font-size: 12px;
    }
  }
  .gun {
    height: 31vw;
    margin-bottom: 2vw;
    overflow-y: auto;
    > div {
      span {
        float: right;
        color: #989898;
        font-size: 0.8em;
      }
    }
    /deep/ .van-field__control {
      border: 1px solid #017cc0;
      padding-left: 10px;
      border-radius: 1vw;
    }
  }
  .van-button--large {
    height: 40px;
    background-color: #017cc0;
    border: 1px solid #017cc0;
  }
}
</style>
