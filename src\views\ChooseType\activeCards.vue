<template>
  <div class="choose_card">
    <p>下列企业已开通,但未激活,请点击激活按钮再次激活</p>
    <van-cell-group>
      <van-cell v-for="(item, index) in list" :key="index" :title="item.companyName + '-' + item.companyCode" @click="tips" class="actived" />
    </van-cell-group>
    <van-button :disabled="isDisabled" type="warning" class="btn btnBc btnFixed" size="large" @click="active">激活</van-button>
  </div>
</template>

<script>
import { getOpenCardList, changeState } from "./_service/";
import { storage } from "Utils/common/";
import { Toast } from "vant";
import filter from "@/filters/index.js"; 

export default {
  name: "activeCards",
  components: {},
  props: {},
  data() {
    return {
      list: [],
      // 电子卡卡号数组
      cardNos: [],
      // 企业编号数组
      // companyCodes: [],
      isDisabled: false,
    };
  },
  watch: {},
  computed: {},
  created() {
    this.loadData();
  },
  mounted() {
    window.onActivateCards = this.onActivateCards;
  },
  methods: {
    // sdk回调
    onActivateCards(res) {
      console.log('activeCards onActivateCards: ', JSON.stringify(res));

      //! add by kj - 激活卡片后禁用button，SDK回调后消失（2022.1.30）
      this.isDisabled = false;
      
      // 只要有成功
      if (typeof res.successCards === "object" && res.successCards.length > 0) {
        // // 成功的企业id
        // var succCompanyCode = [];
        // // 遍历卡号
        // for (let sucCard of res.successCards) {
        //   // 遍历企业拿到企业id
        //   for (let company of this.list) {
        //     if (company.cardNo === sucCard) {
        //       succCompanyCode.push(company.companyCode);
        //     }
        //   }
        // }
        // 调用后台设置激活状态
        this.changeState(res.successCards);
        if (typeof res.failedCards === "object" && res.failedCards.length > 0) {
          this.$Dialog
            .alert({
              title: "部分卡片激活失败,列表如下",
              // message: JSON.stringify(res.failedCards),
              message: filter.errorMsg(res.failedCards), // 修改错误提示信息格式 - add by yl - 2022.5.5
            })
            .then(() => {
              this.$router.replace({
                path: "/gasstationmap",
              });
            });
        } else {
          // 全部成功,跳到首页
          this.$router.replace({
            path: "/gasstationmap",
          });
        }
      } else {
        // 全部失败
        this.$Dialog.alert({
          title: "全部激活失败",
          // message: JSON.stringify(res.failedCards) + '\n' + "请点击【激活按钮】再次激活", //! 增加全部失败后的详细错误提示 - add by kj - 2022.2.24
          message: filter.errorMsg(res.failedCards) + "请点击【激活按钮】再次激活", // 修改错误提示信息格式 - add by yl - 2022.5.5
        });
      }
    },
    // 调用后台设置激活状态
    changeState(params) {
      changeState({
        phone: storage.ss.get("phone"),
        companyCodeList: [],
        cardNoList: params,
        type: this.$setting.APP_TYPE,
      })
        .then((res) => {
          if (res.infoCode !== 1) {
            Toast(res.info);
          }
        })
        .catch((err) => {
          Toast(err);
        });
    },
    // 获取已开通未激活企业
    loadData() {
      getOpenCardList({
        phone: storage.ss.get("phone"),
        type: this.$setting.APP_TYPE,
      })
        .then((res) => {
          this.list = res.data;
          for (let ele of res.data) {
            this.cardNos.push(ele.cardNo);
            // this.companyCodes.push(ele.companyCode);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 调用sdk激活
    active() {
      if (this.cardNos.length > 0) {
        //! add by kj - 激活卡片后禁用button，SDK回调后消失（2022.1.30）
        this.isDisabled = true;
        
        this.$cppeiBridge("activateCards", {
          pans: this.cardNos,
        });
      } else {
        this.$Dialog.alert({
          title: "温馨提示",
          message: "无卡号,无法激活",
        });
      }
    },
    tips() {
      Toast("请点击【激活按钮】进行激活");
    },
  },
};
</script>
<style lang="scss" scoped>
.choose_card {
  background: #ffffff;
  height: 100vh;
  overflow: scroll;
  p {
    margin: 12vw 2.5vw 4vw 2.5vw;
    text-align: left;
  }
  /deep/.van-cell-group {
    width: 95vw;
    margin: 0 auto;
    // 控制中间滚动
    // overflow-y: scroll;
    @include useScrollByBrowser(0);
    height: 145vw;
  }
  /deep/ .van-cell {
    background: #a39f9f;
    margin: 10px 0;
    text-indent: 30px;
  }
  /deep/ .van-cell__title {
    text-align: left;
    color: #ffffff;
  }
  /deep/ .van-cell__label {
    text-align: left;
    color: #ffffff;
  }
}
</style>