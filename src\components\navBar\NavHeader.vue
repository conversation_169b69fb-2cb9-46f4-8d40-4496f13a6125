<!--
 * @Author: Yongon
 * @Date: 2022-06-08 14:22:27
 * @LastEditors: ltw <EMAIL>
 * @LastEditTime: 2024-01-16 14:37:06
 * @Description: 刘海屏
-->
<template>
  <div class="nav-header">
    <div class="placeholder" :style="{ height: placeholderHeigth }">
      <div class="content" :style="{ height: contentHeight, paddingTop: topSafeArea }">
        <div class="left" @click="onClickLeft">
          <span v-if="leftArrow"> <van-icon name="arrow-left" color="#fff"/></span>
        </div>
        <div class="title">{{ title }}</div>
        <div class="right" @click="onClickRight">
          <span v-if="rightIcon"> <van-icon name="add" size="22" color="#fff"/></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { storage } from "Utils/common/";
export default {
  props: {
    title: {
      type: String,
      default: "",
    },
    leftArrow: {
      type: Boolean,
      default: true,
    },
    rightIcon: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  computed: {
    topSafeArea() {
      return Number(storage.ss.get("statusHeight") ?? 30) + "px";
    },
    placeholderHeigth() {
      return Number((storage.ss.get("statusHeight") ?? 30) + 46) + "px";
    },
    contentHeight(){
      return '46px';
    },
  },
  methods: {
    onClickLeft() {
      this.$router.go(-1);
    },
    onClickRight() {
      this.$emit("click-right");
    },
  },
};
</script>

<style scoped lang="scss">
.nav-header {
  .content {
    background: #ff9200;
    // height: 89px;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;

    .left,
    .right {
      padding: 0 16px;
    }

    .title {
      max-width: 60%;
      font-weight: 500;
      font-size: 36px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
