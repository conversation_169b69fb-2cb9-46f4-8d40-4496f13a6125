<!--
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>on
 * @Description: 加油
-->
<template>
  <div class="WisdomItem">
    <van-cell-group class="info">
      <van-cell :border="false" title-class="left-title" value-class="right-value" title="今日油价" value="市场价仅供参考，请以油站实际价格为准" />
      <van-cell v-for="(item,index) in oilPriceList" :key="index" :border="false" :title="item.oilName" value-class="right-value-1" :value="'￥'+ item.retailPrice+ '/L'" />
    </van-cell-group>
  </div>
</template>

<script>
export default {
  props: {
    oilPriceList: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.WisdomItem {
  padding-top: 20px;
  .info {
    .left-title {
      text-align: left;
      font-size: 40px;
    }
    .right-value {
      text-align: right;
      font-size: 24px;
    }
    .right-value-1 {
      color: #da2b15;
    }
    /deep/ .van-cell {
      padding: 10px 0;
      justify-content: space-between;
    }
    /deep/ .van-cell__title {
      flex: none;
    }
  }
}
</style>
