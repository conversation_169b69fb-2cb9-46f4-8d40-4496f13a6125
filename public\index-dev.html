<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <!-- 开发阶段 使用阿里巴巴矢量图标中的fontclass模式的cdn 若觉得不安全那发布测试或者生产版本是下载到本地引入 -->
    <!-- <link
      rel="stylesheet"
      href="//at.alicdn.com/t/font_1680425_5sole7ra8uo.css"
    /> -->
    <title></title>
<!--    <style>-->
<!--      @keyframes van-rotate {-->
<!--        from {-->
<!--          transform: rotate(0deg);-->
<!--        }-->

<!--        to {-->
<!--          transform: rotate(360deg);-->
<!--        }-->
<!--      }-->
<!--      @-moz-keyframes van-rotate {-->
<!--        from {-->
<!--          transform: rotate(0deg);-->
<!--        }-->

<!--        to {-->
<!--          transform: rotate(360deg);-->
<!--        }-->
<!--      }-->
<!--      @-webkit-keyframes van-rotate {-->
<!--        from {-->
<!--          transform: rotate(0deg);-->
<!--        }-->

<!--        to {-->
<!--          transform: rotate(360deg);-->
<!--        }-->
<!--      }-->
<!--      @-o-keyframes van-rotate {-->
<!--        from {-->
<!--          transform: rotate(0deg);-->
<!--        }-->

<!--        to {-->
<!--          transform: rotate(360deg);-->
<!--        }-->
<!--      }-->
<!--    </style>-->
  </head>

  <body>
    <noscript>
      <strong>We're sorry but vue-app-framework doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>

    <!-- 解决vw的兼容性问题 -->
    <script src="//g.alicdn.com/fdilab/lib3rd/viewport-units-buggyfill/0.6.2/??viewport-units-buggyfill.hacks.min.js,viewport-units-buggyfill.min.js"></script>
    <!-- 本地引入vw-hack -->
    <script>
      window.viewportUnitsBuggyfill || document.write('<script src="./static/viewport-units-buggyfill.hacks.js"><\/script>');
    </script>
    <script>
      window.onload = function() {
        window.viewportUnitsBuggyfill.init({
          hacks: window.viewportUnitsBuggyfillHacks,
        });
      };
    </script>
    <!-- 开发阶段 使用阿里巴巴矢量图标中的symbol模式的cdn 若觉得不安全那发布测试或者生产版本是下载到本地引入 -->
    <!-- <script src="//at.alicdn.com/t/font_1680425_5sole7ra8uo.js"></script> -->
    <!-- 针对active样式的兼容性 -->
    <script>
      document.body.addEventListener('touchstart', function() {});
    </script>
  </body>
</html>
