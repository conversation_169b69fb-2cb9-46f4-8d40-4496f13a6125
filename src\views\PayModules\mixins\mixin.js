/*
 * @Author: Ltw
 * @Date: 2022-06-21 15:59:39
 * @LastEditors: Ltw
 * @LastEditTime: 2022-06-21 16:46:08
 * @Description: file content
 */
import QRCode from "qrcode"; //引入生成二维码插件
const createCode = {
  data() {
    return {
        QRCodeMsg: "",
    };
  },
  methods: {
    /**
     * @description: 生成条形码
     * @param {*} val
     * @return {*}
     */
    getBarCode(val) {
      return new Promise((resolve, reject) => {
        JsBarcode("#barcode", val, {
          format: "CODE39", //选择要使用的条形码类型
          width: 1, //设置条之间的宽度
          height: 60, //高度
          displayValue: false, //是否在条形码下方显示文字
          margin: 0, //设置条形码周围的空白边距
        });
        resolve();
      });
    },
    /**
     * @description: 生成二维码
     * @param {*} val
     * @return {*}
     */
    getQRCode(val) {
      return new Promise((resolve, reject) => {
        let opts = {
          errorCorrectionLevel: "H", //容错级别
          type: "image/png", //生成的二维码类型
          quality: 0.3, //二维码质量
          margin: 6, //二维码留白边距
          width: 210, //宽
          height: 210, //高
          text: val, //二维码内容
          color: {
            dark: "#333333", //前景色
            light: "#fff", //背景色
          },
        };
        this.QRCodeMsg = val; //生成的二维码为URL地址js
        let msg = document.getElementById("QRCode_header");
        // 将获取到的数据（val）画到msg（canvas）上
        QRCode.toCanvas(msg, this.QRCodeMsg, opts, (error) => {
          this.countdown();
        });
        resolve();
      });
    },
  },
  created() {},
  mounted() {},
};
export default createCode;
