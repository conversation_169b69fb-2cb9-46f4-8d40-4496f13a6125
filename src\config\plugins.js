/*
 * @Author: Yongon
 * @LastEditors: ltw <EMAIL>
 * @Description: 引用框架
 */
import Vue from "vue";
import setting from "./setting";

/* 引入封装的方法和定义的常量 */
// axios
import request from "@/utils/http";
// 常用工具方法
import utils from "@/utils/common/utils";
// 表单校验
import validate from "@/utils/common/validate";
// 扩展vue指令
import directives from "@/utils/common/directives.js";
// filter过滤器
import filters from "@/filters";
// 统一ios、Android调用差异
import cppeiBridge from "Utils/bridge";
// 解决H5页面在iOS系统中滑动回弹效果(橡皮筋效果)导致的穿透问题
import "@/utils/common/inobounce.js";
// 变量/常量都绑定在 window 对象
import "@/utils/common/global.js";
// 解决ios input双击页面上移问题
import "@/utils/iosDoubleclick.js";
// 判断接口返回数据是否为空的业务处理
import isBackData from '@/utils/isBackData'

/* 第三方组件 */
// 图片懒加载
import VueLazyload from "vue-lazyload";
// 初始化生产环境下的vconsole
import VConsole from "vconsole/dist/vconsole.min.js"; // TODO change
// pwa配置 简化缓存机制产生的js包
import "@/registerServiceWorker";
// vue-ls从Vue上下文中使用本地Storage，会话Storage和内存Storage
import Storage from "vue-ls";
const options = {
  namespace: "vuejs__", // key键前缀
  name: "ls", // 命名Vue变量.[ls]或this.[$ls],
  storage: "session", // 存储名称: session, local, memory
};

/* 扩展组件 */
// import EleDataTable from '@/components/EleDataTable' // 数据表格
// import EleIconPicker from '@/components/EleIconPicker' // 图标选择器
// import EleAvatarList from '@/components/EleAvatarList' // 头像列表
// import EleDot from '@/components/EleDot' // 状态文字
// import EleResult from '@/components/EleResult' // 操作结果
// import EleTagsInput from '@/components/EleTagsInput' // 标签输入框
// import EleEmpty from '@/components/EleEmpty' // 空视图

/* UI框架 */
// vant组件按需引入
import {
  Swipe,
  SwipeItem,
  Button,
  Cell,
  Image,
  Image as VanImage,
  Icon,
  Checkbox,
  Switch,
  Popup,
  Picker,
  Field,
  Dialog,
  NoticeBar,
  Overlay,
  Toast,
  CheckboxGroup,
  Divider,
  IndexBar,
  IndexAnchor,
  RadioGroup,
  Radio,
  Tag,
  Form,
  CountDown,
  CellGroup,
  NumberKeyboard,
  NavBar,
  Col,
  Row,
  ContactCard,
  Calendar,
  ImagePreview,
  Uploader,
  List,
  PullRefresh,
  PasswordInput,
  Grid,
  GridItem,
  Empty,
  Popover,
  Loading,
  Tabs,
  Tab,
} from "vant";

/* VUE原型挂载相关 */
Vue.prototype.$utils = utils;
Vue.prototype.$setting = setting;
Vue.prototype.$validate = validate;
Vue.prototype.$cppeiBridge = cppeiBridge;
// if (process.env.NODE_ENV != "production")
Vue.prototype.$vConsole = new VConsole(); // TODO change
Vue.prototype.$imagePreview = ImagePreview;
Vue.prototype.$Dialog = Dialog;
Vue.prototype.$toast = Toast;
Vue.prototype.$http = request;
Vue.prototype.$isBackData = isBackData;
// 存储页面vue实例
Vue.prototype.$vuePageInstance = [];

/* 使用vant组件 */
Vue.use(Button)
  .use(RadioGroup)
  .use(Radio)
  .use(Cell)
  .use(Image)
  .use(Icon)
  .use(Checkbox)
  .use(Switch)
  .use(Popup)
  .use(Picker)
  .use(Field)
  .use(Swipe)
  .use(SwipeItem)
  .use(Dialog)
  .use(NoticeBar)
  .use(Overlay)
  .use(Toast)
  .use(CheckboxGroup)
  .use(Divider)
  .use(IndexAnchor)
  .use(IndexBar)
  .use(VanImage)
  .use(Form)
  .use(Tag)
  .use(CountDown)
  .use(CellGroup)
  .use(NumberKeyboard)
  .use(NavBar)
  .use(Col)
  .use(Row)
  .use(ContactCard)
  .use(Calendar)
  .use(ImagePreview)
  .use(Uploader)
  .use(List)
  .use(PullRefresh)
  .use(PasswordInput)
  .use(Grid)
  .use(GridItem)
  .use(Empty)
  .use(Popover)
  .use(Loading)
  .use(Tabs)
  .use(Tab);
Vue.use(require("vue-wechat-title")); //解决IOS动态title
Vue.use(VueLazyload);
Vue.use(directives);
Vue.use(Storage, options);

// 引入loading
import loading from "@/components/loading/loading.js";
Vue.use(loading);

import logo from "@/components/logo";
Vue.component("logo", logo);
Vue.use(logo);

/* 全局注册常用组件 */
// Vue.component(EleDataTable.name, EleDataTable);
// Vue.component(EleIconPicker.name, EleIconPicker);
// Vue.component(EleAvatarList.name, EleAvatarList);
// Vue.component(EleDot.name, EleDot);
// Vue.component(EleResult.name, EleResult);
// Vue.component(EleTagsInput.name, EleTagsInput);
// Vue.component(EleEmpty.name, EleEmpty);

/** 添加全局过滤器 */
// 全局注册filter
Object.keys(filters).forEach((filterName) => {
  Vue.filter(filterName, filters[filterName]);
});
