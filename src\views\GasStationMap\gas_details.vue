<!--
 * @Author: Yongon
 * @LastEditors: yangle<PERSON> <EMAIL>
 * @Description: 油站详情
-->
<template>
  <div class="oil-station-details">
    <nav-bar title="油站详情" />

    <!-- 油站 -->
    <section class="oil-module">
      <img class="oil-banner" src="@/assets/images/gasstationmap/oil_station.jpg" alt="图片加载失败" />
      <div class="oil-station">
        <div class="oil-station-left">
          <div class="oil-station-left-content">
            <img src="@/assets/images/OrderList/logo.png" alt="图片加载失败" />
            <div class="oil-station-left-content_title">
              <p>{{oilStationList.stationName}}</p>
              <p>{{oilStationList.address}}</p>
            </div>
          </div>
          <!-- <div class="oil-station-left-label" v-if="labelList.length > 0"> -->
          <div class="oil-station-left-label">
            <!-- <span v-for="(item, index) in labelList" :key="index">{{ item }}</span> -->
            <span v-if="oilStationList.isFreeWater">免费供水</span>
            <span v-if="oilStationList.isCaChe">擦车</span>
            <span v-if="oilStationList.isWC">公共厕所</span>
            <span v-if="oilStationList.isWeixiuJiJiu">维修设备</span>
            <span v-if="oilStationList.isFood">食物供应</span>
            <span v-if="oilStationList.isFreeBoiledWater">免费热水供应</span>
            <span v-if="oilStationList.isSelfHelp">自助服务</span>
            <span v-if="oilStationList.rechargeStation">充值网点</span>
            <span v-if="oilStationList.iscardapply">办卡网点</span>
            <span v-if="oilStationList.isConvenienceStore">便利店</span>
            <span v-if="oilStationList.isDistribution">配货</span>
            <span v-if="oilStationList.isLounge">休息室</span>
            <span v-if="oilStationList.isParking">停车</span>
          </div>
        </div>
        <div class="oil-station-right">
          <img @click="goMapNav()" class="oil-station-right-map" src="@/assets/images/gasstationmap/navigation.png" alt="图片加载失败" />
          <span>{{ oilStationList.distance }}km</span>
        </div>
      </div>
    </section>

    <!-- line -->
    <div class="line"></div>

    <!-- 智慧之家 -->
    <section class="wisdom-home">
      <div class="wisdom-home-phone">
        <div class="wisdom-home-phone_number">联系电话：{{ oilStationList.stationPhone }}</div>
        <img src="@/assets/images/gasstationmap/phone.png" alt="图片加载失败" @click.stop="goLaunchUrl(oilStationList.stationPhone)" />
      </div>
    </section>

    <!-- line -->
    <div class="line"></div>

    <section class="wisdom-tabs" v-if="tabsList.length > 0">
      <van-tabs v-model="activeTabs" color="#058acd" title-active-color="#058acd" swipeable ellipsis>
        <van-tab v-for="(item, index) in tabsList" :key="index" title-style="fontSize:14px" :title="item.serviceType | serviceName">
          <van-empty v-if="item.serviceDescribe == null && item.serviceImgList == null" description="暂无数据" />
          <div class="wisdom-tabs_swiper" v-else>
            <van-swipe :autoplay="3000" :loop="false">
              <van-swipe-item v-for="(el, k) in item.serviceImgList" :key="k">
                <!-- 配合 Lazyload 组件实现图片懒加载。 -->
                <img v-lazy="el.url.replace(/^http:/i, 'https:')" />
              </van-swipe-item>
            </van-swipe>
            
            <!-- 加油页签和其他企业不一致，需单独处理 -->
            <wisdom-item :oilPriceList="item.oilPriceList" v-if="item.serviceType == 'S05'" />
            <div class="wisdom-tabs_swiper_desc" v-else>{{ item.serviceDescribe }}</div>
          </div>
        </van-tab>
      </van-tabs>
    </section>

    

    <!-- 加油 -->
    <section class="btns" :style="{ paddingBottom: paddingBottom }">
    <!-- <section class="btns"> -->
      <van-button class="go-btn" @click="goOiller()">去加油</van-button>
    </section>
  </div>
</template>

<script>
import navBar from "@/components/navBar/NavHeader";
import WisdomItem from "./component/wisdomItem.vue";
import eventBus from "./_service/eventbus.js";
import { storage, isAndroid} from "Utils/common";
import { mapState } from "vuex";
import { queryStationInfoList } from './_service'
import { recordJsLog } from "@/utils/recordJsLog";

export default {
  name: "gasstationmap",
  components: {
    navBar,
    WisdomItem,
  },
  computed: {
    ...mapState(["rstParams", "deviceInfo"]),
  },
  data() {
    return {
      activeTabs: 0,
      oilStationList: {},
      labelList: [],
      tabsList: [],
      paddingBottom: Number(storage.ss.get("paddingBottom")) || "15px",
    };
  },
  computed: {
    ...mapState(["deviceInfo"]),
  },
  created() {},
  mounted() {},
  activated() {
    this.activeTabs = 0
    this.oilStationList = JSON.parse(this.$route.query.list);
    this.getSmartDriverInfo(this.oilStationList.oilStationCode)
    // console.log("gas_details:"+JSON.stringify(this.$route.query.list));
  },
  filters:{
    // 服务名过滤
    serviceName(status) {
      let statusName = null
      switch (status){
        case 'S01':
          statusName = '便利店'
          break;
        case 'S02':
          statusName = '汽修'
          break;
        case 'S03':
          statusName = '配货'
          break;
        case 'S04':
          statusName = '休息'
          break;
        case 'S05':
          statusName = '加油'
          break;
        case 'S06':
          statusName = '停车'
          break;
        case 'S07':
          statusName = '洗车'
          break;
        default:
          statusName = '--'
        break  
      }
      return statusName
		}
  },
  methods: {
    /**
     * @description: 获取油站服务信息
     * @return {*}
     */ 
    getSmartDriverInfo(oilStationCode){
      queryStationInfoList({
          appid: 2,
          apptoken: storage.ss.get("apptoken"),
          // oilStationCode: 'EN5L', // 测试油站编号
          oilStationCode: oilStationCode, // 油站编号
        }).then((res) => {
          if (res.data && res.infoCode == 1) {
            // res.data.serviceImgList = res
            this.tabsList = res.data || []
            // this.labelList = res.data.map((item, index) => {
            //   return this.$options.filters['serviceName'](item.serviceType);
            // });
            
          }else{
            this.tabsList = []
            // this.labelList = []
          }
        }).catch((err) => {
          console.log(err);
        });
    },
    
    /**
     * @description: 去加油
     * @return {*}
     */

    goOiller() {
      eventBus.$emit("currentOilPos", this.oilStationList);
      //调用router回退页面
      this.$router.push({
        path: "/gasstationmap",
        query: "",
      });
    },

    /**
     * @description: 导航
     * @return {*}
     */
    goMapNav() {
      alert("准备唤起百度导航" + this.oilStationList?.posx + "   " + this.oilStationList?.posy);
      recordJsLog('startNavi', {
        startLon: storage.ss.get("deviceInfo").lon,
        startLat: storage.ss.get("deviceInfo").lat,
        endLon: Number(this.oilStationList?.posx),
        endLat: Number(this.oilStationList?.posy),
      });

      this.$cppeiBridge("startNavi", {
        startLon: storage.ss.get("deviceInfo").lon,
        startLat: storage.ss.get("deviceInfo").lat,
        endLon: Number(this.oilStationList?.posx),
        endLat: Number(this.oilStationList?.posy),
      });
    },

    /**
     * @description: 拨打电话
     * @return {*}
     */
    goLaunchUrl(phone) {

      const phoneStr = isAndroid() ? ("tel:" + phone.split("-").join("")) : ("tel://" + phone.split("-").join(""));
      console.log('phoneStr:'+ phoneStr)
      recordJsLog('launchUrl',  {
        url: phoneStr,
      });

      this.$cppeiBridge("launchUrl", {
        url: phoneStr,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.oil-station-details {
  width: 100%;
  background-color: #fff !important;
  @include useScrollByBrowser(0);
  position: relative;

}
.oil-module {
  // margin-top: -3px;
  .oil-banner {
    width: 100%;
    height: 350px;
  }
  .oil-station {
    display: flex;
    justify-content: space-between;
    padding: 40px 40px 20px 40px;
    box-sizing: border-box;
    &-left {
      width: 80%;
      &-content {
        width: 100%;
        display: flex;
        align-items: center;

        img {
          width: 100px;
          height: 100px;
          display: inline-block;
          margin-right: 10px;
          border-radius: 50%;
          // overflow: hidden;
        }
        &_title {
          text-align: left;
          width: 360px;
          // border: 1px solid red;
          p:nth-child(1) {
            font-weight: 700;
            font-size: 32px;
          }
          p:nth-child(2) {
            font-size: 28px;
            margin-top: 10px;
          }
          p {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            margin: 0;
          }
        }
      }
      &-label {
        text-align: left;
        margin-top: 10px;
        span {
          margin-top: 5px;
          display: inline-block;
          padding: 5px;
          border: 1px solid #d0d0d0;
          margin-right: 8px;
          font-size: 24px;
        }
      }
    }
    &-right {
      width: 20%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      &-map {
        width: 80px;
        height: 80px;
      }
      span {
        margin-top: 15px;
        font-size: 24px;
        color: #7f7f7f;
      }
    }
  }
}
.wisdom-home {
  padding: 20px ;
  // border: 1px solid red;
  box-sizing: border-box;
  &-phone {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    &_number {
      font-size: 32px;
      height: 100px;
      line-height: 100px;
    }
    img {
      width: 100px;
      height: 100px;
      cursor: pointer;
    }
  }
}
.wisdom-tabs {
  padding: 20px ;
  box-sizing: border-box;

    &_swiper {
      margin-top: 20px;
      padding-bottom: 150px ;

      img {
        width: 100%;
        height: 350px;
      }
      &_desc {
        margin-top: 10px;
        text-align: left;
        font-size: 28px;
        line-height: 35px;
        padding: 0 10px;
        box-sizing: border-box;
        // text-indent:2em;
      }
    }
  }
.btns {
  width: 100%;
  // border: 1px solid red;
  position: fixed;
  background-color: #fff;
  bottom: 0;
  text-align: center;
  
  .go-btn {
    margin-top: 30px;
    width: 250px !important;
    height: 70px !important;
    font-size: 28px;
    background: #0380c8;
    border-radius: 10px;
    color: #ffffff;
    letter-spacing: 2px;
  }
}
.line {
  width: 100%;
  height: 15px;
  background-color: #f2f2f2;
}

/deep/ .van-empty {
  padding: 0;
}
</style>
