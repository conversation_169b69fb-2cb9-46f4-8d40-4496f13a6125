/**
 * @description: 记录原生操作日志
 * @return {*}
 */

import request from './http'; // 导入axios封装函数
import { storage } from "Utils/common/";

/**
 * @description: 记录原生操作日志
 * @param {*} methodName  方法名
 * @param {*} paramsObj  原生操作回调参数
 * @return {Promise<void>}
 */
export const recordJsLog = function (methodName, paramsObj = '') {
  try {
    storage.ss.get("phone") ? (paramsObj.phone = storage.ss.get("phone")) : "";
    request('POST', `/user/v1/recordJsLog`, {
      operation: methodName,
      params: paramsObj === '' ? '' : JSON.stringify(paramsObj)
    }, false, true, true, false, true)
  } catch (err) {
    // 在控制台打印错误信息，便于调试
    console.log(err,"日志接口报错");
    // 重新抛出异常，以便上层调用者处理（如果需要）
    // throw err;
  }
}