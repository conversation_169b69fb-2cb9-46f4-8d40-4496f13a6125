<template>
  <div class="certification-person">
    <nav-header title="人脸识别" left-arrow />
    <div class="content">
      <div class="desc">
        <div>拍摄您本人人脸，请确保正对手机，光线充足</div>
        <div>{{ formatName }} {{ formatPhone }}</div>
      </div>
      <div class="icon">
        <img :src="iconPic" width="150" height="150" />
      </div>
      <div class="info">
        <van-icon name="warning-o" :color="$setting.themeColor" />
        <span style="margin-left: 5px;"
          >您可以通过人脸识别对您的身份进行确认，以确保企业和个人财产安全，您的个人信息将保存于中国石油企业版，且仅用于加油卡认证、授权、管理等相关工作，认证后中国石油企业版将不会对外显示您的个人信息，如您所在企业有核对需求，也仅会显示您的脱敏信息（如：**亮、11**************25、138****2110）</span
        >
      </div>
    </div>
    <div class="btn-wrap">
      <van-button block round :loading="btnLoading" loading-text="加载中..." :color="$setting.themeColor" @click="getFaceVerifyMetaInfo">同意并提交认证</van-button>
    </div>
  </div>
</template>

<script>
import { storage } from "Utils/common";
import NavHeader from "@/components/navBar/NavHeader";
import { mapState, mapMutations } from "vuex";

import filter from "@/filters/index.js";
import { recordJsLog } from "@/utils/recordJsLog";

export default {
  components: {
    NavHeader,
  },
  data() {
    return {
      iconPic: require("@/assets/images/login/face.png"),
      metaInfo: {},
      certifyId: "",
      name: storage.ss.get("name") || "tt",
      phone: storage.ss.get("phone") || "",
      btnLoading: false,
    };
  },
  computed: {
    formatName: function() {
      return filter.formatName(this.name);
    },
    formatPhone: function() {
      return filter.formatPhone(this.phone);
    },
    ...mapState(["permissions"]),
  },
  mounted() {
    window.onGetFaceVerifyMetaInfo = this.onGetFaceVerifyMetaInfo;
    window.onEvokeFaceVerify = this.onEvokeFaceVerify;
    window.onGetPermissions = this.onGetPermissions;
  },
  methods: {
    ...mapMutations(["Update_permissions"]),

    // 人脸认证初始化参数回调
    onGetFaceVerifyMetaInfo(metaInfo) {
      recordJsLog('onGetFaceVerifyMetaInfo', metaInfo);
      
      this.metaInfo = JSON.stringify(metaInfo);
      this.initFaceVerify();
    },
    // 集成在APP里的阿里云SDK进行人脸认证回调
    onEvokeFaceVerify(params) {
      recordJsLog('onEvokeFaceVerify', params);

      if (params.error == "1000") {
        this.$router.push({
          path: "/personResult",
        });
      } else {
        this.$Dialog.alert({
          title: "人脸认证失败",
          message: params.error + "-" + params.message,
        });
        this.btnLoading = false;
      }
    },

    /**
     * @description: sdk获取隐私权限回调
     * @return {*}
     */

    onGetPermissions(params) {
      recordJsLog('onGetPermissions', params);
      
      console.log("FaceRecognition 隐私权限回调", params);
      this.Update_permissions(params);
      this.btnLoading = false;

      // 跳转到应用详情设置页 （注：仅限Android, 后续有小版本可考虑适配各厂商，跳转到具体的权限设置页）
      if (!params.camera) {
        this.$Dialog
          .alert({ title: "相机权限需要", message: "请前往设置->应用->中油车队端->权限中打开相机相关权限，否则功能无法正常运行！" })
          .then(() => {
            setTimeout(() => {
              recordJsLog('openAuthSetting');

              this.$cppeiBridge("openAuthSetting", {});
            }, 500);
          })
          .catch(() => {});
      } else {
        // 主动调用原生获取阿里人脸认证初始化参数
        recordJsLog('getFaceVerifyMetaInfo');

        this.$cppeiBridge("getFaceVerifyMetaInfo", {});
      }
    },

    // 同意并提交认证
    getFaceVerifyMetaInfo() {
      this.btnLoading = true;
      console.log(this.permissions.camera, "同意并提交认证");
      if (!this.permissions.camera) {
        recordJsLog('getPermissions', {
          permissions: ["camera", "album"], //camera-相机，album-相册
        });

        this.$cppeiBridge("getPermissions", {
          permissions: ["camera", "album"], //camera-相机，album-相册
        });
      } else {
        // 主动调用原生获取阿里人脸认证初始化参数
        recordJsLog('getFaceVerifyMetaInfo');
        
        this.$cppeiBridge("getFaceVerifyMetaInfo", {});
      }
    },

    // 唤起人脸验证
    evokeFaceVerify() {
      if (this.certifyId) {
        // 调用集成在APP里的阿里云SDK进行人脸认证
        recordJsLog("evokeFaceVerify", {
          certifyId: this.certifyId,
        });

        this.$cppeiBridge("evokeFaceVerify", {
          certifyId: this.certifyId,
        });
      } else {
        this.$Dialog.alert({
          title: "提示",
          message: "人脸活体初始化认证失败",
        });
      }
    },
    /**
     * @description:用户信息初始化人脸认证(10.14  二要素修改)
     * @return {*}
     */
    initFaceVerify() {
      const params = {
        cardNumber: storage.ss.get("idCard"),
        mobileNumber: storage.ss.get("phone"),
        channel: 912,
        name: storage.ss.get("name"),
        returnUrl: "http://enterprisecardcs.95504.net/fleetcard_manage/personResult", // 原来方案，该字段没有用
        metaInfo: this.metaInfo,
        verifyMode: 1,
        local: storage.ss.get("deviceInfo")?.province + storage.ss.get("deviceInfo")?.city,
        gpsLocation: storage.ss.get("deviceInfo")?.province + storage.ss.get("deviceInfo")?.city,
        idnoType: 1,
      };
      this.$http("POST", `/card/appUserOcr/v1/initFaceVerifyByUserInfo`, params, true, true, true)
        .then((res) => {
          if (res.infoCode == 1) {
            storage.ss.set("verifyUnique1", res.data.verifyUnique);
            storage.ss.set("certifyId", res.data.certifyId);
            this.certifyId = res.data.certifyId;
            this.evokeFaceVerify();
          } else {
            this.$Dialog.alert({
              title: "提示",
              message: res.info ?? "人脸认证初始化失败！",
            });
          }
        })
        .catch((e) => {});
    },

    // 人脸活体初始化认证
    // initFaceVerify() {
    //   const params = {
    //     metaInfo: this.metaInfo,
    //     // 生产服务器
    //     // returnUrl: 'https://enterprisecard.95504.net/fleetcard_manage/personResult',
    //     // 测试服务器
    //     returnUrl: "http://enterprisecardcs.95504.net/fleetcard_manage/personResult",
    //     // 本地
    //     // returnUrl: 'http://127.0.0.1:8888/fleetcard_manage/personResult',
    //     verifyUnique: storage.ss.get("verifyUnique"),
    //     verifyMode: 1,
    //   };
    //   this.$http("POST", `/card/appUserOcr/v1/initFaceVerify`, params, true, true, true)
    //     .then((res) => {
    //       if (res.infoCode == 1) {
    //         storage.ss.set("verifyUnique1", res.data.verifyUnique);
    //         storage.ss.set("certifyId", res.data.certifyId);
    //         this.certifyId = res.data.certifyId;
    //         this.evokeFaceVerify();
    //       } else {
    //         this.$Dialog.alert({
    //           title: "提示",
    //           message: res.info ?? "人脸认证初始化失败！",
    //         });
    //       }
    //     })
    //     .catch((e) => {});
    // },
  },
};
</script>

<style scoped lang="scss">
.certification-person {
  .content {
    .desc {
      line-height: 2;
      margin: 16px 0;
    }

    .info {
      text-align: left;
      color: #aaa;
      line-height: 1.3;
      margin: 80px 16px 10px;
      display: flex;
    }
  }

  .btn-wrap {
    padding: 20px 20px;
  }
}
</style>
