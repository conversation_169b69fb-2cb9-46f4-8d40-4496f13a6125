<template>
  <div class="consuming-records">
    <nav-bar title="消费记录" />
    <div class="tips">
      查询结果只显示本卡三个月内记录,如需更多查询记录,请联系管理员
    </div>
    <div class="date-choose">
      <van-cell :title="dates" value="" @click="onDisplay">
        <template slot="right-icon">
          <img src="@/assets/images/icon/calendar.jpg" class="icon" />
        </template>
      </van-cell>
      <!-- 日期选择 -->
      <van-calendar
        v-model="show"
        type="range"
        confirm-text="查询"
        :allow-same-day="true"
        :min-date="minDate"
        :max-date="maxDate"
        :max-range="range"
        @close="onClose"
        @confirm="onConfirm"
        :color="$setting.themeColor"
      />
    </div>
    <!-- 列表 -->
    <van-pull-refresh v-model="isLoading" @refresh="onRefresh">
      <div class="container">
        <div class="no-record" v-if="!hasData">无消费记录</div>
        <template v-else>
          <van-list
            v-model="loading"
            :finished="finished"
            finished-text="- 没有更多了 -"
            @load="onLoad"
            :immediate-check="false"
            :offset="50"
          >
            <div
              class="record-list"
              v-for="record in recordList"
              :key="record.orderNo"
            >
              <div class="order-item">
                <span>订单号: {{ record.orderNo }}</span>
                <span id="money">￥ {{ record.rcvAMT }}</span>
              </div>
              <div class="order-time">
                <span>{{ record.paymentTime }}</span>
              </div>
            </div>
          </van-list>
        </template>
      </div>
    </van-pull-refresh>
  </div>
</template>

<script>
import { getRecordList } from "@/views/My/_service";
// 2种方式导入都可以,@代表src, ../父目录 ./当前目录 /根目录
import navBar from "@/components/navBar/NavHeader";
import { storage } from "Utils/common/";
import moment from "moment";

export default {
  props: {},
  components: { navBar },
  data() {
    return {
      show: false,
      dates: "近三个月",
      // 开始时间最小为 2021年1月1日
      // 结束时间最大为当前时间
      minDate: new Date(moment().subtract(3,'month').format('YYYY-MM-DD')),
      maxDate: new Date(moment(new Date()).format('YYYY-MM-DD')),
      // 最多可选90天
      range: "90",
      // 消费记录
      recordList: [
        // 订单编号 orderNo;
        // 订单实付金额 rcvAMT;
        // 支付时间 paymentTime;
      ],
      // 每页条数
      pageSize: 20,
      // 开始时间
      startDate: "",
      // 结束时间
      endDate: "",
      page: 1, //当前页
      loading: false, // 当loading为true时，转圈圈
      finished: false, // 数据是否请求结束，结束会先显示- 没有更多了 -
      hasData: true, // 如果没有数据，显示暂无数据
      isLoading: false, // 下拉的加载图案
    };
  },
  computed: {},
  created() {},
  activated() {},
  mounted() {
    this.startDate = moment(this.minDate).format("yyyy-MM-DD");
    this.endDate = moment(this.maxDate).format("yyyy-MM-DD");
    this.onRefresh();
  },
  watch: {},
  methods: {
    onDisplay() {
      this.show = true;
    },
    onClose() {
      this.show = false;
    },
    formatDate(date) {
      return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
    },
    onConfirm(date) {
      const [start, end] = date;
      this.show = false;
      this.dates = `${this.formatDate(start)} 至 ${this.formatDate(end)}`;
      this.page = 1;
      this.startDate = moment(start).format("yyyy-MM-DD");
      this.endDate = moment(end).format("yyyy-MM-DD");
      // 刷新数据
      this.onRefresh();
    },
    // 列表加载
    onLoad() {
      setTimeout(() => {
        this.getListByPage();
        this.loading = true;
      }, 300);
    },
    onRefresh() {
      setTimeout(() => {
        // 重新初始化这些属性
        this.isLoading = false;
        this.recordList = [];
        this.page = 1;
        this.loading = false;
        this.finished = false;
        this.hasData = true;
        // 查询数据
        this.getListByPage();
      }, 300);
    },
    // 分页查询数据
    getListByPage() {
      getRecordList({
        appid: "2",
        apptoken: storage.ss.get("apptoken"),
        companyUserId: "1",
        curPage: this.page,
        pageSize: this.pageSize,
        phone: storage.ss.get("phone"),
        startDate: this.startDate,
        endDate: this.endDate,
        userid: "1",
        cardNo: storage.ss.get("driverInfo").cardNo,
      })
        .then((res) => {
          // 追加数据
          this.recordList = this.recordList.concat(res.data.items);
          // 加载状态结束
          this.loading = false;
          // 如果没有数据，显示暂无数据
          if (this.recordList.length === 0 && this.page === 1) {
            this.hasData = false;
          }
          // 页码加1
          this.page++;
          // 如果加载完毕，显示没有更多了
          if (res.data.items.length === 0) {
            this.finished = true;
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>

<style scoped lang="scss">
.consuming-records {
  .tips {
    padding: 20px;
    text-align: left;
    margin-bottom: 30px;
    background-color: #ffe5da;
    color: #ff9200;
  }
  .date-choose {
    .van-cell__title {
      text-align: left;
      span {
        color: #c70d0a;
        font-size: 15px;
        width: 66vw;
        display: inline-block;
      }
    }
    img {
      height: 4.5vw;
    }
  }
  /deep/ .van-pull-refresh {
    // background-color: rgba(239, 239, 239, 100%) !important;
    // 控制中间滚动
    height: 435px;
    // overflow-y: scroll;
    @include useScrollByBrowser(0);
    .container {
      .no-record {
        background-color: #fff;
        padding: 30px 0;
        margin-top: 2px;
      }
      .van-list {
        .record-list {
          height: auto;
          margin-bottom: 2px;
          padding: 10px;
          background-color: #fff;
          .order-item,
          .order-time {
            height: 25px;
            display: flex;
            justify-content: space-between;
            #money {
              color: #c70d0a;
            }
          }
        }
      }
    }
  }
}
</style>
