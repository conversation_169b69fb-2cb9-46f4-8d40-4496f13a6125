/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-08-31 14:09:10
 * @LastEditors: Yongon
 * @LastEditTime: 2022-08-31 17:47:39
 * @Description: 解决ios input双击页面上移问题
 */
import { isAndroid } from "Utils/common/";
(function() {
  var iLastTouch = null; //缓存上一次tap的时间
  if (!isAndroid()) {
    //检测是否是ios
    document.body.addEventListener(
      "touchend",
      function(event) {
        var iNow = new Date().getTime();
        iLastTouch = iLastTouch || iNow + 1 /** 第一次时将iLastTouch设为当前时间+1 */;
        var delta = iNow - iLastTouch;
        if (delta < 500 && delta > 0) {
          event.preventDefault();
          return false;
        }
        iLastTouch = iNow;
      },
      false
    );
  }
})();
