<template>
  <div class="close-account">
    <nav-bar title="销户申请" />
    <div class="notice">请选择你想销户的企业，只有该卡账户资金为0时才可申请销户</div>
    <van-cell-group>
      <van-cell 
      v-for="(item,index) in activedCompanyList" 
      :key="index"  
      :value="item.companyName + '-' + item.companyCode" 
      :class="checked == index ? 'active' : ''" 
      @click="selectCompany(item,index)" />
    </van-cell-group>
    <div class="btns">
      <van-button size="large" :color="$setting.themeColor" @click="CancelECardApply">申 请 销 户</van-button>
    </div>

    <van-dialog class="close_dialog" v-model="dialogshow" :title="dialogTitle" confirmButtonText="我知道了">
      <div v-if="dialogContentShow">
        <span>您可以：</span>
        <span>1.去消费账户余额</span>
        <span>2.联系企业管理员汇总账户余额</span>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import navBar from "@/components/navBar/NavHeader";
import { getActivatedList, CancelECardApply,cancelRiskManagement } from "./_service";
import { storage, compareVersions } from "Utils/common";
import { recordJsLog } from "@/utils/recordJsLog";

export default {
  components: { navBar },
  data() {
    return {
      checked: -1,
      activedCompanyList: [],
      closecompany: {},
      dialogshow: false,
      dialogTitle: '',
      dialogContentShow: false,
      payRiskState:null, // 风控状态
    };
  },
  computed: {},
  mounted() {
    window.onDeviceFinger = this.onDeviceFinger;
  },
  methods: {
    back() {
      this.$router.replace({path: "/my",});
    },
    /**
     * 设备指纹识别回调
     *
     * @param obj - 回调对象，包含错误信息和设备信息
     */
    onDeviceFinger(obj) {
      recordJsLog("onDeviceFinger", obj);
      console.log(this.payRiskState,"login----onDeviceFinger", obj);
      
      if (obj.error === "") {
        if (this.payRiskState === 2) {
          this.riskManagement({ status: "" }, (res) => {
            console.log(res);
            if (res.value) {
              // 后台接口登录
              this.GetCancelECardApply();
            }
          });
        } else if (this.payRiskState === 0) {
          this.riskManagement({ status: "0" ,deviceId: obj.message });
        } else if (this.payRiskState === 1) {
          this.riskManagement({ status: "1" ,deviceId: obj.message });
        } else {
          this.$dialog
            .alert({
              title: "提示",
              message: obj.error + "-" + obj.message,
            })
            .then(() => {});
        }
      } else {
        this.$dialog
          .alert({
            title: "提示",
            message: obj.error + "-" + obj.message,
          })
          .then(() => {});
      }
    },
    // 获取已激活所属企业列表
    getActivatedList () {
      getActivatedList({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        phone: storage.ss.get("phone"),
        type: 2,
        curPage: 1,
        pageSize: 10,
      }).then((res) => {
        if(res.infoCode == 1 && res.data) {
          this.activedCompanyList = res.data.filter((item, i, arr) => {
            //函数本身返回布尔值，只有当返回值为true时，当前项存入新数组。
            return item.isActive != 4
          })
        }
      }).catch((res) => {})
    },
    // 选择想要销户的企业
    selectCompany (item,index) {
      this.checked = index;
      this.closecompany = item;
    },
    CancelECardApply () {
      if(this.checked < 0 ) {
        this.$toast("请选择需要销户的单位");
        return false
      }
      if(this.closecompany.spareBalance && this.closecompany.spareBalance > 0) {
        this.dialogshow = true;
        this.dialogTitle = '抱歉，您的账户仍有余额，暂无法销户'
        this.dialogContentShow = true;
        return false
      }
      console.log(12);
      if(compareVersions(storage.ss.get("deviceInfo").version || '', '1.1.2') >= 0) {
        this.payRiskState = 2;
        recordJsLog("deviceFinger", {});
        this.$cppeiBridge("deviceFinger", {});
      } else {
        // 调用销户风控
        this.riskManagement({ status: "" }, (res) => {
          console.log(res);
          if (res.value) {
            // 后台接口登录
            this.GetCancelECardApply();
          }
        })
      }
    },

    /**
     * @description: 调用申请销户接口
     * @return {*}
     */    
    GetCancelECardApply(){
       const params = {
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        attribute1: `${this.closecompany.cardNo}申请销户`,
        cardNo: this.closecompany.cardNo,
        businessType: 0,
        launcherUniqueId: this.closecompany.companyCode,
        requestCnt: "申请销户"
      }
      CancelECardApply(params).then((res) => {
        if (res.infoCode == 1) {
          if(compareVersions(storage.ss.get("deviceInfo").version || '', '1.1.2') >= 0) {
            this.payRiskState = 0;
            recordJsLog('deviceFinger', {});
            this.$cppeiBridge("deviceFinger", {});
          } else {
            this.riskManagement({ status: "0" });
            this.closecompany = {};
          }
          this.dialogshow = true;
          this.dialogTitle = '销户申请已提交，请等待管理员审核';
          this.dialogContentShow = false;
          this.checked = -1;
          this.getActivatedList();
        }else{
          if(compareVersions(storage.ss.get("deviceInfo").version || '', '1.1.2') >= 0) {
            this.payRiskState = 1;
            recordJsLog('deviceFinger', {});
            this.$cppeiBridge("deviceFinger", {});
          } else {
            this.riskManagement({ status: "1" });
          }
        }
      })
    },
    // 销户风控
    riskManagement(params, callback = false) {
      // let res = {
      //   value: true,
      // };
      // if (callback) {
      //   callback(res);
      // }
      // return;
      const deviceInfo = storage.ss.get("deviceInfo");
      let { status,deviceId = '' } = params;
      cancelRiskManagement({
        //  address:"四川省;成都市;青羊区",
        address: deviceInfo?.province + ";" + deviceInfo?.city + ";" + deviceInfo?.district,
        appid: 2,
        cardNo: this.closecompany.cardNo,
        companyPhone: '',  // 后端沟通先传空，后端去取
        driverPhone: this.closecompany.phone,
        gps: 'gps',
        latitude: deviceInfo?.lat ?? "39.920248",
        longitude: deviceInfo?.lon ?? "116.407718",
        status,
        deviceId
      })
        .then((res) => {
          if (callback) {
            callback(res);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
  created() {
    this.getActivatedList();
  }
};
</script>

<style scoped lang="scss">
.close-account {
  /deep/ .van-cell-group {
    margin: 18px 14px;
    background-color: transparent;
    .van-cell{
      background-color: #eee;
      margin-bottom: 12px;
      border-radius: 5px;
    }
    .active {
      background-color: #ff9200;
      .van-cell__value--alone {
        color: #fff;
      }
    }
  }
  .notice {
    margin: 4vw;
    margin-bottom: 10vw;
    text-align: left;
  }
  .btns {
    position: absolute;
    bottom: 40px;
    left: 5%;
    width: 90%;
  }
  /deep/ .close_dialog {
    .van-dialog__header {
      font-weight: bold;
    }
    .van-dialog__content {
      margin-bottom: 26px;
      span {
        display: block;
        text-align: left;
        margin-left: 50px;
        font-size: 12px;
        line-height: 20px;
      }
    }
  }
}
</style>