<!--
 * @Author: Yongon
 * @LastEditors: ltw <EMAIL>
 * @Description: APP.vue
-->
<template>
  <div id="app" class="winter">
    <transition :name="`page-${curRouterType}`">
      <keep-alive include="gasstationmap,noBelowRefuel">
        <router-view v-wechat-title="$route.meta.title" class="router-view" :style="{ height: $route.meta.footNav === false ? '100vh' : routerViewHit }"></router-view>
      </keep-alive>
    </transition>
    <div class="footer">
      <footer-nav v-if="$route.meta.footNav != false" />
    </div>

    <!-- 软件更新弹框 -->
    <update />
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import { storage, getUserAgent } from "Utils/common/";
import filter from "@/filters/index.js";
import FooterNav from "@/components/footerNav/FooterNav.vue";
import update from "@/components/update/index.vue";

// 后端接口
import { GetUserInfoByToken } from "./views/UserModules/_service/index.js";
import { recordJsLog } from "@/utils/recordJsLog";

export default {
  name: "app",
  data() {
    return {
      timeStamp: "",
      routerViewHit: storage.ss.get("routerViewHit") ?? document.body.clientHeight,
    };
  },
  components: {
    FooterNav,
    update,
  },
  computed: {
    ...mapState({
      // 当前路由方式：forward(前进)，back(后退)
      curRouterType: (state) => state.curRouterType,
      isQianYi: (state) => state.isQianYi,
    }),
  },
  watch: {},
  created() {
    this.getUserAgentInfo();
    // this.$router.replace("/identityCardOCR");
    // this.$router.replace("/gasDetails");

    // 在页面刷新时将vuex里的信息保存到sessionStorage里
    window.addEventListener(
      "unload",
      () => {
        sessionStorage.setItem("store", JSON.stringify(this.$store.state));
      },
      true
    );
  },
  mounted() {
    window.onGetDeviceId = this.onGetDeviceId;
  },
  methods: {
    ...mapMutations(["isQianYi_fn"]),

    /**
     * @description: 获取设备唯一编码回调
     * @return {*}
     */

    onGetDeviceId(params) {
      recordJsLog('onGetDeviceId', params);
      
      console.log("APP onGetDeviceId:" + JSON.stringify(params));
      storage.ss.set("deviceInfo", { ...storage.ss.get("deviceInfo"), ...params });
      console.log("APP onGetDeviceId deviceInfo: " + JSON.stringify(storage.ss.get("deviceInfo")));
    },

    /**
     * @description: 通过UserAgent获取设备信息
     * @return {*}
     */
    getUserAgentInfo() {
      let uaStr = getUserAgent();
      console.log("APP UserAgent:" + JSON.stringify(uaStr));

      // let uaStr = {
      //   statusHeight: 45,
      //   cpsStatus: 3,
      //   reapplyStatus: 2,
      //   agreePrivacy: true,
      //   name: "HONOR",
      //   bottomHeight: 0,
      //   isVirBar: false,
      //   type: "Android",
      //   version: "1.0.8",
      //   token: "5e4f3e82e32a5869178b275a478317b3",
      // };
      try {
        // 存储设备信息
        this.setDeviceInfo(uaStr);
        // 是否已点击同意隐私弹框 ---  by yl 20220704
        if (uaStr?.agreePrivacy) {
          // 判断useragent是否已经缓存登录信息
          if (uaStr?.token) {
            // 隐私权限页面 点击同意后到登录页在获取设备ID
            recordJsLog('getDeviceId');

            this.$cppeiBridge("getDeviceId", {});
            // cpsStatus, 仅保持登录下判断使用  SDK注册状态(0-未初始化, 1-未注册, 2-注册中, 3-注册完成, 4-注销, 5-授权证书过期)
            if (uaStr?.cpsStatus === 3) {
              // reapplyStatus, 仅保持登录判断使用，0为初始状态，当前端调用迁移判断需要迁移时调用 (0-初始状态, 1-需要迁移, 2-已迁移)
              this.isQianYi_fn(uaStr.reapplyStatus);
              // this.$cppeiBridge("saveReapplyStatus", {reapplyStatus: uaStr.reapplyStatus})
              console.log('APP 设置迁移状态：'+ uaStr.reapplyStatus);

              // 使用token获取用户信息
              this.GetUserInfoByToken(uaStr.token);
            } else {
              this.$Dialog
                .alert({
                  title: "提示",
                  message: filter.sdkRegisterStatus(uaStr.cpsStatus),
                })
                .then(() => {
                  this.$cppeiBridge("clearToken", {});
                  storage.ss.remove("apptoken");
                  storage.ss.clear();
                  this.$router.replace({ path: "/loginCenter" });
                });
            }
          } else {
            // 解决应用商店上架隐私权限链接打开跳转回首页，供测试审核人员查看
            // if (window.location.pathname.indexOf("privacyPolicy") == -1 && window.location.pathname.indexOf("userAgreement") == -1) {
            //   this.$router.replace({ path: "/loginCenter" });
            // }
            this.$router.replace({ path: "/loginCenter" });
          }
        } else {
          this.$router.push({ path: "/authority" });
        }
      } catch (e) {
        console.error(e);
      }
    },

    /**
     * @description: 使用token获取用户信息
     * @param {*} token
     * @return {*}
     */
    GetUserInfoByToken(token) {
      GetUserInfoByToken({
        appid: 2,
        apptoken: token,
      })
        .then((res) => {
          if (res.data && res.infoCode == 1) {
            // 获取信息保存
            storage.ss.set("apptoken", res.data.apptoken);
            storage.ss.set("userId", res.data.id);
            storage.ss.set("defaultCompanyCode", res.data.defaultCompanyCode);
            storage.ss.set("phone", res.data.phone);
            storage.ss.set("cardNo", res.data.cardNo);
            storage.ss.set("idCard", res.data.idCard);
            if (res.data.defaultCompanyCode) {
              this.getOpenCard({ phone: res.data.phone, type: 2 });
            } else {
              this.$router.replace({
                path: "/loginCenter",
              });
            }
          }
        })
        .catch((err) => {});
    },

    /**
     * @description: 获取已开通未激活企业
     * @param {*} params
     * @return {*}
     */
    getOpenCard(params) {
      this.$http("POST", `/card/appUser/v1/getOpenCardList`, params, true, true, true).then((res) => {
        if (res.infoCode === 1) {
          const defIsOpen = res.data.some((item) => item.companyCode == storage.ss.get("defaultCompanyCode"));
          if (defIsOpen) {
            this.$router.replace({
              path: "/chooseEnterprise",
              query: {
                isActive: 2,
              },
            });
          } else {
            // 跳到首页
            this.$router.replace({ path: "/gasstationmap" });
          }
        } else {
          Toast(res.info);
        }
      });
    },

    /**
     * @description: 设置设备信息包括设备名、安全区域高度
     * @return {*}
     */
    setDeviceInfo(params) {
      if (storage.ss.get("deviceInfo")) {
        storage.ss.set("deviceInfo", { ...storage.ss.get("deviceInfo"), ...params });
      } else {
        storage.ss.set("deviceInfo", { ...params });
      }
      // 在有导航的地方设置安全区域 故缓存 然后在有导航的地方设置
      storage.ss.set("paddingBottom", params.bottomHeight + "px");
      storage.ss.set("statusHeight", params.statusHeight);
      storage.ss.set("routerViewHit", document.body.clientHeight - params.bottomHeight + "px");
      // this.routerViewHit = document.body.clientHeight - params.bottomHeight + "px";
    },
  },
};
</script>

<style lang="scss">
@import "~Styles/_normalize.css";
@import "~Styles/class.css";
@import "~Styles/winter.scss";

#app {
  color: $sysFontColor;
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  text-align: center;
  font-size: 28px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  // 定义过渡效果
  // 前进
  .page-forward-enter,
  .page-back-leave-active {
    transform: translate3d(100%, 0, 0);
    opacity: 0;
  }

  .page-forward-leave-active,
  .page-back-enter {
    transform: translate3d(-100%, 0, 0);
    opacity: 0;
  }

  // 路由切换动效
  .router-view {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f5f5f5;
    backface-visibility: hidden;
    transition: all 500ms cubic-bezier(0.55, 0, 0.1, 1);
  }

  .footer {
    background-color: #f8f8f8;
    position: absolute;
    z-index: 0;
    width: 100vw;

    /deep/ .van-tabbar {
      z-index: 0;
    }
  }
}
</style>
