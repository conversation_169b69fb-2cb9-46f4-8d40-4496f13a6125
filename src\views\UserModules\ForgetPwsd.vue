<template>
  <div class="login-code">
    <nav-bar title="忘记账户密码" />
    <div class="login-card">
      <van-form @submit="onSubmit" validate-first label-width="70" :show-error-message="false" :show-error="false">
        <van-field v-model="user.phone" label="手机号" clearable placeholder="请输入" maxlength="11" center :rules="[{ validator: validatePhone, trigger: 'onBlur' }]">
          <template slot="left-icon">
            <!-- <img src="@/assets/images/icon/shouji.png" class="icon" /> -->
            <img src="@/assets/images/winter/login_phone.png" class="icon" />
          </template>
        </van-field>
        <van-field v-model="user.captcha" label="图片验证码" clearable placeholder="请输入" center :rules="[{ validator: validateCaptcha, trigger: 'onBlur' }]">
          <template slot="left-icon">
            <!-- <img src="@/assets/images/icon/yanzhengma.png" class="icon" /> -->
            <img src="@/assets/images/winter/login_yanzhengma.png" class="icon" />
          </template>
          <template #button>
            <!-- 前端验证码 -->
            <div @click="changeCode">
              <v-sidentify :identifyCode="picCode"></v-sidentify>
            </div>
            <!-- 后端验证码 -->
            <!-- <img id="pic-code" alt="验证码" :src="imgUrl" @click="changeImg" /> -->
          </template>
        </van-field>
        <van-field v-model="user.smsCode" label="短信验证码" icon-prefix="my-icon" clearable maxlength="4" placeholder="请输入" center :rules="[{ validator: validateSms, trigger: 'onBlur' }]">
          <template slot="left-icon">
            <!-- <img src="@/assets/images/icon/mima.png" class="icon" /> -->
            <img src="@/assets/images/winter/login_password.png" class="icon" />
          </template>
          <template slot="button">
            <van-button size="small" style="width:80px" :loading="smsBtnLoading" type="warning" plain :disabled="!showSend" @click.prevent="checkAndSend">
              <!-- 第一次发送显示: 获取验证码 -->
              <div v-show="showSend" v-if="0 === clickNum">发送验证码</div>
              <!-- 以后都显示: 再次发送 -->
              <div v-show="showSend" v-else>再次发送</div>
              <!-- 倒计时60秒 -->
              <span v-show="!showSend">{{ count }}&nbsp;s</span>
            </van-button>
          </template>
        </van-field>
        <!-- 错误提示 -->
        <div class="err-tips">{{ errMsg }}</div>

        <!-- 下一步按钮 -->
        <div class="btn-wrap">
          <van-button block round size="large" :color="$setting.themeColor" native-type="submit" :disabled="isDisabled">下一步</van-button>
        </div>
      </van-form>
    </div>

    <!-- 底部其他操作 切换登录方式 协议 -->
    <div class="loginfooter">
      <van-checkbox class="check-box" label-disabled :checked-color="$setting.themeColor" v-model="checked" shape="square">我已阅读并同意</van-checkbox>
      <span class="text" @click="switch2PrivacyPolicy">《隐私政策》</span>及
      <span class="text" @click="switch2UserAgreement">《用户协议》</span>
    </div>
  </div>
</template>

<script>
import { Toast } from "vant";
import { mapMutations } from "vuex";
import navBar from "@/components/navBar/NavHeader";
import { storage, generateUUID } from "Utils/common/";
// 后端接口
import { sendCode4ForgetPswd, checkCode4ForgetPswd, getCaptchaText, isPhoneExist } from "./_service/";

// 画验证码组件
import Sidentify from "@/components/code/drawCodePic.vue";
//更改倒计时时间
const TIME_COUNT = 59;

export default {
  props: {},
  components: {
    navBar,
    "v-sidentify": Sidentify,
  },
  data() {
    return {
      // 表单数据
      user: {
        phone: "",
        // 短信验证码
        smsCode: "",
        // 图形验证码
        captcha: "",
        // 1 单位 2司机
        type: this.$setting.APP_TYPE,
      },
      // 默认选中
      checked: false,
      // 图形验证码,传到画图的子组件,首次加载会被覆盖掉
      picCode: "",
      // 初始启用按钮
      showSend: true,
      // 点击发送验证码次数
      clickNum: 0,
      // 初始化次数
      count: "",
      // 错误提示
      errMsg: "",
      // 图片验证码路径前缀
      prefix: process.env.VUE_APP_AXIOS_URL,
      // 后端生成图片验证码接口
      imgUrl: "",
      uuid2: "",
      deviceName: storage.ss.get("deviceInfo") ? storage.ss.get("deviceInfo").deviceId : "1",
      smsBtnLoading: false,
    };
  },
  computed: {
    isDisabled() {
      return this.user.phone && this.user.smsCode && this.user.captcha && this.checked ? false : true;
    },
  },
  created() {
    // this.changeImg();
    // 延时解决初次加载不出来问题
    setTimeout(() => {
      this.changeCode();
    }, 500);
  },
  mounted() {},
  watch: {},
  methods: {
    ...mapMutations(["apptoken_fn"]),
    switch2SetPswd() {
      // 忘记密码,设置密码后跳到登录页
      storage.ss.set("isForget", true);
      storage.ss.set("setPhone", this.user.phone);
      this.$router.replace({
        path: "/setPswd",
      });
    },
    changeImg() {
      this.imgUrl = this.prefix + "/appUser/v1/getCaptcha?uuid=" + `${generateUUID()}`;
    },
    /**
     * @description: 验证手机号是否有权限
     * @return {*}
     */

    isPhoneExist() {
      // 验证是否有权限
      isPhoneExist({
        phone: this.user.phone,
        type: this.user.type,
      })
        .then((res) => {
          this.errMsg = "";
        })
        .catch((err) => {
          this.errMsg = "*" + err;
        });
    },

    /**
     * @description: 表单失焦判断
     * @return {*}
     */
    validatePhone(val) {
      if (!val) {
        this.errMsg = "*手机号不能为空";
        return false;
      } else {
        var isPhone = this.$validate.isPhone(val);
        if (isPhone) {
          this.errMsg = "";
          return this.isPhoneExist();
        } else {
          this.errMsg = "*手机号格式不正确";
          return false;
        }
      }
    },
    validateCaptcha(val) {
      if (!val) {
        this.errMsg = "*图片验证码不能为空";
        return false;
      } else {
        this.errMsg = "";
        return true;
      }
    },

    validateSms(val) {
      if (!val) {
        this.errMsg = "*短信验证码不能为空";
        return false;
      } else {
        if (val.length != 4) {
          this.errMsg = "*短信验证码错误";
          return false;
        } else {
          this.errMsg = "";
          return true;
        }
      }
    },
    // 刷新验证码
    changeCode() {
      var uuid = `${generateUUID()}`;
      this.uuid2 = uuid;
      // 调用后台接口
      getCaptchaText({
        uuid: uuid
      })
        .then((res) => {
          this.picCode = res.data;
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 校验&发短信
     * @return {*}
     */

    checkAndSend() {
      // 校验手机号
      var isPhone = this.$validate.isPhone(this.user.phone);
      if (!isPhone) {
        this.errMsg = "* 请输入正确的手机号码";
        return;
      }
      if (this.user.captcha === "") {
        this.errMsg = "* 请填写图片验证码";
        return;
      }
      this.errMsg = "";
      // 发送验证码
      this.checkAndSendCode();
    },

    /**
     * @description: 校验后发送验证码
     * @return {*}
     */

    checkAndSendCode() {
      // 验证是否有权限
      isPhoneExist({
        phone: this.user.phone,
        type: this.user.type,
      })
        .then((res) => {
          if (1001 === res.infoCode) {
            // 跳到未授权提示页面
            this.$router.push({
              path: "/noAuth",
            });
          } else {
            // 发送短信验证码
            this.sendCodeByPhone();
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 发送短信验证码
     * @return {*}
     */

    sendCodeByPhone() {
      this.smsBtnLoading = true;
      sendCode4ForgetPswd({
        phone: this.user.phone,
        captcha: this.user.captcha,
        type: this.user.type,
        deviceName: this.deviceName,
        uuid: this.uuid2,
      })
        .then((res) => {
          if (!res.value) {
            setTimeout(() => {
              this.changeCode();
            }, 500);
            Toast(res.info);
          } else {
            // 倒计时60秒
            this.countdown();
            // 必须延时关闭弹窗  ${phone.substr(0, 3)}****${phone.substr(7, 11)}
            setTimeout(() => {
              Toast(`验证码已发送至: ${this.user.phone.substring(0, 3)}****${this.user.phone.substring(7, 11)}`);
            }, 300);
          }
          this.smsBtnLoading = false;
        })
        .catch((err) => {
          setTimeout(() => {
            this.changeCode();
          }, 500);
          console.log(err);
          this.smsBtnLoading = false;
        });
    },

    /**
     * @description: 表单提交
     * @return {*}
     */

    onSubmit() {
      if (!this.checked) {
        Toast("请勾选同意《隐私政策》及《用户协议》！");
        return;
      }
      checkCode4ForgetPswd({
        phone: this.user.phone,
        smsCode: this.user.smsCode,
        type: this.$setting.APP_TYPE,
        uuid: this.uuid2,
        captcha: this.user.captcha,
      })
        .then((res) => {
          if (!res.value) {
            setTimeout(() => {
              this.changeCode();
            }, 500);
            Toast("短信验证码错误或失效");
          } else {
            this.switch2SetPswd();
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 倒计时60秒
     * @return {*}
     */

    countdown() {
      this.clickNum++;
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.showSend = false;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.showSend = true;
            clearInterval(this.timer); // 清除定时器
            this.timer = null;
          }
        }, 1000);
      }
    },

    /**
     * @description: 协议文本页
     * @return {*}
     */

    switch2PrivacyPolicy() {
      this.$router.push({
        path: "/privacyPolicy",
      });
    },
    switch2UserAgreement() {
      this.$router.push({
        path: "/userAgreement",
      });
    },
  },
};
</script>

<style scoped lang="scss">
.login-code {
  height: 100vh;
  .header {
    img {
      width: 100%;
    }
  }

  .login-card {
    background: #fff;
    margin: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 12px;

    .icon {
      width: 27.5px;
      height: 29.5px;
      vertical-align: sub;
    }

    .err-tips {
      color: #ee0a24;
      // line-height: 1.4;
      padding: 30px 30px 0;
      text-align: left;
      min-height: 20px;
      font-size: 24px;
    }
    .btn-wrap {
      padding: 0 20px;
      position: relative;
      bottom: -48px;
      .van-button--disabled {
        // 按钮透明度
        opacity: 1;
      }
    }

    /deep/ .van-cell {
      background: none;

      #pic-code {
        width: 100px;
        height: 32.5px;
      }
    }
  }

  .loginfooter {
    font-size: 0.8em;
    margin: 80px 40px 0 40px;
    display: flex;
    align-items: center;
    .check-box {
      justify-content: start;
      /deep/ .van-checkbox__icon {
        .van-icon {
          width: 1em;
          height: 1em;
          line-height: 1em;
          border-radius: 50%;
        }
      }
    }
    .text {
      color: #ff9200;
      cursor: pointer;
    }
  }
}
</style>
