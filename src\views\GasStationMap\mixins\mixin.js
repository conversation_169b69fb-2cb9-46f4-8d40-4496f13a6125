import { mapState } from "vuex";
import { storage } from "Utils/common/";
import { GetNavigationList } from "../_service/";
const gasStationMixin = {
  data() {
    return {
      oilStationList: [], // 油站信息
      currentOilStation: {},
      addoiltype: 1, // 智慧后台配置1预授权加油或者2不下车加油;默认显示预约加油
    };
  },
  computed: {
    ...mapState(["rstParams", "deviceInfo"]),
  },
  directives: {},
  methods: {
    GetNavigationList(center) {
      return GetNavigationList({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        distance: _const.distance,
        posx: storage.ss.get("deviceInfo")?.lon || 116.407718,
        posy: storage.ss.get("deviceInfo")?.lat || 39.920248,
        cityCode: "",
        type: "",
        tradeType:'1',
      })
        .then((res) => {
          if (res?.data?.gasStationList?.length > 0) {
            const filtergasStation = res?.data?.gasStationList.filter((item) => item.ifShow == 1);
            if (filtergasStation.length > 0) {
              filtergasStation.map(function(item, index, self) {
                if (item.oilStationCode == "V911") {
                  item.stationName = "中国石油河北张家口太子城服务区加氢站";
                }
              });
              const gasStationList = filtergasStation;
              setTimeout(() => {
                this.oilStationList = gasStationList;
              }, 500);
              
              // todo 如果当前油站（油站列表默认取第一个油站）的经纬度为空，则给油站赋默认地图（北京）的经纬度
              if (!gasStationList[0].posx && !gasStationList[0].posy) {
                this.currentOilStation = gasStationList[0];
                this.currentOilStation.posx = "116.39752";
                this.currentOilStation.posy = "39.908754";
              } else {
                this.currentOilStation = gasStationList[0];
              }
              console.log('addoiltype:1预授权加油；2不下车加油 ====>  addoiltype ==', this.addoiltype);

              if (this.addoiltype == 1) {
                console.log("mixins: getOilCodeListByStationCode 触发");
                this.getOilCodeListByStationCode();
              } else {
                console.log("mixins: getOilProductlist 触发");
                // this.getOilProductlist();
              }
            }
          } else {
            // 解决油站为空地图不展示问题【优化】
            if (!storage.ss.get("deviceInfo")?.city) {
              this.$set(this.currentOilStation, "posx", "116.39752");
              this.$set(this.currentOilStation, "posy", "39.908754");
            } else {
              this.$set(this.currentOilStation, "posx", storage.ss.get("deviceInfo")?.lon);
              this.$set(this.currentOilStation, "posy", storage.ss.get("deviceInfo")?.lat);
            }
          }
        })
        .catch((err) => {});
    },

    // 预授权加油  根据油站编号获取油品信息
    getOilCodeListByStationCode() {
      this.oilType = [];
      const params = {
        appid: 2, //this.rstParams.appid
        apptoken: storage.ss.get("apptoken"),
        stationCode: this.currentOilStation?.oilStationCode,
        // stationCode: 'KKKK',
      };
      this.$http("POST", `/appcenter/preTrade/v1/getOilCodeListByStationCode`, params, true, true, true, false, true)
        .then((res) => {
          if (res.infoCode == 1 && res.data.length > 0) {
            this.oilType = res.data;
            // 处理油品名称
            this.oilType.map(function(item, index, self) {
              if (item.oilName.indexOf("汽油") != -1) {
                if (item.oilName.indexOf("乙醇") != -1) {
                  item.oilNametxt = item.oilName.substring(0, item.oilName.indexOf("号")) + "#乙醇汽油";
                } else {
                  item.oilNametxt = item.oilName.substring(0, item.oilName.indexOf("号")) + "#汽油";
                }
              } else if (item.oilName.indexOf("柴油") != -1) {
                item.oilNametxt = item.oilName.substring(0, item.oilName.indexOf("号")) + "#柴油";
              } else if (item.oilName.indexOf("氢气") != -1) {
                item.oilNametxt = "氢气";
              } else {
                item.oilNametxt = item.oilName;
              }
            });
          } else {
            this.$Dialog.alert({
              title: "提示",
              message: res.info,
            });
          }
        })
        .catch((err) => {
          this.$Dialog.alert({
            title: "提示",
            message: err,
          });
        });
    },
    // 不下车加油 获取油品对应的油枪列表
    getOilProductlist() {
      const params = {
        appid: 2, //this.rstParams.appid
        apptoken: storage.ss.get("apptoken"),
        // stationCode: this.currentOilStation?.oilStationCode,
        stationCode: "KKKK",
      };
      this.$http("POST", `/appcenter/trade/v1/getOilProductlist`, params, true, true, true, false, true)
        .then((res) => {
          if (res.infoCode == 1) {
            this.oilType = res.data.data;
            this.gunNoarr = this.oilType[0].oilGunNo.split(",");
          } else {
            this.$Dialog.alert({
              title: "提示",
              message: res.info,
            });
          }
        })
        .catch((err) => {
          this.$Dialog.alert({
            title: "提示",
            message: err,
          });
        });
    },
    TextWidthChange(e) {
      let odivParent = e.currentTarget.parentNode; //获取目标父元素
      let dx = e.clientX; //当你第一次单击的时候，存储x轴的坐标。
      let dw = odivParent.offsetWidth; //存储默认的div的宽度。
      document.onmousemove = (e) => {
        odivParent.style.width = dw + (e.clientX - dx) + "px";
        if (odivParent.offsetWidth <= 100) {
          //当盒子缩小到一定范围内的时候，让他保持一个固定值，不再继续改变
          odivParent.style.width = "100px";
        }
        if (odivParent.offsetWidth + odivParent.offsetLeft >= this.pdfWidth) {
          odivParent.style.width = this.pdfWidth - odivParent.offsetLeft + "px";
        }
      };
      document.onmouseup = (e) => {
        document.onmousemove = null;
        document.onmouseup = null;
      };
      e.stopPropagation();
      e.preventDefault();
      return false;
    },
  },
  created() {},
  mounted() {},
};
export default gasStationMixin;
