<!--
 * @Author: Yongon
 * @Date: 2022-06-20 10:22:22
 * @LastEditors: ltw <EMAIL>
 * @LastEditTime: 2024-04-28 22:02:46
 * @Description: 迁移组件
-->
<template>
  <van-overlay class="qianyi" :show="isQianYi == 1">
    <!-- <h2 style="color:red">{{isShow}}</h2> -->
    <div class="wrapper qianyi_container" @click.stop>
      <!-- 迁移验证码模块 -->
      <div class="block">
        <div class="tit">
          <span>账户安全验证</span>
          <van-icon v-if="$setting.AuditPhone == currentPhone" name="close" color="#cbcbcb" size="30" class="vant_close" @click.stop="changeQianYi()" />
        </div>
        <div class="tips">为保证您的账户安全,需要进行短信验证码校验。验证码将发送至您的手机号上</div>

        <!-- 密码输入框 -->
        <van-password-input :value="value" :focused="showKeyboard" @focus="showKeyboard = true" :mask="false" length="6" />
        <!-- 数字键盘 -->
        <van-number-keyboard v-model="value" :show="showKeyboard" @blur="showKeyboard = false" />
        <!-- 发送按钮 -->
        <van-button block round :color="$setting.themeColor" size="normal" @click="countdown">{{ btnval }}</van-button>
      </div>
    </div>
  </van-overlay>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import { storage } from "Utils/common/";
import filter from "@/filters/index.js";
import { recordJsLog } from "@/utils/recordJsLog";

export default {
  data() {
    return {
      value: "",
      btnval: "发送迁移验证码",
      showKeyboard: false,
      count: 60,
      timer: null,
      defCardNo: "",
      isShow: false,
      // 迁移次数,2次激活后仍然不成功,跳到我的界面
      activeNum: 0,
    };
  },
  computed: {
    ...mapState(["isQianYi", "currentPhone"]),
  },
  watch: {
    value(val, oldval) {
      if (val.length === 6) {
        console.log("transfer watch value:" + val + "长度为:" + val.length);
        recordJsLog('reapplyCards', { 
          verifyCode: val,
          route: this.$route.name 
        });

        this.$cppeiBridge("reapplyCards", { 
          verifyCode: val,
          route: this.$route.name 
        });
      }
    },
  },
  created() {},
  mounted() {
    window.onSendReapplyCardsVerifyCode = this.onSendReapplyCardsVerifyCode;
    window.onReapplyCards = this.onReapplyCards;
    console.log("transfer 当前路由：" + this.$route.path);
  },
  methods: {
    ...mapMutations(["isQianYi_fn"]),

    /**
     * @description: sdk返回验证码
     * @param {*} obj
     * @return {*}
     */
    onSendReapplyCardsVerifyCode(obj) {
      recordJsLog('onSendReapplyCardsVerifyCode', obj);

      console.log("transfer sdk返回验证码:" + JSON.stringify(obj));
      if (obj.error === 0) {
        let phone = _const.formatPhone(storage.ss.get("phone"));
        this.$toast(`验证码已发送至您${phone}的手机号上`);
      } else {
        this.$Dialog.alert({
          title: "迁移验证码获取失败",
          message: obj.error + "-" + obj.message,
        });
      }
    },

    /**
     * @description: SDK回调迁移是否成功判断
     * @param {*} obj
     * @return {*}
     */
    async onReapplyCards(params) {
      recordJsLog('onReapplyCards', params);
      console.log(params.route + ' ==> transfer SDK回调迁移是否成功判断', JSON.stringify(params));

      // 失败-给出提示，3次或5次后弹出建议重新登录
      if (this.activeNum > 3) {
        this.logOutDialog();
        return;
      }

      if (params.successCards.length != 0) {
        // 首页迁移逻辑
        if (params.route === "gasstationmap") {
          this.$toast(`验证成功,设置默认卡...`);

          // 设置默认卡  判断返回的成功的卡列表中是否包含登录的卡号 有 就将该卡号设置为默认卡 没有就将返回的成功卡列表中的第一个卡号设置为默认卡
          let boolen = params.successCards.some((currentValue, index, arr) => {
            console.log(currentValue, "currentValue");
            return currentValue == storage.ss.get("cardNo");
          });

          console.log(boolen, "boolen");
          //  如果存在登录后的卡和企业号（登录后 会缓存卡号和该卡号的企业号 可直接设置为默认卡）
          if (boolen) {
            this.setDefaultCard(storage.ss.get("defaultCompanyCode"));
          } else {
            // 如果迁移后没有返回 则用迁移返回的第一个卡号 并查询对应的企业号 将其设置为默认企业和默认卡号
            await this.getCompanyInfoByCardNo(params.successCards[0]);
          }
        } else if (params.route === "chooseEnterprise") {
          // 首次再激活迁移逻辑 - 只需要给一个提示即可，设置默认卡操作在激活后设置
          // this.$toast({  //  解决提示层级问题 指定挂载的节点qianyi_container
          //   message: "迁移成功，请再次激活",
          //   getContainer() {
          //     return document.querySelector('.qianyi_container');
          //   },
          // });
          this.$Dialog
            .alert({
              title: "提示",
              message: "迁移成功，请再次激活",
              //  解决提示层级问题 指定挂载的节点qianyi_container
              getContainer() {
                return document.querySelector(".qianyi_container");
              },
            })
            .then(() => {
              //迁移成功,主动保存迁移状态，隐藏迁移弹窗
              this.isQianYi_fn(2); // 2-完成迁移
              recordJsLog('saveReapplyStatus', { reapplyStatus: 2 });

              this.$cppeiBridge("saveReapplyStatus", { reapplyStatus: 2 });
              console.log('transfer 迁移弹窗设置迁移状态为：2');
              this.value = "";
            });
        }
      } else {
        this.isQianYi_fn(1); // 1-需要迁移
        recordJsLog('saveReapplyStatus', { reapplyStatus: 1 });

        this.$cppeiBridge("saveReapplyStatus", { reapplyStatus: 1 });
        console.log('transfer 迁移弹窗设置迁移状态为：1');

        this.activeNum++;

        this.$Dialog.alert({
          title: "迁移失败",
          message: filter.errorMsg(params.failedCards) + "请重新尝试", // 修改错误提示信息格式
          //  解决提示层级问题 指定挂载的节点qianyi_container
          getContainer() {
            return document.querySelector(".qianyi_container");
          },
        });
      }

      // 倒计时结束时清除定时器和设置弹窗默认值
      if (this.count <= 1) {
        this.timer = null;
        this.btnval = "发送迁移验证码";
        this.count = 60;
        clearInterval(this.timer);
      }
    },

    /**
     * @description: 迁移-设置默认卡回调
     * @param {*} obj
     * @return {*}
     */

    onSetDefaultCardFn(params){
      if(params.route === 'transfer'){
        if (params.error === 0) {
          this.$toast("设置成功...");
          //迁移成功,主动保存迁移状态，隐藏迁移弹窗
          this.isQianYi_fn(2); // 2-完成迁移
          recordJsLog('saveReapplyStatus', { reapplyStatus: 2 });

          this.$cppeiBridge("saveReapplyStatus", { reapplyStatus: 2 });
          this.value = "";
        } else {
          this.$Dialog
            .alert({
              title: "迁移设置默认卡失败",
              message: obj.error + "-" + params.message,
            })
          .then(() => {});
        }
      }
    },

    /**
     * @description: 设置默认卡
     * @param {*} defaultCode
     * @return {*}
     */

    setDefaultCard(defaultCode) {
      const params = {
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        companyCode: defaultCode,
        phone: storage.ss.get("phone"),
        type: 2,
      };
      this.$http("POST", `/user/v1/updateDefaultCompanyCode`, params, true, true, true)
        .then((res) => {
          if (res.infoCode === 1) {
            storage.ss.set("defaultCompanyCode", defaultCode);
            storage.ss.set("apptoken", res.data.apptoken);
            storage.ss.set("userId", res.data.id);
            // this.$cppeiBridge("saveInfo", {token: res.data.apptoken,userId: res.data.id,});
            // 调用sdk设置默认卡
            this.setDef(res.data.cardNo);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 设置默认卡
     * @param {*} cardNo
     * @return {*}
     */

    setDef(cardNo) {
      if ("" === cardNo || null === cardNo) {
        Toast("卡号为: 空");
      } else {
        this.defCardNo = cardNo;
        // Toast("sdk设置默认卡,卡号为: " + cardNo);
        // sdk 设置默认卡
        console.log("触发setDefaultCard：" + cardNo);
        recordJsLog('setDefaultCard', {
          pan: cardNo,
          route: 'transfer'
        });

        this.$cppeiBridge("setDefaultCard", {
          pan: cardNo,
          route: 'transfer'
        });
      }
    },

    /**
     * @description: 根据卡号查询对应的企业信息
     * @param {*} cardNo
     * @return {*}
     */

    getCompanyInfoByCardNo(cardNo) {
      return this.$http("post", `/card/appUser/v1/getCompanyInfoByCardNo`, { cardAsn: cardNo }, true, true, true)
        .then((res) => {
          if (res?.data.companyCode) {
            // this.companyCode = res.data.companyCode;
            this.setDefaultCard(res?.data.companyCode);
          } else {
            this.$toast("企业不存在");
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 发送迁移验证码
     * @return {*}
     */
    countdown() {
      // 防止重复点击
      if (this.btnval != "发送迁移验证码") {
        return;
      }
      // 唤sdk发送验证码
      this.sendReapplyCardsVerifyCode();
      const preDate = new Date();
      const count = this.count;
      this.timer = setInterval(() => {
        if (this.count <= 1) {
          clearInterval(this.timer);
          this.timer = null;
          this.btnval = "发送迁移验证码";
          this.count = count;
        } else {
          const nowDate = new Date();
          const diff = Number.parseInt((nowDate.getTime() - preDate.getTime()) / 1000);
          this.count = count - diff;
          this.btnval = this.count + "s";
        }
      }, 1000);
    },

    /**
     * @description: 唤sdk发送验证码
     * @return {*}
     */
    sendReapplyCardsVerifyCode() {
      recordJsLog('sendReapplyCardsVerifyCode');
      
      this.$cppeiBridge("sendReapplyCardsVerifyCode", {});
    },

    /**
     * @description:
     * @return {*}
     */

    logOutDialog() {
      this.$Dialog
        .confirm({
          title: "提示",
          message: "多次迁移失败，建议重新登录",
        })
        .then(() => {
          this.$cppeiBridge("clearToken", {});
          storage.ss.remove("apptoken");
          storage.ss.clear();
          this.$router.replace({
            path: "/loginCenter",
          });
        })
        .catch(() => {});
    },

    /**
     * @description: 模拟软件商店审核测试人员进行上线审核
     * @return {*}
     */

    changeQianYi() {
      this.isQianYi_fn(2); // 2-完成迁移
      recordJsLog('saveReapplyStatus', { reapplyStatus: 2 });

      this.$cppeiBridge("saveReapplyStatus", { reapplyStatus: 2 });
    },
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  padding-top: 10px;
  width: 90vw;
  height: auto;
  background-color: #fff;
  border-radius: 2vw;

  .tit {
    width: 100%;
    height: 80px;
    line-height: 80px;
    padding-top: 20px;
    // text-align: center;
    // border: 1px solid red;
    font-size: 36px;
    color: rgb(255, 146, 0);
    position: relative;
    .vant_close {
      position: absolute;
      right: 10px;
      top: 0px;
    }
  }

  .tips {
    margin: 0 auto;
    text-align: left;
    margin: 10px 3.5vw 30px;
    font-size: 20px;
    line-height: 1.5em;
  }

  .van-password-input__security {
    height: 40px;
  }

  button {
    // 按钮透明度
    width: 70% !important;
    margin: 30px auto;
  }
}
</style>
