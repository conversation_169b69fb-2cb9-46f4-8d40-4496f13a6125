<template>
  <div class="no-service-station">
    <div class="tip-text">附近暂无不下车加油站</div>
    <van-button class="tip-button" @click="toGasStationTap()">预授权加油</van-button>
  </div>
</template>

<script>
export default {
  props: {},
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {
    toGasStationTap(){
      this.$router.push({
        path: "/gasstationmap",
        query: '',
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.no-service-station {
  width: 100%;
  height: 100%;
  background: #fff;
  .tip-text {
    padding-top: 300px;
  }
  .tip-button{
    margin-top: 100px;
    border: 1px solid #468DB1;
   
  }
  .van-button--normal {
    height: 40px;
    color: #468DB1;
    padding: 20px 40px;
    border: 1px solid #468DB1;
  }
}
</style>
