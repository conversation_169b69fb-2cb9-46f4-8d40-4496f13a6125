/*
 * @Author: <PERSON>on
 * @Date: 2022-08-18 14:10:34
 * @LastEditors: Yongon
 * @LastEditTime: 2022-09-06 10:36:41
 * @Description: file content
 */
import request from "Utils/http/"; // 导入axios封装函数

// 获取登录设备
export async function getEquipments(params) {
  return request("post", "/user/v1/getLoginEquipmentInfo", params, true, true, true, false, true);
}

// 重置登录密码
export async function resetPswd(params) {
  return request("post", "/user/v1/resetPassword", params, true, true, true, false, true);
}

// 获取消费记录
export async function getRecordList(params) {
  return request("post", "/user/v1/getConsumeRecord", params, true, true, true, false, true);
}

// 获取充值记录
export async function getCardTradeList(params) {
  return request("post", "/user/v1/getCardTradeList", params, true, true, true, false, true);
}

// 获取某企业司机卡信息
export async function getDriverInfo(params) {
  return request("post", "/user/v1/getDriverInfo", params, true, true, true, false, true);
}

// 获取激活企业列表
export async function getActivatedList(params) {
  return request("post", "/user/v1/getActivatedList", params, true, true, true, false, true);
}

// 获取未激活企业列表
export async function getUnActivatedList(params) {
  return request("post", "/user/v1/getUnActivatedList", params, true, true, true, false, true);
}

// 开通司机卡
export async function doOpenCards(params) {
  return request("post", "/user/v1/activateCompanyOrDriver", params, true, true, true, false, true);
}

// 修改车牌
export async function ModifyLicenseNum(params) {
  return request("post", "/user/v1/modifyLicenseNum", params, true, true, true, false, true);
}

// 修改司机默认的企业
export async function updateDefaultCompanyCode(params) {
  return request("post", "/user/v1/updateDefaultCompanyCode", params, true, true, true, false, true);
}

// 重置支付密码发短信验证码
export async function sendCodeNoCaptcha(params) {
  return request("post", "/card/appUserSms/v1/getPayPasswordSms", params, false, true, true, false, true);
}

// 校验重置支付密码短信验证码
export async function checkSmsCode(params) {
  return request("post", "/card/appUserSms/v1/checkPayPasswordBySms", params, false, true, true, false, true);
}

// 改变卡片状态
export async function changeState(params) {
  return request("post", "/card/appUser/v1/activeCards", params, true, true, true, false, true);
}

// 销户申请
export async function CancelECardApply(params) {
  return request("post", "/card/approve/v1/launch", params, true, true, true, false, true);
}

// 支付宝充值
export async function aliPay(params) {
  return request("post", "/appcenter/aliPay/v1/addOrder", params, true, true, true, false, true);
}
// 查询订单信息
export async function GetOrderInfo(params) {
  return request("post", "/appcenter/aliPay/v1/getOrderInfo", params, true, true, true, false, true);
}
// 支付宝充值结果查询
export async function ReceiveAliRechargeNotity(params) {
  return request("post", "/appcenter/aliPay/v1/receiveAliRechargeNotity", params, true, true, true, false, true);
}

// 获取加油卡信息
export function GetUserCardInfo(params) {
  return request("post", "/card/v1/getUserCardInfo", params, true, true, true, false, true);
}

// 车队卡审批销户/管理端销户风控接口【新增】 雷天伟
export async function cancelRiskManagement(params) {
  return request("post", "/appcenter/riskManagement/v1/cancelRiskManagement", params, true, true, true, false, true);
}
