<template>
  <div class="all-consuming-records">
    <div class="nav-header">
      <div class="placeholder" :style="{ height: placeholderHeigth }">
      <div class="content" :style="{ height: contentHeight, paddingTop: topSafeArea }">
        <div class="left" @click="onClickLeft">
          <span v-if="leftArrow"> <van-icon name="arrow-left" color="#fff"/></span>
        </div>
        <div class="title">消费记录</div>
        <div class="right">
          <span v-if="rightIcon"> <van-icon name="add" size="22" color="#fff"/></span>
        </div>
      </div>
    </div>
    </div>
    <div class="tips">本查询结果只显示通过APP消费的三个月内的消费记录</div>
    <div class="date-choose">
      <van-cell :title="dates" value="" @click="onDisplay">
        <template slot="right-icon">
          <img src="@/assets/images/icon/calendar.jpg" class="icon" />
        </template>
      </van-cell>
      <!-- 日期选择 -->
      <van-calendar
        v-model="show"
        type="range"
        confirm-text="查询"
        :allow-same-day="true"
        :min-date="minDate"
        :max-date="maxDate"
        :max-range="range"
        @close="onClose"
        @confirm="onConfirm"
        :color="$setting.themeColor"
      />
    </div>
    <!-- 列表 -->
    <van-pull-refresh v-model="isLoading" @refresh="onRefresh">
      <van-empty v-if="!hasData" description="暂无消费记录" />
      <div class="container" v-else>
        <template>
          <!-- :immediate-check="false"  解决接口加载2次的问题 -->
          <van-list
            v-model="loading"
            :finished="finished"
            finished-text="- 没有更多了 -"
            @load="onLoad"
            :immediate-check="false"
            :offset="50"
          >
            <div
              class="record-list"
              v-for="(record, index) in recordList"
              :key="index"
              @click="goConsumingDetail(record)"
            >
              <div class="order-item">
                <span>订单号: {{ record.orderNo }}</span>
                <span id="money">￥ {{ record.realAmount }}</span>
              </div>
              <div class="order-time">
                <span>{{ record.paymentTime }}</span>
              </div>
            </div>
          </van-list>
        </template>
      </div>
    </van-pull-refresh>
  </div>
</template>

<script>
import { getRecordList } from "@/views/My/_service";
// 2种方式导入都可以,@代表src, ../父目录 ./当前目录 /根目录
// import { getRecordList } from ".././_service";
// import navBar from "@/components/navBar/NavHeader";
import { storage } from "Utils/common/";
import moment from "moment";

export default {
  props: {
    title: {
      type: String,
      default: "",
    },
    leftArrow: {
      type: Boolean,
      default: true,
    },
    rightIcon: {
      type: Boolean,
      default: false,
    },
  },
  // components: { navBar },
  data() {
    return {
      // 设备编号
      deviceName: storage.ss.get("deviceInfo")
        ? storage.ss.get("deviceInfo").deviceId
        : "1",
      show: false,
      dates: "近三个月",
      // 结束时间最大为当前时间
      minDate: new Date(moment().subtract(3,'month').format('YYYY-MM-DD')),
      maxDate: new Date(moment(new Date()).format('YYYY-MM-DD')),
      // 最多可选90天
      range: "90",
      // 消费记录
      recordList: [
        // 订单编号 orderNo;
        // 订单实付金额 rcvAMT;
        // 支付时间 paymentTime;
      ],
      // 每页条数
      pageSize: 20,
      // 开始时间
      startDate: "",
      // 结束时间
      endDate: "",
      page: 1, //当前页
      loading: false, // 当loading为true时，转圈圈
      finished: false, // 数据是否请求结束，结束会先显示- 没有更多了 -
      hasData: true, // 如果没有数据，显示暂无数据
      isLoading: false, // 下拉的加载图案
    };
  },
  computed: {
    // methods和computed的区别: methods 多次调用会执行多次, 计算属性多次调用只会执行1次
    // minDate: function () {
    //   const BEFORE_90 = -(90 - 1);
    //   var day = new Date();
    //   var targetDay = day.getTime() + 1000 * 60 * 60 * 24 * BEFORE_90;
    //   day.setTime(targetDay);
    //   return day;
    // },
  },
  created() {},
  activated() {},
  mounted() {
    this.onRefresh();
    this.startDate = moment(this.minDate).format("yyyy-MM-DD");
    this.endDate = moment(this.maxDate).format("yyyy-MM-DD");
  },
  computed: {
    topSafeArea() {
      return Number(storage.ss.get("statusHeight") ?? 30) + "px";
    },
    placeholderHeigth() {
      return Number((storage.ss.get("statusHeight") ?? 30) + 46) + "px";
    },
    contentHeight(){
      return '46px';
    },
  },
  watch: {},
  methods: {
    onClickLeft() {
      this.$router.push({ path: "/my" });
    },
    onDisplay() {
      this.show = true;
    },
    onClose() {
      this.show = false;
    },
    formatDate(date) {
      return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
    },
    onConfirm(date) {
      const [start, end] = date;
      this.show = false;
      this.dates = `${this.formatDate(start)} 至 ${this.formatDate(end)}`;
      this.page = 1;
      this.startDate = moment(start).format("yyyy-MM-DD");
      this.endDate = moment(end).format("yyyy-MM-DD");
      // 刷新数据
      this.onRefresh();
    },
    // 列表加载
    onLoad() {
      setTimeout(() => {
        this.getListByPage();
        this.loading = true;
      }, 300);
    },
    onRefresh() {
      // this.getListByPage();
      setTimeout(() => {
        // 重新初始化这些属性
        this.isLoading = false;
        this.recordList = [];
        this.page = 1;
        this.loading = false;
        this.finished = false;
        this.hasData = true;
        // 查询数据
        this.getListByPage();
      }, 300);
    },
    // 分页查询数据
    getListByPage() {
      getRecordList({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        companyCode: storage.ss.get("defaultCompanyCode"),
        curPage: this.page,
        pageSize: this.pageSize,
        startDate: this.startDate,
        endDate: this.endDate,
        plateNo: this.plateNo?.length > 6 ? this.plateNo : '',
      })
        .then((res) => {
          // 追加数据
          this.recordList = this.recordList.concat(res.data.list);
          // 加载状态结束
          this.loading = false;
          // 如果没有数据，显示暂无数据
          if (this.recordList.length === 0 && this.page === 1) {
            this.hasData = false;
          }
          // 页码加1
          this.page++;
          // 如果加载完毕，显示没有更多了
          if (res.data.list.length === 0) {
            this.finished = true;
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    goConsumingDetail(item) {
      this.$router.push({path:'/my/AllConsumingDetail', query: {recordDetail: JSON.stringify(item)}},);
    }
  },
};
</script>

<style scoped lang="scss">
.all-consuming-records {
  .nav-header {
    .content {
      background: #ff9200;
      // height: 89px;
      color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 999;

      .left,
      .right {
        padding: 0 16px;
      }

      .title {
        max-width: 60%;
        font-weight: 500;
        font-size: 36px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .tips {
    padding: 20px;
    text-align: left;
    margin-bottom: 30px;
    background-color: #ffe5da;
    color: #ff9200;
  }
  .date-choose {
    .van-cell__title {
      text-align: left;
      span {
        color: #c70d0a;
        font-size: 15px;
        width: 66vw;
        display: inline-block;
      }
    }
    img {
      height: 4.5vw;
    }
  }
  /deep/ .van-pull-refresh {
    background-color: rgba(239, 239, 239, 100%) !important;
    height: 75vh !important;
    padding-bottom: 3vw;
    @include useScrollByBrowser(0);
    .container {
      height: 75vh;
      .no-record {
        background-color: #fff;
        padding: 30px 0;
        margin-top: 2px;
      }
      .van-list {
        .record-list {
          height: auto;
          margin-bottom: 2px;
          padding: 10px;
          background-color: #fff;
          .order-item,
          .order-time {
            height: 25px;
            display: flex;
            justify-content: space-between;
            #money {
              color: #c70d0a;
            }
          }
        }
      }
    }
  }
}
</style>
