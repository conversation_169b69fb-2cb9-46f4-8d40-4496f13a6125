---
description: 中油智行车队卡司机端H5前端API接口规范
globs: *.js,*.vue
alwaysApply: true
---

# 前端API接口规范

## 📋 总体描述

本文档定义了中油智行车队卡司机端H5项目的前端API接口规范，基于Axios 0.19.2版本，涵盖HTTP封装、接口调用、响应处理、错误处理等核心内容，确保API接口的统一性和可维护性。

## 🎯 应用范围

- **HTTP请求**：所有与后端的数据交互
- **接口封装**：统一的请求和响应处理
- **错误处理**：网络异常和业务错误的统一处理
- **请求拦截**：请求参数的统一处理和权限验证
- **响应拦截**：响应数据的统一格式化和错误处理

## 📖 使用要求

1. **统一封装**：所有API请求必须使用项目封装的HTTP实例
2. **标准格式**：遵循项目定义的请求和响应格式
3. **错误处理**：合理处理网络异常和业务错误
4. **取消机制**：支持请求取消，避免重复请求
5. **安全性**：敏感信息加密传输，防止数据泄露

## 🏗️ 规则1：HTTP封装配置

### 基础配置

**配置文件**：`src/utils/http/index.js`

**标准HTTP实例**：

```javascript
// src/utils/http/index.js
import axios from "axios";
import store from "Store/index";
import { bangbangEncrypt } from "Utils/bangbangEncrypt";

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_API_URL,
  timeout: 30000,
  headers: {
    "Content-Type": "application/json;charset=UTF-8"
  }
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 添加取消令牌
    config.cancelToken = new axios.CancelToken(cancel => {
      window._axiosPromiseArr = window._axiosPromiseArr || [];
      window._axiosPromiseArr.push({ cancel });
    });

    // 加密请求参数
    if (config.data) {
      config.data = bangbangEncrypt(JSON.stringify(config.data));
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data;

    // 统一响应格式处理
    if (res.Value === false) {
      // 业务错误处理
      console.error('API业务错误:', res.Info);
      return Promise.reject(new Error(res.Info));
    }

    return response;
  },
  error => {
    // 网络错误处理
    console.error('网络请求错误:', error);
    return Promise.reject(error);
  }
);

export default service;
```

#### 规则2 响应格式定义

```typescript
interface ApiResponse<T> {
  code: number
  message: string
  data: T
}
```

#### 规则3 状态码定义

- 2xx: 成功
- 4xx: 客户端错误（比如401未授权、403无权限等）
- 5xx: 服务器错误

#### 规则4 请求拦截

- 统一添加 token
- 处理请求参数
- 添加时间戳防止缓存

#### 规则5 响应拦截

- 统一处理错误响应
- 处理 401、403 未授权等特殊逻辑
- 格式化响应数据
