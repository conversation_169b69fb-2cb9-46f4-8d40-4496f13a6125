---
description: 前端 API 接口开发规范
globs: .ts,.vue,*.js
alwaysApply: true
---

### API规则

#### 总体描述

该文档主要对前端 API 接口相关规则进行了说明，包括 API 定义、响应格式定义、状态码定义以及请求和响应拦截等内容，有助于规范前端与后端的接口交互。

#### 应用范围
本规范适用于所有前端项目中与后端进行接口交互的开发场景，确保前端开发人员能够正确地定义、使用和处理 API 接口。

#### 使用要求
开发人员在进行前端 API 接口开发时，需要严格按照本规范中的 API 定义、响应格式定义、状态码定义以及请求和响应拦截等方面的要求进行开发。在使用 axios 实例发起请求时，要确保遵循 RESTful API 设计规范，并正确处理请求和响应。

#### 规则1 API定义

- API 请求方法统一在 `src/api` 目录下管理
- 使用 TypeScript 定义请求参数和响应类型
- 遵循 RESTful API 设计规范
- 统一使用 axios 实例发起请求

#### 规则2 响应格式定义

```typescript
interface ApiResponse<T> {
  code: number
  message: string
  data: T
}
```

#### 规则3 状态码定义

- 2xx: 成功
- 4xx: 客户端错误（比如401未授权、403无权限等）
- 5xx: 服务器错误

#### 规则4 请求拦截

- 统一添加 token
- 处理请求参数
- 添加时间戳防止缓存

#### 规则5 响应拦截

- 统一处理错误响应
- 处理 401、403 未授权等特殊逻辑
- 格式化响应数据
