import Vue from "vue";
import VueRouter from "vue-router";

import store from "../store/index";
import routes from "./routes";
import { isAndroid } from "Utils/common/";

// 开发环境开启
// if (process.env.NODE_ENV === "development") {
//   Vue.use(VueRouter);
// }
Vue.use(VueRouter);

const router = new VueRouter({
  base: "/fleetcard_driver",
  mode: "history",
  routes,
});

// 解决router版本3.1.3报 Uncaught (in promise) undefined错误提示
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject);
  return originalPush.call(this, location).catch((err) => err);
};

router.beforeEach((to, from, next) => {
  // store.commit('clearToken'); // 取消请求
  if (window._axiosPromiseArr) {
    window._axiosPromiseArr.forEach((ele, index) => {
      ele.cancel();
      delete window._axiosPromiseArr[index];
    });
  }
  if (to.meta.title) {
    document.title = to.meta.title;
    // 如果是 iOS 设备，则使用如下 hack 的写法实现页面标题的更新
    // if (navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
    if (!isAndroid()) {
      const hackIframe = document.createElement("iframe");
      hackIframe.style.display = "none";
      hackIframe.src = "/static/html/fixIosTitle.html?r=" + Math.random();
      document.body.appendChild(hackIframe);
      setTimeout((_) => {
        document.body.removeChild(hackIframe);
      }, 300);
    }
  }
  // 查找当前路由是否已存在
  const { navigations } = store.state;
  let isHasIndex = navigations.findIndex((el) => el.name === to.name);
  if (isHasIndex >= 0) {
    // 若存在
    if (isHasIndex === navigations.length - 1) {
      // 当前进入的页面为同一个页面
      console.log("router 刷新");
    } else {
      // 那证明 需要后退
      store.commit("Upadate_curRouterType", {
        type: "back",
      });
      // 同时删除该路径后面的所有已存路径
      store.commit("Delete_otherRouterPath", {
        index: isHasIndex,
      });
    }
  } else {
    // 不存在 则表示前进 且此时生成随机数
    store.commit("Upadate_curRouterType", {
      type: to.meta.isNoAni && !!to.meta.isNoAni ? "" : "forward",
    });

    let keyP = Math.random()
      .toString(16)
      .substring(2);
    store.commit("Update_navigations", {
      name: to.name,
      keyP,
    });
  }
  next();
});

Vue.mixin({
  beforeRouteLeave: function(to, from, next) {
    // 利用mixin来缓存每个页面的当前vue实例
    // console.log(this.$vuePageInstance)
    if (this.$vuePageInstance.length === 0) {
      var obj = {};
      obj["pageName"] = from.name;
      obj["pageInstance"] = this;
      // 直接压入
      this.$vuePageInstance.push(obj);
    } else {
      // 判断是否已存
      let onOff = false;
      this.$vuePageInstance.map((el) => {
        if (el.pageName === from.name) {
          // 存在
          onOff = true;
        }
      });
      if (!onOff) {
        this.$vuePageInstance.push({
          pageName: from.name,
          pageInstance: this,
        });
      }
    }
    // console.log(this.$vuePageInstance)

    // 针对index为0的页面重定向到某个页面 则全盘清除缓存
    if (from.meta.index === 0) {
      if (this.$vnode && this.$vnode.data.keepAlive) {
        if (this.$vnode.parent && this.$vnode.parent.componentInstance && this.$vnode.parent.componentInstance.cache) {
          this.$vnode.parent.componentInstance.cache = [];
          this.$vnode.parent.componentInstance.keys = [];
          // 清除所有vue实例缓存
          this.$vuePageInstance.map((el) => {
            let vueInstance = el.pageInstance;
            vueInstance.$destroy();
          });
          // 重置
          this.$vuePageInstance = [];
        }
      }
      next();
    }
    // 针对前进后退 来清除回退的那个页面
    if (from && from.meta.index && to.meta.index && from.meta.index > to.meta.index) {
      // console.log("清除缓存");
      // 此处判断是如果返回上一层，你可以根据自己的业务更改此处的判断逻辑，酌情决定是否摧毁本层缓存。
      if (this.$vnode && this.$vnode.data.keepAlive) {
        if (this.$vnode.parent && this.$vnode.parent.componentInstance && this.$vnode.parent.componentInstance.cache) {
          if (this.$vnode.componentOptions) {
            var key = this.$vnode.key == null ? this.$vnode.componentOptions.Ctor.cid + (this.$vnode.componentOptions.tag ? `::${this.$vnode.componentOptions.tag}` : "") : this.$vnode.key;
            var cache = this.$vnode.parent.componentInstance.cache;
            var keys = this.$vnode.parent.componentInstance.keys;
            if (cache[key]) {
              if (keys.length) {
                var index = keys.indexOf(key);
                if (index > -1) {
                  keys.splice(index, 1);
                }
              }
              delete cache[key];
            }
          }
        }
      }
      this.$destroy();

      // 同时清理缓存的对应实例
      this.$vuePageInstance.pop();
    }
    next();
    // console.log(this.$vuePageInstance)
  },
});

export default router;
