const obj_constant = {
  selfKey: "oOYYVuVO2GKi0Clafd1VfWME", //百度地图key
  distance: "20", //百度地图请求附近加油站的范围距离
  pollingParams: {
    //轮循参数  已完成预授权订单查询轮循
    timeInterval: 3000, //轮循时间间隔
    totalTime: 15000, //轮循总时间
    payOrderTotalTime: 5000, //查询室内支付订单信息接口轮循总时间
  },
  oilType: [
    //测试环境   北京    四川    重庆
    // { oilNum: "92#汽油",  oilVal: "300668", },//300668  300775  300874  300865
    // { oilNum: "95#汽油",  oilVal: "300667", },//300667  300776  300873  300867
    // { oilNum: "98#汽油",  oilVal: "300684", },//300684  300777  300940  300866
    // { oilNum: "0#柴油",   oilVal: "300644", },//300644  300771  300863  300863
    // { oilNum: "-10#柴油", oilVal: "300568", },//300568  300772  300864
    // { oilNum: "-20#柴油", oilVal: "300567", },//300567  300773
    // { oilNum: "-35#柴油", oilVal: "300575", },//300575  300774
  ],
  oilTypesc: [
    {
      oilNum: "92#汽油",
      oilVal: "300874",
    },
    {
      oilNum: "95#汽油",
      oilVal: "300873",
    },
    {
      oilNum: "98#汽油",
      oilVal: "300940",
    },
    {
      oilNum: "0#柴油",
      oilVal: "300863",
    },
    {
      oilNum: "-10#柴油",
      oilVal: "300864",
    },
  ],
  oilTypebj: [
    {
      oilNum: "92#汽油",
      oilVal: "300775",
    },
    {
      oilNum: "95#汽油",
      oilVal: "300776",
    },
    {
      oilNum: "98#汽油",
      oilVal: "300777",
    },
    {
      oilNum: "0#柴油",
      oilVal: "300771",
    },
    {
      oilNum: "-10#柴油",
      oilVal: "300772",
    },
    {
      oilNum: "-20#柴油",
      oilVal: "300773",
    },
    {
      oilNum: "-35#柴油",
      oilVal: "300774",
    },
  ],
  oilTypecq: [
    {
      oilNum: "92#汽油",
      oilVal: "300865",
    },
    {
      oilNum: "95#汽油",
      oilVal: "300867",
    },
    {
      oilNum: "98#汽油",
      oilVal: "300866",
    },
    {
      oilNum: "0#柴油",
      oilVal: "300863",
    },
  ],
  formatPhone: (phone) => {
    phone = phone.toString();
    return `${phone.substr(0, 3)}****${phone.substr(7, 11)}`;
  },
};
let bindToGlobal = (obj, key = "_const") => {
  if (typeof window[key] === "undefined") {
    window[key] = {};
  }

  for (let i in obj) {
    window[key][i] = obj[i];
  }
};
bindToGlobal(obj_constant);
