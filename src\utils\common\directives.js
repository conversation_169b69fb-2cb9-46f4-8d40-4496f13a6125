/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-04-20 10:24:30
 * @LastEditors: Yongon
 * @LastEditTime: 2022-05-10 16:07:02
 * @Description: 自定义指令(扩展vue指令，根据实际情况来定，比如函数的节流防抖可以用指令来做,还有我遇到的按钮权限等)
 */
export default (Vue) => {
  Vue.directive('dragging', {
    inserted(el, binding, vnode) {
      // console.log(binding.value)
      let current = el; //当前元素
      let crel = document.getElementById(binding.value); //传入的id对应dom
      let targetDiv = crel || document.getElementById(el.id);
      const domHit = crel ? crel.offsetHeight : el.offsetHeight;
      // console.log(domHit)
      var startY = 0;
      var endY = 0;
      var diffY = 0;
      el.addEventListener('touchstart', function (event) {

        var touch = event.targetTouches[0];
        startY = startY || touch.pageY;
        el.addEventListener('touchmove', (event) => {

          touch = event.targetTouches[0];

          endY = touch.pageY;
          diffY = endY - startY;
          // let th = document.body.clientHeight - diffY * 2 - 30;
          let th = document.body.clientHeight - touch.screenY;
          if (th < 100) {
            th = 100;
          }
          if (th > domHit) {
            th = domHit;
          }
          // console.log(document.body.clientHeight + '    ' + touch.screenY + '     ' + th)

          targetDiv.style.height = th + "px";
        })
        el.addEventListener('touchend', (event) => {
          // startY = 0;
          // endY = 0;
          // diffY = 0;
        })

      });

    },
  })
  Vue.directive('focus', {
    inserted(el, binding, vnode) {
      el.onfocus = () => {
        console.log(el)
      }
      el.onblur = () => {
        console.log(el)
      }
    },
  })
}