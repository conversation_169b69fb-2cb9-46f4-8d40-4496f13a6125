/*
 * @Author: <PERSON>on
 * @Date: 2022-06-29 08:46:31
 * @LastEditors: Yongon
 * @LastEditTime: 2022-10-18 10:00:52
 * @Description: file content
 */
import request from './http'; // 导入axios封装函数
import router from '../router';
import cppeiBridge from "Utils/bridge";
import { storage } from "Utils/common/";
import { Toast, Dialog } from "vant";

/*
* 验证电子卡是否注销:获取已激活企业列表和司机信息进行比较
* 若激活企业列表不包含司机信息中的默认企业和电子卡号，则已注销
*/
const isCancellation = async function() {
  let driverInfo = {},
      errorApi = false,
      isCancel = true;
  await request('POST',`/user/v1/getDriverInfo`, {  // 司机信息
    appid: 2,
    apptoken: storage.ss.get("apptoken"),
    companyCode: storage.ss.get("defaultCompanyCode"),
    phone: storage.ss.get("phone"),
    userid: storage.ss.get("userId"),
  }, true, true, true,false,false).then(res => {
    if(res.infoCode == 1 && res.data) {
      driverInfo = res.data || {};
      storage.ss.set("driverInfo", res.data);
    }
  }).catch(err => {})
  
  await request('POST',`/user/v1/getActivatedList`, {  // 已激活企业列表
    appid: 2,
    apptoken: storage.ss.get("apptoken"),
    phone: storage.ss.get("phone"),
    curPage: 1,
    pageSize: 99,
    type: 2, // 1企业端，2司机端
  }, true, true, true,false,true).then(list => {
    if(list.infoCode == 1) {
      if(list.data.length > 0) {
        isCancel = list.data.some(item => {
          return item.cardNo == driverInfo.cardNo && item.companyCode == driverInfo.companyCode
        });
      } else {
        isCancel = false;
      }
    } else { 
      errorApi = true;
    }
  }).catch(err => {
    errorApi = true;
  })

  if(!isCancel) {
    // 通知后台该司机已被卡系统线下销户
    await request('POST',`/user/v1/unboundDriver`, {  // 司机对应卡解绑
      appid: 2,
      apptoken: storage.ss.get("apptoken"),
      cardNo: driverInfo.cardNo,
      companyCode: driverInfo.companyCode,
      flag: "1", // 0切换企业  1其他
      isActive: 2,
      phone: storage.ss.get("phone"),
      type: "2", // 1企业 2司机
    }, true, true, true,false,true).then(res => {
      if(res.infoCode == 1) {
        Dialog.alert({
          title: '温馨提示',
          message: `${storage.ss.get("driverInfo").companyName}的司机账户已注销，请重新登录。如有问题请与企业管理员联系`,
          confirmButtonText: '知道了',
        }).then(() => {});
        router.replace({path: "/loginCenter",});
        cppeiBridge("clearToken", {});
        storage.ss.remove("apptoken");
      }
    }).catch(err =>{})
  }
  return {isCancel: isCancel, errorApi: errorApi}
}

export {
  isCancellation
}