<template>
  <div class="my-home">
    <!-- 顶部司机卡信息展示 -->
    <div class="nav-bar">
      <div class="title">我的</div>
      <div class="setting" @click="switch2Setting">
        <van-icon name="setting-o" color="#fff" size="22" />
      </div>
      <!-- 固定图片 -->
      <div class="head-portrait"></div>
      <div class="details">
        <p>{{ driverInfo.name }}</p>
        <p>{{ driverInfo.companyName }}-{{ driverInfo.companyCode }}</p>
      </div>
    </div>
    
    <!-- 卡信息 -->
    <section class="card-item">
      <van-cell-group>
        <van-cell title="姓名：" :value="driverInfo.name" />
        <van-cell title="证件号码：" :value="driverInfo.idNo" />
        <van-cell title="手机号：" :value="driverInfo.phone" />
        <van-cell title="卡号：" :value="driverInfo.cardNo" />
      </van-cell-group>
    </section>

    <!-- 链接 -->
    <div class="my_cont">
      <div class="my-links">
        <van-cell is-link link-type="reLaunch" to="/my/ecard">
          <template #title>
            <van-icon name="card" :color="$setting.themeColor" size="18" />
            <span>我的加油卡</span>
          </template>
        </van-cell>
        <van-cell is-link link-type="reLaunch" to="/my/allConsumingRecords">
          <template #title>
            <van-icon name="balance-list" :color="$setting.themeColor" size="18" />
            <span>消费记录</span>
          </template>
        </van-cell>
        <van-cell is-link link-type="reLaunch" to="/my/applyMoney" v-if="moneyType == 0">
          <template #title>
            <van-icon name="add-square" :color="$setting.themeColor" size="18" />
            <span>资金申请</span>
          </template>
        </van-cell>
        <van-cell is-link link-type="reLaunch" to="/my/openNewCard">
          <template #title>
            <van-icon name="add-square" :color="$setting.themeColor" size="18" />
            <span>开通新的司机卡</span>
          </template>
        </van-cell>
        <van-cell is-link link-type="reLaunch" to="/my/closeAccount">
          <template #title>
            <van-icon name="clear" :color="$setting.themeColor" size="18" />
            <span>电子司机卡销户申请</span>
          </template>
        </van-cell>
        <van-cell is-link link-type="reLaunch" to="/my/security" use-label-slot>
          <template #title>
            <van-icon name="manager" :color="$setting.themeColor" size="18" />
            <span>账户安全</span>
          </template>
        </van-cell>
        <van-cell is-link link-type="reLaunch" to="/my/deviceManagement">
          <template #title>
            <van-icon name="setting" :color="$setting.themeColor" size="18" />
            <span>登录设备管理</span>
          </template>
        </van-cell>
        <div class="version">版本号：{{ version }}</div>
      </div>
    </div>
  </div>
</template>

<script>
  import logo from "@/components/logo";
  import {
    getDriverInfo,
    getActivatedList,
  } from "./_service";
  import {
    storage,
    nameDesensitization,
    phoneDesensitization,
    idCardDesensitization,
  } from "Utils/common/";



  export default {
    props: {},
    components: {
      logo,
    },
    data() {
      return {
        deviceName: storage.ss.get("deviceInfo")?.deviceId ?? "2", // 设备编号
        driverInfo: {},
        name: storage.ss.get("name"), // 司机姓名
        allCompanyList: [],
        defaultCompanyCode: storage.ss.get("defaultCompanyCode"),
        version: storage.ss.get("deviceInfo") ?.version ?? "V1.0.5",
        moneyType: storage.ss.get("driverInfo") ?.moneyType,
      };
    },
    computed: {
      cellTitle() {
        return function (item) {
          var title = item.companyName + " - " + item.companyCode;
          // 过长显示...
          return title.length > 32 ? title.slice(0, 32) + "..." : title;
        };
      },
    },
    created() {
      // 查询已激活列表
      this.loadActivedList();
    },
    mounted() {
      console.log("my mounted");
    },
    activated(){
      console.log(123456);
    },
    watch: {},
    methods: {
      /**
       * @description: 点击设置按钮
       * @return {*}
       */      
      switch2Setting() {
        this.$router.push({
          path: "/my/setting",
        });
      },

      /**
       * @description: 获取激活企业列表
       * @return {*}
       */      
      loadActivedList() {
        getActivatedList({
          appid: 2,
          apptoken: storage.ss.get("apptoken"),
          phone: storage.ss.get("phone"),
          // type: 2,
          curPage: 1,
          pageSize: 10,
          type: this.$setting.APP_TYPE,
        }).then((res) => {
          this.allCompanyList = res.data || [];
          // 加载,不设置默认卡
          this.loadDriverInfo(storage.ss.get("apptoken"));

        }).catch((err) => {
          console.log(err);
        });
      },

      /**
       * @description: 获取企业司机卡信息
       * @param {*} token
       * @return {*}
       */      
      loadDriverInfo(token) {
        getDriverInfo({
          appid: 2,
          apptoken: token,
          companyCode: this.defaultCompanyCode || storage.ss.get("defaultCompanyCode"),
          phone: storage.ss.get("phone"),
          userid: storage.ss.get("userId"),
        }).then((res) => {
          if (res.data === null) {
            this.$Dialog.alert({
              title: "提示",
              message: "无企业信息,切换失败,请联系管理员",
            });
            return;
          }
          // 保存
          storage.ss.set("driverInfo", res.data);
          // 头像
          // this.headPortrait = [{
          //   url: res.data.headPortrait
          // }];
          // JSON.parse(JSON.stringify(this.titleDetail));
          this.driverInfo = JSON.parse(JSON.stringify(res.data));
          this.driverInfo.name = nameDesensitization(res.data.name);
          this.driverInfo.idNo = idCardDesensitization(this.driverInfo.idNo);
          this.driverInfo.phone = phoneDesensitization(this.driverInfo.phone);
          this.moneyType = this.driverInfo ?.moneyType;
        }).catch((err) => {
          console.log(err);
        });
      }   
    },
  };
</script>

<style scoped lang="scss">
  .my-home {
    width: 100%;
    // min-height: 100vh;
    background-color: rgba(239, 239, 239, 100%) !important;
    position: relative;

    .nav-bar {
      position: relative;
      height: 65.66vw;
      // 背景图片
      background-image: url("../../assets/images/common/my_bg.png");
      background-image: url("../../assets/images/winter/my_bg.png");
      background-size: cover;

      .header {
        // padding: 20px 10px 0;
        text-align: left;
        // border-bottom-left-radius: 5vw;
        // border-bottom-right-radius: 5vw;
        font-size: 16px;
        color: #fff;
        // background: #ff9200;
        position: absolute;
        top: 23vw;
        right: 10vw;

        // line-height: 32px;
        // overflow: hidden;
        // margin: 30px 20px auto;
        button {
          vertical-align: super;
          background: transparent;
          border-color: transparent;

          span {
            position: relative;
            top: 0;
            left: 0;
          }
        }
      }

      .title {
        display: flex;
        position: absolute;
        top: 9.667vw;
        left: 30px;
        font-size: 38px;
        color: white;
      }

      .setting {
        display: flex;
        position: absolute;
        top: 9.9vw;
        left: 90vw;
      }

      .head-portrait {
        width: 126px;
        height: 126px;
        position: absolute;
        top: 19vw;
        left: 3vw;
        border-radius: 50%;
        background-color: white;
        background-image: url("../../assets/images/common/touxiang.png");
        background-size: 49px 75px;
        background-repeat: no-repeat;
        background-position: 50% 50%;
      }

      .pic {
        position: absolute;
        top: 19vw;
        left: 3vw;

        /deep/ .van-uploader {
          /deep/ .van-image {
            // width: 60px !important;
            // height: 60px !important;
            // display: flex;
            // justify-content: center;
            // align-items: center;
            // background: white;
            // border-radius: 50%;
            .van-image__img {
              width: 60px !important;
              height: 60px !important;
              border-radius: 50%;
            }
          }

          .head-img {
            width: 60px;
            height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: white;
            border-radius: 50%;

            img {
              height: 36.5px;
              width: 24px;
            }
          }
        }
      }

      .details {
        position: absolute;
        top: 18vw;
        left: 23vw;

        p {
          color: white;
          font-size: 28px;
          text-align: left;
        }
      }

      /deep/ .van-tag {
        position: absolute;
        top: 25vw;
        left: 66vw;
      }

      span {
        position: absolute;
        top: 25.5vw;
        left: 92vw;
      }
    }

    .card-item {
      width: 93.34%;
      position: absolute;
      top: 39vw;
      left: 26.413px;
      border-radius: 20px;

      /deep/ .van-cell-group {
        background-color: rgba(239, 239, 239, 100%) !important;

        .van-cell {
          .van-cell__title {
            text-align: left;
          }
        }
      }
    }

    .my_cont {
      // 控制中间滚动
      overflow-y: scroll;
      max-height: 92vw;
      width: 700px;
      position: absolute;
      top: 81vw;
      left: 26.413px;
      background: #ffffff;
      margin: 50px auto 0;
      @include useScrollByBrowser(0);

      .flex_top {
        padding-top: 20px;

        .my_card_left {
          width: 0px;
        }

        .my_card_center {
          div {
            /deep/ .van-image {
              vertical-align: bottom;
            }

            margin: 10px auto;
          }
        }
      }

      .my-links {
        text-align: left;
        border-radius: 10px;
        margin: 0 auto; 
        padding: 10px 0;

        /deep/ .van-cell {
          padding-left: 21px;
          padding-right: 20px;
          padding-top: 10px;
          padding-bottom: 10px;
          height: 40px;

          .van-cell__title {
            display: flex;
            align-items: center;

            .icon {
              height: 16px;
              margin-right: 12px;
            }

            .van-icon {
              // height: 26px;
              margin-right: 12px;
            }
          }
        }
      }
    }

    .version {
      color: #587cff;
      text-align: center;
      margin-top: 20px;
      padding: 3vw 0;
    }
  }
</style>