<template>
  <div class="recharge_result">
    <nav-bar title="充值成功" />
    <div class="result">
      <div class="top">恭喜您，充值成功</div>
      <van-cell-group class="middle" :border="false">
        <van-cell title="充值卡号" :value="rechargedata.cardNo" />
        <van-cell title="支付方式" :value="rechargedata.payModetxt" />
        <van-cell title="支付时间" :value="rechargedata.payTime" />
      </van-cell-group>
      <div class="bottom">
        <span>支付金额</span>
        <span v-if="rechargedata">{{rechargedata.amount}}</span>
      </div>
    </div>
  </div>
</template>
<script>
import navBar from "@/components/navBar/NavHeader";
export default {
  components: { navBar },
  data() {
    return {
      rechargedata: {},
    }
  },
  created() {
    this.Init();
  },
  methods: {
    Init() {
      this.rechargedata = this.$route.query.recharge;
      this.rechargedata.payModetxt = this.rechargedata.payMode == 13 ? '支付宝': ''
    },
  }
}
</script>
<style lang="scss" scoped>
.recharge_result {
  .result {
    width: 92vw;
    margin: 4vw auto 0;
    .top {
      height: 12vw;
      padding-top: 6vw;
      background: url("~@/assets/images/PayInfo/recharge_bgtop.png") center center no-repeat;
      background-size: cover;
      color: #ff9200;
      font-size: 1.2rem;
    }
    .middle {
      padding: 16vw 0;
      .van-cell__title {
        text-align: left;
      }
    }
    .bottom {
      height: 15vw;
      color: #fff;
      background: url("~@/assets/images/PayInfo/recharge_bgbottom.png") center center no-repeat;
      background-size: cover;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 16px;
    }
  }
}
</style>