<template>
  <div class="certification-person">
    <nav-bar title="人脸活体认证结果" />
    <div v-if="!result" style="margin: 40px 0 20px;">
      <span>采集成功，结果查询中...</span>
    </div>
    <div v-else>
      <div class="result" style="margin: 40px 0 20px;">
        <div v-if="isSuccess">
          <van-icon name="passed" size="160px" color="#07c160" />
          <p>实人认证成功</p>
        </div>
        <div v-else>
          <van-icon name="close" size="160px" color="#ee0a24" />
          <p>实人认证失败</p>
        </div>
      </div>
      <div class="btn-wrap">
        <van-button v-if="isSuccess" block round :color="$setting.themeColor" @click="getActivatedList">下一步</van-button>
        <van-button v-else block round :color="$setting.themeColor" @click="goBack">重新认证</van-button>
      </div>
    </div>
  </div>
</template>

<script>
import { storage } from "Utils/common";
import navBar from "@/components/navBar/NavHeader";
import { recordJsLog } from "@/utils/recordJsLog";

export default {
  components: {
    navBar,
  },
  data() {
    return {
      result: false,
      isSuccess: false,
    };
  },
  mounted() {
    this.describeFaceVerify();
    // 设置默认卡，重要流程，若默认卡设置失败，APP核心功能卡操作相关将无法使用
    window.onSetDefaultCard = this.onSetDefaultCard;
  },
  methods: {
    /**
     * @description: sdk设置默认卡回调
     * @param {*} params
     * @return {*}
     */

    onSetDefaultCard(params) {
      recordJsLog('onSetDefaultCard', params);

      this.$router.replace({ path: "/gasstationmap" });
      // if (params.error === 0) {
      //   this.$router.replace({path: "/gasstationmap",});
      // } else {
      //   this.$Dialog.alert({
      //     title: "设置默认卡失败",
      //     message: params.error + '-' + params.message,
      //   }).then(() => {});
      // }
    },

    /**
     * @description: 人脸活体认证结果查询
     * @return {*}
     */

    describeFaceVerify() {
      const params = {
        certifyId: storage.ss.get("certifyId"),
        verifyUnique: storage.ss.get("verifyUnique1"),
        verifyMode: 1,
        channel: 912,
      };
      this.result = false;
      this.$http("POST", `/card/appUserOcr/v1/describeFaceVerify`, params, true, true, true)
        .then((res) => {
          if (res.infoCode == 1) {
            this.isSuccess = true;
            storage.ss.set("authToken", res.data.verifyUnique);
          } else {
            this.isSuccess = false;
          }
          this.result = true;
        })
        .catch((e) => {
          this.isSuccess = false;
          this.result = true;
        });
    },

    /**
     * @description: 返回上一步，重新认证
     * @return {*}
     */

    goBack() {
      this.$router.go(-1);
    },

    /**
     * @description: 司机验证完成后，根据不同情况进行不同的操作
     * @return {*}
     */

    getActivatedList() {
      this.$http(
        "POST",
        "/user/v1/getActivatedList",
        {
          appid: 2,
          apptoken: storage.ss.get("apptoken"),
          phone: storage.ss.get("phone"),
          curPage: 1,
          pageSize: 99,
          type: 2, // 1企业端，2司机端
        },
        true,
        true,
        true
      ).then((res) => {
        if (res.infoCode == 1) {
          if (res.data.length > 0) {
            // isActive 1已激活   2已开卡，未激活
            let setPayCompanylist = res.data.filter((item) => {
              return item.isActive == 1;
            });
            let unsetPayCompanylist = res.data.filter((item) => {
              return item.isActive == 2;
            });

            // 注册未激活跳转到激活页面设置默认卡
            if (setPayCompanylist.length == 0 && unsetPayCompanylist.length > 0) {
              this.$router.replace({
                path: "/chooseEnterprise",
                query: {
                  isActive: 2,
                },
              });
            }

            if (setPayCompanylist.length > 0) {
              // 更新用户信息
              this.$http(
                "POST",
                "/user/v1/updateUserInfo",
                {
                  apptoken: storage.ss.get("apptoken"),
                  appid: 2,
                  idCard: storage.ss.get("idCard"),
                  authToken: storage.ss.get("authToken"),
                  phone: storage.ss.get("phone"),
                  username: storage.ss.get("name"),
                  companyCodeList: setPayCompanylist[0].companyCode,
                  type: 2,
                },
                true,
                true,
                true
              ).then((upInfo) => {
                if (upInfo && upInfo.infoCode == 1) {
                  this.$http(
                    "POST",
                    "/user/v1/updateDefaultCompanyCode",
                    {
                      appid: 2,
                      apptoken: storage.ss.get("apptoken"),
                      companyCode: setPayCompanylist[0].companyCode,
                      phone: storage.ss.get("phone"),
                      type: 2,
                    },
                    true,
                    true,
                    true
                  ).then((defcode) => {
                    if (defcode && defcode.infoCode == 1) {
                      storage.ss.set("defaultCompanyCode", defcode.data.defaultCompanyCode);
                      storage.ss.set("apptoken", defcode.data.apptoken);
                      storage.ss.set("userId", defcode.data.id);
                      storage.ss.set("cardNo", defcode.data.cardNo);
                      recordJsLog('setDefaultCard', {
                        pan: defcode.data.cardNo,
                      });

                      this.$cppeiBridge("setDefaultCard", {
                        pan: defcode.data.cardNo,
                      });
                      // this.$cppeiBridge("saveInfo", {
                      //   token: defcode.data.apptoken,
                      //   userId: defcode.data.id,
                      // });
                    }
                  });
                }
              });
            }
          } else {
            this.$router.push({
              path: "/chooseEnterprise",
              query: {
                isActive: 1,
              },
            });
          }
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.certification-person {
  .btn-wrap {
    padding: 20px 20px;
  }
}
</style>
