<template>
  <div class="oilList">
    <nav-header title="油站列表" />
    <template v-if="list.length > 0">
      <div class="list" v-for="(item, index) in list" :key="index">
        <!-- 冬奥会油站显示字段 ifShow 0不显示，1显示  v-show="item.ifShow == 1" -->
        <div class="list_li" @click="goback(item)">
          <logo />
          <div class="list_center">
            <div class="oil_name">
              <div class="name">
                {{ item.stationName }}({{ item.status | oilStatus }})
              </div>
            </div>
            <div class="oil_distance">距离你{{ item.distance }}公里</div>
          </div>
          <div class="list_go_detil" v-if="details">
            <van-tag plain type="warning" size="large">加油</van-tag>
          </div>
        </div>
        <!-- <div class="list_li">
        <logo />
        <div class="list_center">
          <div class="oil_name">
            <div class="name">加油站名字(营业中)</div>
          </div>
          <div class="oil_distance">距离你40米</div>
        </div>
        <div class="list_go_detil" v-if="details">
          <van-tag plain type="warning">加油</van-tag>
        </div>
      </div> -->
      </div>
    </template>
    <no-service-station v-else></no-service-station>
  </div>
</template>

<script>
import NavHeader from "@/components/navBar/NavHeader.vue";
import { mapState, mapMutations } from "vuex";
import eventBus from "./_service/eventbus.js";
import noServiceStation from "./component/noServiceStation";
export default {
  name: "",
  components: {
    NavHeader,
    noServiceStation,
  },
  props: {
    yyz: {
      type: Boolean,
      default: true,
    },
    details: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      list: "",
    };
  },
  watch: {},
  computed: {},
  methods: {
    ...mapMutations(["rightSlide_Fn"]),
    goback(item) {
      // console.log("gas_list:" + JSON.stringify(item));
      this.rightSlide_Fn(true);
      this.$router.replace({
        path: "/noBelowRefuel",
        query: {
          isStoplocation: 1,
          tabOil: true,
          navState: true,
          OilStationinfo: item,
        },
      });
      // console.log("currentOilNo73");
      // eventBus.$emit("currentOilNo", item);
      //调用router回退页面
      // this.$router.go(-1);
    },
  },
  created() {},
  mounted() {
    this.list = JSON.parse(this.$route.query.list);
  },
};
</script>
<style lang="scss" scoped>
.oilList {
  background-color: #ffffff !important;
  // overflow: scroll !important;
  // overflow: scroll;
  box-sizing: border-box;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none;
  }
  -webkit-overflow-scrolling: touch;
  .list {
    margin: 40px auto;
    border-bottom: 1px solid #bfddef;
    width: 90%;
    .list_li {
      width: 100%;
      margin: 30px auto;
      display: flex;
      justify-content: space-between;
      .list_center {
        margin-left: 20px;
        flex: 1;
        text-align: left;
        .oil_name {
          color: #333333;
          font-size: 32px;
          margin-bottom: 10px;
          display: flex;
          width: 60vw;
          justify-content: space-between;
          .name {
            width: 45vw;
          }
          .tag {
            width: 15vw;
          }
        }
        .oil_distance {
          padding-left: 30px;
          background: url("~@/assets/images/gasstationmap/dingwei.png") left
            center no-repeat;
          background-size: 25px 25px;
          color: #666666;
          font-size: 26px;
        }
        .icon_list {
          margin-top: 20px;
          img {
            display: inline-block;
            margin: 5px;
            width: 50px;
            height: 50px;
            &:nth-child(1) {
              margin-left: 0;
            }
          }
        }
      }
      .list_go_detil {
        align-self: center;
        color: #ff9200;
        font-size: 36px;
      }
    }
    .list_btn {
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
      width: 100%;
      button {
        width: 48%;
        border-radius: 20px;
      }
    }
  }
}
</style>
