/*
 * @Author: <PERSON>on
 * @Date: 2022-04-19 14:06:04
 * @LastEditors: ltw <EMAIL>
 * @LastEditTime: 2024-01-15 14:12:59
 * @Description: 整个app的前端路由定义,为了优化,全部按照动态函数import导入视图
 */
export default [
  {
    path: "/",
    redirect: "",
    meta: {
      index: 0,
      title: "登录",
      footNav: false,
    },
  },
  {
    path: "/authority",
    name: "authority",
    component: () => import("Views/UserModules/Authority"),
    meta: {
      index: 1,
      title: "隐私权限设置",
      footNav: false,
    },
  },
  {
    path: "/choosegun",
    name: "choosegun",
    component: () => import("Views/ChooseGun/"),
    meta: {
      index: 1,
      title: "选择油枪",
    },
  },
  {
    path: "/stationList",
    name: "stationList",
    component: () => import("Views/StationList/"),
    meta: {
      index: 2,
      title: "油站列表",
    },
  },
  {
    path: "/gasstationmap",
    name: "gasstationmap",
    component: () => import("Views/GasStationMap/"),
    meta: {
      index: 4,
      title: "预授权加油",
    },
  },
  {
    path: "/loginCenter",
    name: "loginCenter",
    component: () => import("Views/UserModules/LoginCenter"),
    meta: {
      index: 6,
      title: "手机验证码登录",
      footNav: false,
    },
  },
  {
    path: "/forgetPswd",
    name: "forgetPswd",
    component: () => import("Views/UserModules/ForgetPwsd"),
    meta: {
      index: 7,
      title: "忘记密码",
      footNav: false,
    },
  },
  {
    path: "/setPswd",
    name: "setPswd",
    component: () => import("Views/UserModules/SetPswd"),
    meta: {
      index: 8,
      title: "设置密码",
      footNav: false,
    },
  },
  {
    path: "/paymentCode",
    name: "paymentCode",
    component: () => import("Views/PayModules"),
    meta: {
      index: 9,
      title: "付款码",
    },
  },
  {
    path: "/gasDetails",
    name: "gasDetails",
    component: () => import("Views/GasStationMap/gas_details"),
    meta: {
      index: 12,
      title: "加油详情",
      footNav: false,
    },
  },
  {
    path: "/chooseCard",
    name: "chooseCard",
    component: () => import("Views/ChooseType/chooseCard"),
    meta: {
      index: 14,
      title: "选择司机卡",
    },
  },
  {
    path: "/chooseEnterprise",
    name: "chooseEnterprise",
    component: () => import("Views/ChooseType/chooseEnterprise"),
    meta: {
      index: 15,
      title: "选择司企业",
      footNav: false,
    },
  },
  {
    path: "/activeCards",
    name: "activeCards",
    component: () => import("Views/ChooseType/activeCards"),
    meta: {
      index: 16,
      title: "已开通,只激活",
    },
  },
  {
    path: "/gaslist",
    name: "gaslist",
    component: () => import("Views/GasStationMap/gas_list"),
    meta: {
      index: 17,
      title: "选择司企业",
      footNav: false,
    },
  },
  {
    path: "/gasNav",
    name: "gasNav",
    component: () => import("Views/GasStationMap/gasNav"),
    meta: {
      index: 18,
      title: "选择司企业",
    },
  },
  {
    path: "/my",
    name: "my",
    component: () => import("Views/My"),
    meta: {
      index: 19,
      title: "我的",
    },
  },
  {
    path: "/my/ecard",
    name: "myEcard",
    component: () => import("Views/MyEcard"),
    meta: {
      index: 20,
      title: "我的加油卡",
      footNav: false,
    },
  },
  {
    path: "/my/ecard/recharge",
    name: "recharge",
    component: () => import("Views/MyEcard/Recharge"),
    meta: {
      index: 21,
      title: "充值",
      footNav: false,
    },
  },
  {
    path: "/my/ecard/recharge/rechargeResult",
    name: "rechargeResult",
    component: () => import("Views/MyEcard/rechargeResult"),
    meta: {
      index: 21,
      title: "充值成功",
      footNav: false,
    },
  },
  {
    path: "/my/ecard/rechargeRecord",
    name: "rechargeRecord",
    component: () => import("Views/MyEcard/RechargeRecord"),
    meta: {
      index: 22,
      title: "本卡充值记录",
      footNav: false,
    },
  },
  {
    path: "/my/ecard/consumingRecords",
    name: "consumingRecords",
    component: () => import("Views/MyEcard/ConsumingRecords"),
    meta: {
      index: 23,
      title: "本卡消费记录",
      footNav: false,
    },
  },
  {
    path: "/my/ecard/discountContract",
    name: "discountContract",
    component: () => import("Views/MyEcard/DiscountContract"),
    meta: {
      index: 24,
      title: "查询优惠合同",
      footNav: false,
    },
  },
  {
    path: "/my/allConsumingRecords",
    name: "allConsumingRecords",
    component: () => import("Views/My/AllConsumingRecords"),
    meta: {
      index: 25,
      title: "消费记录",
      footNav: false,
    },
  },
  {
    path: "/my/openNewCard",
    name: "openNewCard",
    component: () => import("Views/My/OpenNewCard"),
    meta: {
      index: 26,
      title: "开通新的司机卡",
      footNav: false,
    },
  },
  {
    path: "/my/closeAccount",
    name: "closeAccount",
    component: () => import("Views/My/CloseAccount"),
    meta: {
      index: 27,
      title: "销户",
      footNav: false,
    },
  },
  {
    path: "/my/security",
    name: "security",
    component: () => import("Views/My/Security"),
    meta: {
      index: 28,
      title: "账户安全",
      footNav: false,
    },
  },
  {
    path: "/my/deviceManagement",
    name: "deviceManagement",
    component: () => import("Views/My/DeviceManagement"),
    meta: {
      index: 29,
      title: "设备管理",
      footNav: false,
    },
  },
  {
    path: "/my/resetPswd",
    name: "resetPswd",
    component: () => import("Views/My/ResetPswd"),
    meta: {
      index: 31,
      title: "修改账户密码",
      footNav: false,
    },
  },
  {
    path: "/my/resetPayPswd",
    name: "resetPayPswd",
    component: () => import("Views/My/ResetPayPswd"),
    meta: {
      index: 32,
      title: "重置支付密码",
      footNav: false,
    },
  },
  {
    path: "/faceRecognition",
    name: "faceRecognition",
    component: () => import("Views/FaceRecognition/FaceRecognition"),
    meta: {
      index: 33,
      title: "人脸识别",
      footNav: false,
    },
  },
  {
    path: "/personResult",
    name: "personResult",
    component: () => import("Views/FaceRecognition/personResult"),
    meta: {
      index: 33,
      title: "实人认证结果",
      footNav: false,
    },
  },
  {
    path: "/identityCardOCR",
    name: "identityCardOCR",
    component: () => import("Views/FaceRecognition/IdentityCardOCR"),
    meta: {
      index: 34,
      title: "ocr识别",
      footNav: false,
    },
  },
  {
    path: "/my/companyDetail",
    name: "companyDetail",
    component: () => import("Views/My/CompanyDetail"),
    meta: {
      index: 35,
      title: "我的-企业详情",
      footNav: false,
    },
  },
  {
    path: "/noAuth",
    name: "noAuth",
    component: () => import("Views/UserModules/NoAuth"),
    meta: {
      index: 36,
      title: "未授权提示页",
      footNav: false,
    },
  },
  {
    path: "/setPayPswd",
    name: "setPayPswd",
    component: () => import("Views/UserModules/SetPayPswd"),
    meta: {
      index: 37,
      title: "设置支付密码",
      footNav: false,
    },
  },
  {
    path: "/my/setting",
    name: "setting",
    component: () => import("Views/My/Setting"),
    meta: {
      index: 38,
      title: "设置",
      footNav: false,
    },
  },

  /**** 用户协议相关  ****/
  {
    path: "/userAgreement",
    name: "userAgreement",
    component: () => import("Views/Agreement/UserAgreement"),
    meta: {
      index: 40,
      title: "用户协议",
      footNav: false,
    },
  },
  {
    path: "/privacyPolicy",
    name: "privacyPolicy",
    component: () => import("Views/Agreement/PrivacyPolicy"),
    meta: {
      index: 41,
      title: "隐私政策",
      footNav: false,
    },
  },
  {
    path: "/personAuthAgreement",
    name: "personAuthAgreement",
    component: () => import("Views/Agreement/PersonAuthAgreement"),
    meta: {
      index: 42,
      title: "认证协议",
      footNav: false,
    },
  },
  {
    path: "/safeNotice",
    name: "safeNotice",
    component: () => import("Views/UserModules/SecurityNotice"),
    meta: {
      index: 43,
      title: "安全验证",
      footNav: false,
    },
  },
  {
    path: "/safeChecking",
    name: "safeChecking",
    component: () => import("Views/UserModules/SecurityVerification"),
    meta: {
      index: 44,
      title: "安全验证",
      footNav: false,
    },
  },
  {
    path: "/rechargeAgreement",
    name: "rechargeAgreement",
    component: () => import("Views/Agreement/RechargeAgreement"),
    meta: {
      index: 45,
      title: "充值协议",
      footNav: false,
    },
  },
  {
    path: "/my/rechargeRecord",
    name: "rechargeRecord",
    component: () => import("Views/My/rechargeRecord"),
    meta: {
      index: 46,
      title: "充值记录",
      footNav: false,
    },
  },
  {
    path: "/my/applyMoney",
    name: "applyMoney",
    component: () => import("Views/My/ApplyMoney"),
    meta: {
      index: 47,
      title: "资金申请",
      footNav: false,
    },
  },
  {
    path: "/chooseOrder",
    name: "chooseOrder",
    component: () => import("Views/chooseOrder/chooseOrder"),
    meta: {
      index: 48,
      title: "订单选择",
      footNav: false,
    },
  },
  {
    path: "/chooseOrder/result",
    name: "orderPayResult",
    component: () => import("Views/chooseOrder/orderPayResult"),
    meta: {
      index: 49,
      title: "不下车加油",
      footNav: false,
    },
  },
  {
    path: "/preOrderPayResult",
    name: "preOrderPayResult",
    component: () => import("Views/GasStationMap/preOrderPayResult"),
    meta: {
      index: 50,
      title: "预授权核销",
      footNav: false,
    },
  },

  {
    path: "/preOrderPayResult",
    name: "preOrderPayResult",
    component: () => import("Views/GasStationMap/preOrderPayResult"),
    meta: {
      index: 50,
      title: "预授权核销",
      footNav: false,
    },
  },
  {
    path: "/paySucs",
    name: "paySucs",
    component: () => import("Views/PayModules/paySucs"),
    meta: {
      index: 51,
      title: "室内支付",
      footNav: false,
    },
  },
  {
    path: "/my/AllConsumingDetail",
    name: "AllConsumingDetail",
    component: () => import("Views/My/AllConsumingDetail"),
    meta: {
      index: 52,
      title: "消费明细",
      footNav: false,
    },
  },
  {
    path: "/my/AllcommodityDetail",
    name: "AllcommodityDetail",
    component: () => import("Views/My/AllcommodityDetail"),
    meta: {
      index: 53,
      title: "商品明细",
      footNav: false,
    },
  },
  {
    path: "/test",
    name: "Test",
    component: () => import("Views/Test/index"),
    meta: {
      index: 54,
      title: "测试",
      footNav: false,
    },
  },
  {
    path: "/noBelowRefuel",
    name: "noBelowRefuel",
    component: () => import("Views/NoBelowRefuel"),
    meta: {
      index: 55,
      title: "不下车加油展示",
    },
  },
  {
    path: "/nobelowlist",
    name: "nobelowlist",
    component: () => import("Views/NoBelowRefuel/no_below_list"),
    meta: {
      index: 55,
      title: "选择司企业",
      footNav: false,
    },
  },
];
