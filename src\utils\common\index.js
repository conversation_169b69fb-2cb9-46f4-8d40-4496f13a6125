/**
 *  公共处理工具集合
 */
import md5 from "js-md5";
import moment from "moment";

let bigDecimal = require("js-big-decimal");

/**
 * @description: 浏览器伪造 useragent,确保浏览器能打开
 * @return {*}
 */
const browserUserAgent = `Mozilla/5.0 (Linux; Android 12; M2102K1AC Build/SKQ1.211006.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/95.0.4638.74 Mobile Safari/537.36$ua={"statusHeight":39,"cpsStatus":1,"reapplyStatus":0,"agreePrivacy":true,"name":"<PERSON><PERSON>","bottomHeight":0,"isVirBar":false,"type":"Android","deviceId":"f359d59f3c6e0e8f","version":"1.0.7","token":""}`;
const isBrowser = () => {
  let ua = navigator.userAgent;
  // 如果默认打开是浏览器
  if (ua.indexOf("$ua=") == -1) {
    return true;
  } else {
    return false;
  }
};

/**
 * @desc 函数防抖
 * @param func 函数
 * @param wait 延迟执行毫秒数
 * @param immediate true 表立即执行，false 表非立即执行
 */
const debounce = function(func, wait, immediate = true) {
  let timeout;
  return function() {
    if (timeout) clearTimeout(timeout);
    if (immediate) {
      var callNow = !timeout;
      timeout = setTimeout(() => {
        timeout = null;
      }, wait);
      if (callNow) func.apply(this, arguments);
    } else {
      timeout = setTimeout(function() {
        func.apply(context, args);
      }, wait);
    }
  };
};

/**
 * @object 操作localStorage,sessionStorage,cookie封装
 * @method  ls 操作localStorage
 * @method  ss 操作sessionStorage
 * @method  ck 操作cookie
 */
const ls = localStorage;
const ss = sessionStorage;
const storage = {
  ls: {
    get(key) {
      try {
        return JSON.parse(ls.getItem(key));
      } catch (err) {
        return ls.getItem(key);
      }
    },
    set(key, value) {
      ls.setItem(key, JSON.stringify(value));
    },
    remove(key) {
      ls.removeItem(key);
    },
    clear() {
      ls.clear();
    },
  },
  ss: {
    get(key) {
      try {
        return JSON.parse(ss.getItem(key));
      } catch (err) {
        return ss.getItem(key);
      }
    },
    set(key, value) {
      ss.setItem(key, JSON.stringify(value));
    },
    remove(key) {
      ss.removeItem(key);
    },
    clear() {
      ss.clear();
    },
  },
  ck: {
    set(cname, value, expire) {
      var date = new Date();
      date.setSeconds(date.getSeconds() + expire);
      document.cookie = cname + "=" + escape(value) + "; expires=" + date.toGMTString();
    },
    get(cname) {
      if (document.cookie.length > 0) {
        let cstart = document.cookie.indexOf(cname + "=");
        if (cstart !== -1) {
          cstart = cstart + cname.length + 1;
          let cend = document.cookie.indexOf(";", cstart);
          if (cend === -1) {
            cend = document.cookie.length;
          }
          return unescape(document.cookie.substring(cstart, cend));
        }
      }
      return "";
    },
    del(cname) {
      storage.ck.set(cname, "", -1);
    },
  },
};

/**
 * @func 手动处理px转为vw,目前先写死默认值，此处应该可postcss中的options设置一致,后面再优化
 * @method  px 需要转换的px数值
 * @method  viewportSize 基于多少视口宽度 默认375
 * @method  unitPrecision 保留小数 默认3位
 * @method  viewportUnit 转换的视口单位值 默认vw
 * @method  minPixelValue 小于多少不转换了 默认1
 */
const px2vw = function(px, viewportSize = 375, unitPrecision = 3, viewportUnit = "vw", minPixelValue = 1) {
  let pixels = parseFloat(px);
  if (pixels <= minPixelValue) return;
  return toFixed((pixels / viewportSize) * 100, unitPrecision) + viewportUnit;
};
function toFixed(number, precision) {
  let multiplier = Math.pow(10, precision + 1);
  let wholeNumber = Math.floor(number * multiplier);
  return (Math.round(wholeNumber / 10) * 10) / multiplier;
}

/**
 * @func 构造UUID
 */
const generateUUID = function() {
  var d = new Date().getTime();
  if (window.performance && typeof window.performance.now === "function") {
    d += performance.now(); //use high-precision timer if available
  }
  var uuid = "xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g, function(c) {
    var r = (d + Math.random() * 32) % 32 | 0;
    d = Math.floor(d / 32);
    return (c === "x" ? r : (r & 0x3) | 0x8).toString(32);
  });
  return uuid;
};
const checkIDCard = function(idStr) {
  let pattern = /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
  return pattern.test(idStr);
};
const OilTypes = [
  {
    values: ["10#柴油", "5#柴油", "0#柴油", "-5#柴油", "-10#柴油", "-20#柴油", "-30#柴油", "-35#柴油", "-50#柴油", "89#汽油", "90#汽油", "92#汽油", "93#汽油", "95#汽油", "97#汽油", "98#汽油"],
    defaultIndex: 0,
  },
];

const isAndroid = function() {
  // const ua = navigator.userAgent;
  let ua = "";
  ua = isBrowser() ? browserUserAgent : navigator.userAgent;
  let userAgent = ua.substring(ua.indexOf("$ua=") + 4, ua.length);
  const isAndroid = JSON.parse(userAgent)?.type == "Android" ? true : false;
  return isAndroid;
};

const AllowAmount = [
  {
    values: ["200", "400", "600", "800", "1000", "1200", "1400", "1600", "1800", "2000"],
    defaultIndex: 0,
  },
];

const CreditTypesColumn = [
  {
    values: ["黑色", "白色", "红色", "灰色", "银色", "蓝色", "黄色", "棕色", "绿色", "橙色", "紫色", "金色", "粉红", "香槟", "其它"],
    defaultIndex: 0,
  },
];

const carNumberColorColumn = [
  {
    values: ["蓝色", "黄色", "黑色", "白色", "渐变绿色", "黄绿双拼色", "蓝白渐变色", "绿色", "红色", "未确定"],
    defaultIndex: 0,
  },
];

const carNoColor = [
  { text: "蓝色", value: "0" },
  { text: "黄色", value: "1" },
  { text: "黑色", value: "2" },
  { text: "白色", value: "3" },
  { text: "渐变绿色", value: "4" },
  { text: "黄绿双拼色", value: "5" },
  { text: "蓝白渐变色", value: "6" },
  { text: "绿色", value: "11" },
  { text: "红色", value: "12" },
  { text: "未确定", value: "9" },
];

const getCarNoColorTextOrValue = (v, type = 0) => {
  let filed = type === 0 ? "text" : "value";
  let backV = type === 0 ? "value" : "text";
  return (!!carNoColor.filter((el) => el[filed] === v) && carNoColor.filter((el) => el[filed] === v).length !== 0 && carNoColor.filter((el) => el[filed] === v)[0][backV]) || 0;
};

const GetQueryString = (name) => {
  let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  let r = window.location.search.substr(1).match(reg);
  let context = "";
  if (r != null) context = r[2];
  reg = null;
  r = null;
  return context == null || context == "" || context == "undefined" ? "" : context;
};

/**
 * @func js浮点数的加减乘除
 */
//加
const floatAdd = function(arg1, arg2) {
  let result = bigDecimal.add(arg1, arg2);
  if (result.indexOf(".") === -1) {
    result = result + ".00";
  } else {
    if (result.split(".")[1].length === 1) {
      result = result + "0";
    }
  }
  return result;
  // var r1, r2, m;
  // try {
  //   r1 = arg1.toString().split(".")[1].length;
  // } catch (e) {
  //   r1 = 0;
  // }
  // try {
  //   r2 = arg2.toString().split(".")[1].length;
  // } catch (e) {
  //   r2 = 0;
  // }
  // m = Math.pow(10, Math.max(r1, r2));
  // return (arg1 * m + arg2 * m) / m;
};
//减
const floatSub = function(arg1, arg2) {
  // return parseFloat(bigDecimal.subtract(arg1, arg2, 2));

  let result = bigDecimal.subtract(arg1, arg2);
  if (result.indexOf(".") === -1) {
    result = result + ".00";
  } else {
    if (result.split(".")[1].length === 1) {
      result = result + "0";
    }
  }
  return result;
  // var r1, r2, m, n;
  // try {
  //   r1 = arg1.toString().split(".")[1].length;
  // } catch (e) {
  //   r1 = 0;
  // }
  // try {
  //   r2 = arg2.toString().split(".")[1].length;
  // } catch (e) {
  //   r2 = 0;
  // }
  // m = Math.pow(10, Math.max(r1, r2));
  // //动态控制精度长度
  // n = r1 >= r2 ? r1 : r2;
  // return ((arg1 * m - arg2 * m) / m).toFixed(n);
};

//乘
const floatMul = function(arg1, arg2) {
  let result = bigDecimal.multiply(arg1, arg2);
  if (result.indexOf(".") === -1) {
    result = result + ".00";
  } else {
    if (result.split(".")[1].length === 1) {
      result = result + "0";
    }
  }
  return result;
  // var m = 0,
  //   s1 = arg1.toString(),
  //   s2 = arg2.toString();
  // try {
  //   m += s1.split(".")[1].length;
  // } catch (e) {}
  // try {
  //   m += s2.split(".")[1].length;
  // } catch (e) {}
  // return (
  //   (Number(s1.replace(".", "")) * Number(s2.replace(".", ""))) /
  //   Math.pow(10, m)
  // );
};
//除
const floatDiv = function(arg1, arg2, decimal) {
  if (decimal == 0) {
    //传0不保留
    return parseFloat(bigDecimal.divide(arg1, arg2));
  } else if (decimal == null) {
    //默认保留2位数
    return parseFloat(bigDecimal.divide(arg1, arg2, 2));
  }
  return parseFloat(bigDecimal.divide(arg1, arg2, decimal));

  // let result = bigDecimal.divide(arg1, arg2);
  // if (result.indexOf(".") === -1) {
  //   result = result + ".00";
  // } else {
  //   if (result.split(".")[1].length === 1) {
  //     result = result + "0";
  //   }
  // }
  // return result;
  // var t1 = 0,
  //   t2 = 0,
  //   r1,
  //   r2;
  // try {
  //   t1 = arg1.toString().split(".")[1].length;
  // } catch (e) {}
  // try {
  //   t2 = arg2.toString().split(".")[1].length;
  // } catch (e) {}

  // r1 = Number(arg1.toString().replace(".", ""));

  // r2 = Number(arg2.toString().replace(".", ""));
  // return (r1 / r2) * Math.pow(10, t2 - t1);
};

const getCurrentTime = (time = +new Date()) => {
  let date = new Date(time + 8 * 3600 * 1000); // 增加8小时
  return date
    .toJSON()
    .substr(0, 19)
    .replace("T", " ");
};

const dateFormat = (longTypeDate) => {
  return moment(parseInt(longTypeDate)).format("yyyy-MM-DD HH:mm:ss");
};

const todayFormat = () => {
  return moment(new Date()).format("YYYY-MM-DD 23:59:59");
};

const lastMonthFormt = () => {
  return moment(new Date())
    .subtract(1, "months")
    .format("YYYY-MM-DD 00:00:00");
};

const getUserAgent = () => {
  // let ua = navigator.userAgent;
  let ua = "";
  ua = isBrowser() ? browserUserAgent : navigator.userAgent;
  let userAgent = ua.substring(ua.indexOf("$ua=") + 4, ua.length);
  return JSON.parse(userAgent);
};
/**
 * @description: 姓名脱敏展示
 * @param {*} type 传入的参数
 * @return {*}
 */
const nameDesensitization = (type) => {
  if (type.length == 2) {
    return type.substring(0, 1) + "*";
  } else if (type.length == 3) {
    return type.substring(0, 1) + "*" + type.substring(2, 3);
  } else if (type.length > 3) {
    return type.substring(0, 2) + "**";
  }
};
/**
 * @description: 手机号脱敏
 * @param {*} type 传入的参数
 * @return {*}
 */
const phoneDesensitization = (type) => {
  return type.substring(0, 3) + "****" + type.substring(7, 11);
};
/**
 * @description: 身份证脱敏
 * @param {*} type 传入的参数
 * @return {*}
 */
const idCardDesensitization = (type) => {
  return type.substring(0, 6) + "********" + type.substring(14, 18);
};

/**
 * @description: 生成签名
 * @return {*}
 */
const sign = (params, url) => {
  // console.log("====>  SIGN 验签接口: ", url);
  let mdStr = '';
  // TODO change
  mdStr = JSON.stringify(sortObject(params)) + '&zsy_key=e5d0eed1dc7522fc' //测试密钥
  // mdStr = JSON.stringify(sortObject(params)) + '&zsy_key=78a05a28c63549b3' //生产密钥
  // console.log("md5加密前字符串====>", mdStr);
  // console.log("md5加密后字符串====>", md5(mdStr).toUpperCase());
  // console.log("=========  end  =========")
  return md5(mdStr).toUpperCase();
};

// 将对象中的属性按升序排序
const sortObject = (obj) => {
  if (typeof obj !== "object" || obj === null || Array.isArray(obj) === true) {
    return obj;
  }
  const keys = Object.keys(obj);
  keys.sort();
  const sortedObj = {};
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    sortedObj[key] = obj[key];
  }
  return sortedObj;
}
// 精确运算 加
const add=(a, b)=> {
  var c, d, e;
  try {
    c = a.toString().split(".")[1].length;
  } catch (f) {
    c = 0;
  }
  try {
    d = b.toString().split(".")[1].length;
  } catch (f) {
    d = 0;
  }
  return (e = Math.pow(10, Math.max(c, d))), (mul(a, e) + mul(b, e)) / e;
}

// 精确运算 减
const sub=(a, b)=> {
  var c, d, e;
  try {
    c = a.toString().split(".")[1].length;
  } catch (f) {
    c = 0;
  }
  try {
    d = b.toString().split(".")[1].length;
  } catch (f) {
    d = 0;
  }
  return (e = Math.pow(10, Math.max(c, d))), (mul(a, e) - mul(b, e)) / e;
}

// 精确运算 乘
const mul=(a, b)=> {
  var c = 0,
    d = a.toString(),
    e = b.toString();
  try {
    c += d.split(".")[1].length;
  } catch (f) {}
  try {
    c += e.split(".")[1].length;
  } catch (f) {}
  return (
    (Number(d.replace(".", "")) * Number(e.replace(".", ""))) / Math.pow(10, c)
  );
}

// 精确运算 除
const div=(a, b)=> {
  var c,
    d,
    e = 0,
    f = 0;
  try {
    e = a.toString().split(".")[1].length;
  } catch (g) {}
  try {
    f = b.toString().split(".")[1].length;
  } catch (g) {}
  return (
    (c = Number(a.toString().replace(".", ""))),
    (d = Number(b.toString().replace(".", ""))),
    mul(c / d, Math.pow(10, f - e))
  );
}
/**获取定位失败，点击取消（功能受限）后，给定位赋值北京默认值，保证后续流程不受影响 */
const LOCATION_INFO = {
  province: "北京",
  city: "北京",
  district: "",
  lon: 116.407718,
  lat: 39.920248,
  error: "",
  hasLocationPermission: true
}
function compareVersions(v1, v2) {
  const version1 = v1.split('.');
  const version2 = v2.split('.');

  const maxLength = Math.max(version1.length, version2.length);
  for (let i = 0; i < maxLength; i++) {
      const num1 = parseInt(version1[i] || '0', 10);
      const num2 = parseInt(version2[i] || '0', 10);

      if (num1 > num2) return 1;
      if (num1 < num2) return -1;
  }
  return 0;
}

/**
 * 生成一个随机字符串
 * @param {number} [len=6] - 字符串长度，默认为6位
 * @returns {string} 随机生成的指定长度的字符串，包含0-9a-zA-Z
 */
function randomString(len = 6) {
  if (len <= 11) {
    return Math.random()
      .toString(36)
      .substring(2, 2 + len)
      .padEnd(len, '0');
  } else {
    return randomString(11) + randomString(len - 11);
  }
}

export {
  debounce,
  storage,
  px2vw,
  isBrowser,
  generateUUID,
  OilTypes,
  AllowAmount,
  CreditTypesColumn,
  carNumberColorColumn,
  getCarNoColorTextOrValue,
  isAndroid,
  checkIDCard,
  GetQueryString,
  floatAdd,
  floatSub,
  floatMul,
  floatDiv,
  getCurrentTime,
  dateFormat,
  todayFormat,
  lastMonthFormt,
  getUserAgent,
  nameDesensitization,
  phoneDesensitization,
  idCardDesensitization,
  sign,
  add,
  sub,
  mul,
  div,
  LOCATION_INFO,
  compareVersions,
  randomString
};
