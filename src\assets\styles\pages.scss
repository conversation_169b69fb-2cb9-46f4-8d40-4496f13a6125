$btnBgcolor: #ff9200;
$bg-color-white: #fff;
$bg-color-gray:#F5F5F5;
$bg-theme-gray: #eee;
$txt-color-white: #fff;
$txt-color-theme: #ff9200;
// 部分公用样式
// 页面一半按钮
.button-half{
  width: 44vw;
  border-radius: 4vw;
  background-color: $btnBgcolor;
  border: none;
}
// 两端对齐
.flex_space-between { 
  display: flex;
  justify-content: space-between;
}
// swipe 轮播图整体框架样式调整
.van-swipe {
  width: 94%;
  margin: 0 auto;
  height: 30vh;
  border-radius: 10px;
  .van-swipe__indicator {
    width: 4vw;
    border-radius: 1vw;
    background-color: rgba(244,244,244,.5);
    &.van-swipe__indicator--active {
      width: 6vw;
      background-color: rgb(244,244,244);
    }
  }
}
// 宫格
.van-grid-item__content {
  padding: 8px 0;
  border-radius: 5vw;
}
.van-hairline--bottom {
  &::after {
    border: none;
  }
}
// tab标签
.van-tabs__wrap {
  background: $backgoundColor;
  padding: 2vw 0 5vw;
  .van-tabs__nav--card {
    width: 80%;
    border: none;
    margin: 0 auto;
    border: 1px solid #DCDEE3;
    border-radius: 2vw;
    .van-tab {
      border: none;
      color: #b2b3b5;
      border-radius: 2vw;
      &.van-tab--active {
        background-color: $btnBgcolor;
        border: 1px solid $btnBgcolor;
        color: $txt-color-white;
      }
    }

  }
}
.van-button__content {
  .van-button__text {
    line-height: inherit;
  }
}
.van-tab__pane {
  background-color: $bg-color-gray;
}
.approval_box {
  background-color: $bg-color-white;
  border-radius: 2vh;
  padding: 2vh;
  margin-bottom: 2vh;
  text-align: left;
  header {
    line-height: 3vh;
  }
  section {
    background-color: $bg-color-gray;
    padding: 2vh;
    border-radius: 1vh;
    margin: 1.5vh auto;
    div {
      display: flex;
      justify-content: space-between;
      padding: .5vh 0;
    }
  }
  footer {
    display: flex;
    justify-content: space-around;
    span {
      line-height: 5vh;
    }
    i {
      font-style: normal;
      button {
        padding: 1vh 3vh;
        &.van-button--default {
          margin-right: 1vh;
          border: none;
        }
        &.van-button--primary {
          background-color: $btnBgcolor;
          border: 1px solid $btnBgcolor;
        }
      }
    }
  }
  &.destory {
    color: #FF6666;
    section {
      background: rgba(255,102,102,.2);
    }
    button {
      &.van-button--primary {
        background-color: #FF6666;
      }
    }
  }
}
.van-button:before {
  background-color: transparent;
}
// 发票 - 充值页面
.bill {
  .van-tab__pane {
    background-color: $bg-color-white;
    padding: 0;
  }
}
.rechange {
  .head {
    padding: 0px 2vh 2vh;
    b {
      border-radius: 6vw;
      font-weight: normal;
      background-color: #eee;
      button {
        border-radius: inherit;
        background-color: transparent;
        border: none;
        margin: .3vh;
        &.active {
          background-color: $bg-color-white;
          color: $btnBgcolor;
        }
      }
    }
    .all {
      border: none;
      background-color: $btnBgcolor;
      color: $txt-color-white;
    }
  }
  .rechange_box {
    background-color: $bg-color-gray;
    height: 116vw;
    overflow: auto;
    .van-checkbox {
      padding: 1.5vh 1.5vh 0;
      .van-checkbox__label {
        background-color: $bg-color-white;
        border-radius: 4vw;
        padding: 1vh;
        text-align: left;
        .flex_space-between {
          font-size: 1.1rem;
          line-height: 6vh;
        }
        .van-button--info {
          border: none;
          top: 1vh;
        }
      }
    }
  }
  .total_box {
    position: fixed;
    left: 0;
    bottom: 0;
    background-color: $bg-color-white;
    width: 96vw;
    padding: 4vw 2vw;
    >div {
      display: flex;
      justify-content: space-between;
      line-height: 12vw;
    }
    b {
      i {
        color: red;
        font-style: normal;
      }
    }
    .van-button--primary {
      background-color: $btnBgcolor;
      border: 1px solid $btnBgcolor;
      border-radius: 4vw;
      width: 26vw;
    }
  }
}
// 企业卡管理
.capital {
  .detail {
    color: $txt-color-theme;
    padding: 2vh;
    p {
      margin: 0;
      padding-bottom: 2vh;
      &.money {
        font-size: 1.5rem;
        span {
          font-size: 1rem;
        }
      }
    }
    .van-button--large {
      border-radius: 4vw;
      background-color: $btnBgcolor;
      border-color: $btnBgcolor;
      letter-spacing: 1vw;
    }
  }
  .distribution {
    background-color: $bg-color-white;
    line-height: 10vw;
    text-align: left;
    padding-left: 4vw;
  }
  .total {
    line-height: 14vw;
    span {
      color: rgb(174,23,50);
    }
  }
  .distribution-box {
    margin: 0 4vw 4vw;
    padding: 0 4vw;
    background-color: $bg-color-white;
    border-radius: 3vw;
    .flex_space-between {
      line-height: 9vw;
      b {
        font-size: 1.1rem;
      }
      em {
        font-style: normal;
      }
    }
    .van-button--large {
      background-color: transparent;
      border-color: transparent;
      color: rgb(245,152,41);
      letter-spacing: 2vw;
      border-top: 1px solid $bg-color-gray;
    }
    .van-button:before {
      border-color: transparent;
    }
  }
}
// 司机卡管理
.cancelcard {
  .content {
    margin: 6vw 4vw;
    padding: 4vw;
    background-color: $bg-color-white;
    border-radius: 4vw;
    div {
      line-height: 10vw;
      b {
        font-size: 1.1rem;
      }
    }
  }
  .notice {
    padding: 4vw;
    text-align: left;
    color: rgb(243,140,38);
    background-color: rgb(248, 245, 240);
    div {
      font-size: 1.1rem;
      padding-bottom: 4vw;
    }
  }
  .van-checkbox {
    font-size: .8em;
    padding-left: 4vw;
    background-color: rgb(248, 245, 240);
    .van-checkbox__label {
      color: rgb(167, 167, 167);
    }
  }
  .footer {
    position: absolute;
    left: 0;
    bottom: 0;
    padding: 0 4vw 4vw;
    button {
      &.cancel {
        background-color: rgb(194,194,194);
        margin-left: 4vw;
      }
    }
  }
}
// 资金分配汇总
.van-grid {
  span {
    margin-top: 2vw;
  }
}
.distribute {
  .van-field {
    margin: 4vw;
    width: 92%;
    border-radius: 6vw;
  }
  .total {
    background-color: $bg-color-white;
    line-height: 10vw;
    margin-bottom: 4vw;
  }
}
.distribute_box {
  margin: 4vw;
  .van-swipe-cell {
    border-radius: 4vw;
    background-color: $bg-color-white;
    .flex_space-between {
      padding: 2vw;
      line-height: 4vw;
      em {
        font-style: normal;
      }
    }
    .van-button  {
      height: 100%;
    }
  }
}