<!--
 * @Author: Yongon
 * @Date: 2022-07-04 11:38:10
 * @LastEditors: ltw <EMAIL>
 * @LastEditTime: 2024-05-16 23:30:25
 * @Description: file content
-->
<template>
  <van-dialog v-model="show" :title="'有新版本v' + updatData.version" :show-confirm-button="false" width="280px" style="z-index:9999">
    <div class="update_container">
      <div class="content">
        <p class="title">更新提示：</p>
        <p class="info">发现新版本（v{{ updatData.version }}）,为了增强您的使用体验，请尽快前往应用市场下载最新版本</p>
        <p class="title">更新内容：</p>
        <p class="info">{{ updateObj.content }}</p>
        <p class="tips">（若无法更新下载，请联系管理人员）</p>
      </div>
      <div class="btn">
        <van-button class="no_btn" color="#f2f2f2" @click="quitSystem()" v-if="updateObj.updateType == '2'">暂不更新</van-button>
        <van-button class="yes_btn" color="#0380c8" @click="updateApp" :loading="btnLoading" loading-text="加载中...">立即更新</van-button>
      </div>
      <!-- <van-checkbox v-model="checked" shape="square">不再提示</van-checkbox> -->
    </div>
  </van-dialog>
</template>

<script>
import { storage, isAndroid, isBrowser } from "Utils/common/";
import { mapState, mapMutations } from "vuex";
import { GetAppVersion } from "Views/UserModules/_service/index.js";
import { recordJsLog } from "@/utils/recordJsLog";

export default {
  data() {
    return {
      checked: false,
      btnLoading: false,
      show: false,
      version: storage.ss.get("deviceInfo")?.version ?? "1.0.5",
      // 软件更新信息
      updatData: {
        version: "",
        url: "",
      },
      updateObj: {
        updateType: "2",  // 是否强制更新 1-强制更新 2-非强制更新
        content: "",      // 更新内容
        updateChannel: "1",    // 更新渠道 1-应用市场 2-托管平台（蒲公英）
      },
    };
  },
  computed: {
    ...mapState(["permissions"]),
  },
  created() {
    if(!isBrowser()){
      this.GetAppVersion();
    }
  },
  mounted() {
    window.onGetPermissions = this.onGetPermissions;
  },
  methods: {
    ...mapMutations(["Update_permissions"]),

    /**
     * @description: sdk获取隐私权限回调（Android专用，iOS均为跳转安装）
     * @return {*}
     */

    onGetPermissions(params) {
      recordJsLog('onGetPermissions', params);

      console.log("update 隐私权限回调", params);

      this.Update_permissions(params);
      this.btnLoading = false;

      // 跳转到应用详情设置页 （注：仅限Android, 后续有小版本可考虑适配各厂商，跳转到具体的权限设置页）
      if (!params.album) {
        this.$Dialog
          .alert({
            title: "存储权限需要",
            message: "请前往设置->应用->中油车队端->权限中打开存储相关权限，否则功能无法正常运行!",
            width: 280,
            //  解决提示层级问题 指定挂载的节点qianyi_container
            getContainer() {
              return document.querySelector(".update_container");
            },
          })
          .then(() => {
            setTimeout(() => {
              recordJsLog('openAuthSetting');

              this.$cppeiBridge("openAuthSetting", {});
            }, 500);
          })
          .catch(() => {});
      } else {
        this.$toast({
          //  解决提示层级问题 指定挂载的节点qianyi_container
          message: "后台更新中",
          getContainer() {
            return document.querySelector(".update_container");
          },
        });
        recordJsLog('updateApp', {
          version: this.updatData.version, // 版本号
          apkUrl: this.updatData.url, // Android新包下载地址
          iosUpdate: storage.ss.get("deviceInfo")?.type == "Android" ? false : true,
        });

        // Android应用内更新
        this.$cppeiBridge("updateApp", {
          version: this.updatData.version, // 版本号
          apkUrl: this.updatData.url, // Android新包下载地址
          iosUpdate: storage.ss.get("deviceInfo")?.type == "Android" ? false : true,
        });
      }
    },

    /**
     * @description: 暂不更新
     * @return {*}
     */

    quitSystem() {
      this.show = false;
    },

    /**
     * @description: 更新软件
     * @return {*}
     */

    updateApp() {
      // 暂时关闭loading
      // this.btnLoading = true;
      console.log("updateApp:" + !this.permissions.album, isAndroid());
      if (isAndroid()) {
        // 安卓更新暂时使用跳转链接更新,暂停应用内更新
        recordJsLog("launchUrl", {
          url: this.updatData.url,
        });

        this.$cppeiBridge("launchUrl", {
          url: this.updatData.url,
        });
        return;
        recordJsLog("getPermissions", {
          permissions: ["album"], //camera-相机，album-相册
        });

        this.$cppeiBridge("getPermissions", {
          permissions: ["album"], // album-相册(本地存储权限)，在隐私权限回调中做调用
        });
      } else { // iOS
        if (this.updateObj.updateChannel == "1") { // 更新渠道为应用市场 
          recordJsLog("updateApp", {
            version: this.updatData.version, // 版本号
            apkUrl: this.updatData.url, // Android新包下载地址
            iosUpdate: storage.ss.get("deviceInfo")?.type == "Android" ? false : true,
          });
        
          this.$cppeiBridge("updateApp", {
            version: this.updatData.version, // 版本号
            apkUrl: this.updatData.url, // Android新包下载地址
            iosUpdate: storage.ss.get("deviceInfo")?.type == "Android" ? false : true,
          });
        } else {
          recordJsLog("launchUrl", {
            url: this.updatData.url,
          });
          
          this.$cppeiBridge("launchUrl",{
            url: this.updatData.url,
          })
        }
      }

      // 安装失败返回后  10S后打开禁用
      setTimeout(() => {
        this.btnLoading = false;
      }, 10000);
    },

    /**
     * @description: 软件更新
     * @return {*}
     */

    GetAppVersion() {
      // this.updatData = {
      //   version: "1.0.6",
      //   updateType: "2",
      //   id: null,
      //   type: 1,
      //   maintenanceTime: null,
      //   url: "http://enterprisecardsit.95504.net/imgs/versionpack/uat/enterprise/fleetcard-d-uat-1.0.5.apk",
      //   state: null
      // }
      GetAppVersion({
        currentVersion: storage.ss.get("deviceInfo")?.version ?? "1.0.5",
        type: "2", // 1-企业端 2-车队端
        osType: storage.ss.get("deviceInfo")?.type ?? "Android", // 手机系统类型（iOS、Android）
      })
        .then((res) => {
          if (res.data && res.infoCode == 1) {
            this.updateObj = {
              updateType: res.data.updateType,  // 是否强制更新 1-强制更新 2-非强制更新
              content: res.data.content,        // 更新内容
              updateChannel: res.data.updateChannel, // 更新渠道 1-应用市场 2-托管平台（蒲公英）
            };
            this.updatData = {
              version: res.data.version,
              url: res.data.url,
            };
            // 判断是否需要更新（弹框）
            if (storage.ss.get("deviceInfo")?.type == "Android") {
              this.isUpdate(res.data.version);
            } else {
              if (res.data.updateChannel == "1") { // 更新渠道为应用市场 --通过AppStore接口返回的版本号判断
                this.GetAppVersionByIos();
              } else {
                this.isUpdate(res.data.version); // 更新渠道为托管平台（蒲公英）--通过接口返回的版本号判断
              }
            }
          }
        })
        .catch((err) => {});
    },

    /**
     * @description: IOS更新
     * @return {*}
     */

    GetAppVersionByIos() {
      this.$http("POST", "https://itunes.apple.com/lookup?id=1623529166")
        .then((res) => {
          if (res.results.length > 0) {
            this.updatData = {
              version: res.results[0].version || "1.0.5",
              url: "",
            };
            this.isUpdate(res.results[0].version);
          }
        })
        .catch((err) => {});
    },

    /**
     * @description: 判断软件是否更新
     * @return {*}
     */
    isUpdate(version) {
      if (version > storage.ss.get("deviceInfo")?.version) {
        this.show = true;
      } else {
        this.show = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.update_container {
  padding: 0 40px 40px 40px;
  box-sizing: border-box;

  .content {
    text-align: left;
    p {
      margin: 0px;
      line-height: 40px;
      font-size: 30px !important;
    }
    .title {
      margin-top: 15px;
    }
    .info {
      text-indent: 2em;
    }
    .tips{
      font-size: 26px !important;
      color: #ff0000;
      margin-top: 15px
    }
  }
  .btn {
    width: 100%;
    margin: 30px 0;
    text-align: center;
    display: flex;
    justify-content: space-around;
    .van-button {
      width: 110px;
      height: 40px;
    }
    .no_btn {
      color: $sysAppColor !important;
    }
    .yes_btn {
      color: #fff !important;
      // margin-left: 20px;
    }
  }
}
/deep/.van-dialog__header {
  padding-top: 20px !important;
}
</style>
