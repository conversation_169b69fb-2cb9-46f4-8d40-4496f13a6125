/**
 *  @module  全局过滤器定义
 *  <AUTHOR>
 *  @desc 主要处理些后端返回的数据格式与实际不同时 进行转换
 */
// import dayjs from 'dayjs';

/**
 * 金额格式化(三位一个逗号,整数xx.00)
 * @param {*} money 
 */
 const formatMoney = money => {
  if (!money && money !== 0) return '-';
  var intPart = Number(money) | 0; //获取整数部分
  //将整数部分逢三一断
  var intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
  var floatPart = ".00"; //预定义小数部分
  var value2Array = money.toString().split(".");
  //=2表示数据有小数位
  if (value2Array.length == 2) {
    floatPart = value2Array[1].toString(); //拿到小数部分
    if (floatPart.length == 1) { //补0,实际上用不着
      return intPartFormat + "." + floatPart + '0';
    } else {
      return intPartFormat + "." + floatPart;
    }
  } else {
    return intPartFormat + floatPart;
  }
};

/**
 * 姓名格式化隐藏
 * @param {*} name 
 */
const formatName = name => {
  return new Array(name.length).join('*') + name.substr(-1);
};

/**
 * 身份证号格式化
 * @param {*} idCard 
 */
const formatIdCard = idCard => {
  return `${idCard.substr(0, 2)}**************${idCard.substr(16, 18)}`;
};

/**
 *  手机号格式化
 *  @param {String} phone
 *
 */
const formatPhone = phone => {
  phone = phone.toString();
  return `${phone.substr(0, 3)}****${phone.substr(7, 11)}`;
};

/**
 * 格式化成默认(****)
 * @param {*} str 
 */
const formatDef = str => {
  return "****";
};

/**
 *  百分号格式化
 *  @param {String} n
 *
 */
const formatPersent = n => {
  return !!n ? `${(n - 0) * 100}%` : '';
};

/**
 *  银行卡格式化
 *  @param {String} n
 *
 */
const formatBankCard = bankCard => {
  bankCard = bankCard.toString();
  return bankCard.replace(/^(\d{4})\d+(\d{4})$/, "$1 **** **** $2");
};

/**
*  油卡号格式化
*  @param {String} n
*
*/
const formatOilCard = oilCard => {
  oilCard = oilCard.toString();
  return oilCard.replace(/(\d{13})/g, "*********");
};
// 加油站状态
// Status; 1 初始状态;2 加油站正常营业;3 加油站临时关闭-整修;4 加油站关闭-永久关闭;9 加油站关闭-组织机构调整
const oilStatus = status => {
  let val;
  switch (status) {
    case '1':
      val = '初始状态'
      break;
    case '2':
      val = '营业中'
      break;
    case '3':
      val = '关闭'
      break;
    case '4':
      val = '关闭'
      break;
    case '9':
      val = '关闭'
      break;
    default:
      val = '营业中'
  }
  return val
}
/**
 * 错误信息格式化
 */
const errorMsg = errorArr =>{
  let errorMsgList = errorArr || []
  let errorMsg = ''
  errorMsgList.forEach(item => {
    if(item.pan && item.pan != ''){
      errorMsg += item.error + '-' + item.message + '-卡号'+ item.pan + '\n'
    }else{
      errorMsg += item.error + '-' + item.message + '\n' 
    }
  });
  return errorMsg
}

// SDK注册状态(0-未初始化, 1-未注册, 2-注册中, 3-注册完成, 4-注销, 5-授权证书过期)
const sdkRegisterStatus = status => {
  let val;
  switch (status) {
    case 0:
      val = 'SDK未初始化,请重新登录'
      break;
    case 1:
      val = 'SDK未注册,请重新登录'
      break;
    case 2:
      val = 'SDK注册中,请耐心等待'
      break;
    case 3:
      val = 'SDK注册完成'
      break;
    case 4:
      val = 'SDK已注销,请重新登录'
      break;
    case 5:
      val = 'SDK授权证书已过期,请重新登录'
      break;
  }
  return val
}

export default {
  formatMoney,
  formatName,
  formatIdCard,
  formatPhone,
  formatDef,
  formatPersent,
  formatBankCard,
  formatOilCard,
  oilStatus,
  errorMsg,
  sdkRegisterStatus
};
