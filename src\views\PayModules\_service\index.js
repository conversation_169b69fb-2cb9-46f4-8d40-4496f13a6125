import request from "Utils/http/"; // 导入axios封装函数

/**
 * 附近加油站列表接口
 * @param params
 * @returns {Promise<*>}
 * @constructor
 */

// 11.2查询室内支付待支付订单接口【倪俊聪】
export async function GetUnPayOrderInfo(params) {
  return request(
    "post",
    "/appcenter/trade/v1/getUnPayOrderInfo",
    params,
    false,
    true,
    true,
    false,
    true
  );
}

// 11.4查询室内支付订单信息接口【倪俊聪】
export async function GetIndoorPayOrderInfo(params) {
  return request(
    "post",
    "/appcenter/trade/v1/getIndoorPayOrderInfo",
    params,
    false,
    true,
    true,
    false,
    true
  );
}
// 导航列表接口
export async function GetNavigationList(params) {
  return request(
    "post",
    "/user/v1/getNavigationList",
    params,
    true,
    true,
    true,
    false,
    true
  );
}
