<!--
 * @Author: Yongon
 * @Date: 2022-06-29 08:46:33
 * @LastEditors: ltw <EMAIL>
 * @LastEditTime: 2024-04-28 09:14:48
 * @Description: 退出登录
-->
<template>
  <div class="setting">
    <nav-bar title="设置" />
    <div class="cells">
      <van-cell title="查看 《隐私协议》" link-type="reLaunch" to="/privacyPolicy"> </van-cell>
      <van-cell title="查看 《用户协议》" link-type="reLaunch" to="/userAgreement"> </van-cell>
      <van-cell :title="version"></van-cell>
    </div>
    <div class="btn">
      <van-button type="default" size="large" @click="quit">退出登录</van-button>
    </div>
    <!-- 备案号 -->
    <p class="filing" @click="toHttp">京ICP备14026641号-41A</p>
  </div>
</template>

<script>
import navBar from "@/components/navBar/NavHeader";
import { storage } from "Utils/common/";
export default {
  props: {},
  components: {
    navBar,
  },
  data() {
    return {
      version: storage.ss.get("deviceInfo")?.version ?? "V1.0.5",
    };
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    quit() {
      this.$Dialog
        .confirm({
          title: "提示",
          message: "确认执行此操作",
        })
        .then(() => {
          this.$cppeiBridge("clearToken", {});
          storage.ss.remove("apptoken");
          storage.ss.clear();
          this.$router.replace({
            path: "/loginCenter",
          });
        });
    },
    doQuit() {},
    toHttp() {
      this.$cppeiBridge("launchUrl",{
        url: "https://beian.miit.gov.cn",
      })
    }
  },
};
</script>

<style scoped lang="scss">
.setting {
  .cells {
    margin-bottom: 20px;
  }

  .van-cell {
    text-align: left;
    margin-bottom: 2px;
  }

  .btn {
    padding: 10px 12px;
  }

  .filing {
    position: fixed;
    bottom: 120px;
    left: 0;
    right: 0;
    color: #999;
  }
}
</style>
