import cppeiBridge from "Utils/bridge";
import { storage, LOCATION_INFO} from "Utils/common";
import router from '@/router/index'
import { recordJsLog } from "@/utils/recordJsLog";
import { Dialog } from "vant";

/**
 * params 定位回调参数
 * 
 * */ 
export function useGetLocation(params, callBack){
  recordJsLog(params.route + '-->onGetLocation', params);
  console.log(params.route +' ==> onGetLocation ====>', JSON.stringify(params));
  let routerName = router.app._route.name;
  console.log('useGetLocation ===>', routerName);

  if(params.route === routerName){
    // error不为空时，H5 展示 error 内容，为空时若 city 为空，保持原逻辑错误提示
    if(!params.error){ // 判断字符串为空
      // 是否打开了定位服务和权限
      if (params.hasLocationPermission) {
        // 如果省市区只要其中一个有值 则判断获取定位成功
        if (params.province || params.city || params.district) {
          if (storage.ss.get("deviceInfo")) {
            storage.ss.set("deviceInfo", { ...storage.ss.get("deviceInfo"), ...params });
          } else {
            storage.ss.set("deviceInfo", { ...params });
          }
          callBack && callBack(true);
        
        } else {
          Dialog.confirm({
            title: "获取定位失败",
            message: "重新获取定位，否则加油等核心功能将无法使用",
            confirmButtonText: "获取定位",
            cancelButtonText: "取消（功能受限）",
          })
          .then(() => {
            setTimeout(() => {
              recordJsLog('getLocation', {route: routerName});
              cppeiBridge("getLocation", {route: routerName});
            }, 500);
          })
          .catch(() => {
            if (storage.ss.get("deviceInfo")) {
              storage.ss.set("deviceInfo", { ...storage.ss.get("deviceInfo"), ...LOCATION_INFO });
            } else {
              storage.ss.set("deviceInfo", { ...LOCATION_INFO });
            }
            callBack && callBack(false); 
          });
        }
      } else {
        Dialog.confirm({
            title: "获取定位失败",
            message: "未打开定位服务，请去设置打开，否则加油等核心功能将无法使用",
            confirmButtonText: "跳转到设置打开",
            cancelButtonText: "好的，手动打开",
          })
          .then(() => {
            recordJsLog('openLocSetting');
            cppeiBridge("openLocSetting", {});
          })
          .catch(() => {
            console.log("好的，手动打开:" + JSON.stringify(params));
            if (storage.ss.get("deviceInfo")) {
              storage.ss.set("deviceInfo", { ...storage.ss.get("deviceInfo"), ...LOCATION_INFO });
            } else {
              storage.ss.set("deviceInfo", { ...LOCATION_INFO });
            }
            callBack && callBack(false);  
          });
      }
    }else{
      Dialog.alert({
          title: "提示",
          message: params.error
        })
        .then(() => {});
    }
  }
  
}