---
description: 中油智行车队卡司机端H5前端项目开发总览
globs: .md,.vue,.js,.scss
alwaysApply: true
---

# 前端项目开发总览

## 📋 总体描述

本文档是中油智行车队卡司机端H5前端项目的开发总览，涵盖项目技术栈、工程结构、开发规范等核心内容，为前端开发提供全面的架构指引和规范约束。

## 🎯 应用范围

- **开发人员**：前端开发工程师、全栈开发工程师
- **项目管理**：项目经理、技术负责人
- **质量保证**：代码审查人员、测试工程师
- **运维部署**：DevOps工程师、运维人员

## 📖 使用要求

1. **强制遵循**：所有开发人员必须严格按照本规范进行开发
2. **代码审查**：代码提交前必须通过规范检查
3. **持续更新**：规范文档需要随项目演进持续更新
4. **团队培训**：新成员入职时必须学习本规范体系

## 🏗️ 项目技术栈

### 核心框架
- **Vue.js**: 2.6.11 (渐进式JavaScript框架)
- **Vue CLI**: 4.2.0 (Vue.js开发工具链)
- **Vue Router**: 3.1.5 (官方路由管理器)
- **Vuex**: 3.1.2 (状态管理模式)

### 构建工具
- **Webpack**: 4.47.0 (模块打包器)
- **Babel**: ES6+语法转换
- **PostCSS**: CSS后处理器
- **SCSS**: CSS预处理器

### UI组件库
- **Vant**: 2.12.48 (移动端Vue组件库)
- **Better Scroll**: 1.15.2 (移动端滚动解决方案)

### 网络请求
- **Axios**: 0.19.2 (HTTP客户端)
- **统一封装**: src/utils/http/ (请求拦截、响应处理)

### 开发工具
- **VConsole**: 3.4.0 (移动端调试工具)
- **ESLint**: 代码质量检查 (当前已关闭)
- **Prettier**: 代码格式化工具

### 移动端适配
- **postcss-px-to-viewport**: px转vw适配方案
- **style-vw-loader**: Webpack样式适配加载器
- **设计稿宽度**: 750px

## 📁 工程结构

```
petro-soti-zyzxcdkdriverfront-h5/          # 项目根目录
├── public/                                  # 静态资源目录
│   ├── favicon.ico                         # 网站图标
│   ├── index-dev.html                      # 开发环境HTML模板
│   ├── index-prod.html                     # 生产环境HTML模板
│   └── static/                             # 静态文件
├── src/                                    # 源代码目录
│   ├── App.vue                             # 根组件
│   ├── main.js                             # 应用入口文件
│   ├── registerServiceWorker.js            # PWA服务工作者注册
│   ├── assets/                             # 资源文件
│   │   ├── images/                         # 图片资源
│   │   ├── styles/                         # 全局样式
│   │   │   ├── _variable.scss              # SCSS变量
│   │   │   └── _mixin.scss                 # SCSS混入
│   │   └── font/                           # 字体文件
│   ├── components/                         # 公共组件
│   │   ├── navBar/                         # 导航栏组件
│   │   ├── footerNav/                      # 底部导航组件
│   │   ├── loading/                        # 加载组件
│   │   └── ...                             # 其他公共组件
│   ├── views/                              # 页面组件
│   │   ├── UserModules/                    # 用户模块
│   │   ├── PayModules/                     # 支付模块
│   │   ├── StationList/                    # 油站列表
│   │   ├── GasStationMap/                  # 油站地图
│   │   └── ...                             # 其他业务页面
│   ├── router/                             # 路由配置
│   │   ├── index.js                        # 路由实例
│   │   └── routes.js                       # 路由定义
│   ├── store/                              # Vuex状态管理
│   │   └── index.js                        # 状态管理入口
│   ├── utils/                              # 工具函数
│   │   ├── http/                           # HTTP请求封装
│   │   │   ├── index.js                    # Axios封装
│   │   │   └── throwErrorCode.js           # 错误码处理
│   │   ├── common/                         # 通用工具
│   │   ├── bridge.js                       # 原生桥接
│   │   └── ...                             # 其他工具函数
│   ├── config/                             # 配置文件
│   │   ├── setting.js                      # 项目配置
│   │   └── plugins.js                      # 插件配置
│   ├── filters/                            # 过滤器
│   │   └── index.js                        # 全局过滤器
│   └── skeleton/                           # 骨架屏
│       ├── index.js                        # 骨架屏入口
│       └── skeletonRoutes.js               # 骨架屏路由
├── .env.development                        # 开发环境变量
├── .env.production                         # 生产环境变量
├── vue.config.js                           # Vue CLI配置
├── postcss.config.js                       # PostCSS配置
├── babel.config.js                         # Babel配置
└── package.json                            # 项目依赖配置
```

## 📚 规范文档体系

| 序号 | 规则名称         | 核心内容                                                     |
| ---- | ---------------- | ------------------------------------------------------------ |
| 1    | 前端_总览.md     | **技术栈说明**：Vue 2.6.11生态系统<br />**工程结构**：项目目录组织和文件分类<br />**开发环境**：构建工具和开发配置 |
| 2    | 前端_公共规则.md | **编码规范**：JavaScript/Vue代码风格<br />**命名规范**：文件、组件、变量命名<br />**注释规范**：代码注释和文档规范 |
| 3    | 前端_路由.md     | **路由配置**：Vue Router 3.x配置方式<br />**懒加载**：动态import实现代码分割<br />**路由守卫**：权限控制和页面缓存管理 |
| 4    | 前端_状态管理.md | **Vuex架构**：单一store状态管理<br />**状态设计**：state、mutations、actions规范<br />**模块化**：状态管理的组织方式 |
| 5    | 前端_API接口.md  | **HTTP封装**：Axios统一配置和拦截器<br />**接口规范**：请求格式和响应处理<br />**错误处理**：统一错误码和异常处理机制 |
| 6    | 前端_UI.md       | **移动端适配**：vw适配方案和响应式设计<br />**Vant组件**：UI组件库使用规范<br />**样式规范**：SCSS编写和BEM命名法 |

## 🔄 开发流程

### 开发环境搭建
1. **Node.js**: 建议使用v14.x或v16.x版本
2. **包管理器**: 使用npm或yarn
3. **IDE配置**: 推荐VSCode + Vetur插件
4. **调试工具**: Chrome DevTools + VConsole

### 代码提交流程
1. **功能开发**: 基于develop分支创建feature分支
2. **代码检查**: 自测功能完整性和兼容性
3. **代码审查**: 提交Pull Request进行代码审查
4. **合并发布**: 审查通过后合并到develop分支

### 构建部署流程
1. **开发构建**: `npm run serve` (端口8888)
2. **生产构建**: `npm run build` (输出到fleetcard_driver目录)
3. **部署发布**: 通过deploy脚本自动部署到服务器
