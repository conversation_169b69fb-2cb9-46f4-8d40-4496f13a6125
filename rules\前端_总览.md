---
description: 前端项目开发总览
globs: .md
alwaysApply: true
---

### 总体描述
该文档对前端项目进行了全面概述。

#### 应用范围
本文档适用于所有前端项目的开发人员、项目经理和相关技术人员，帮助他们了解前端项目的整体架构。

#### 使用要求
开发人员在进行前端项目开发时要遵循相关规则文档的要求进行开发，确保项目的一致性和可维护性。项目经理可以根据本文档对前端项目进行整体规划和管理。

### 前端总览

|      | 规则名称         | 用途描述                                                     |
| ---- | ---------------- | ------------------------------------------------------------ |
| 1    | 前端_总览.md     | 1.技术栈：说明前端所用全部技术能力<br />2.工程结构：展示前端代码的整体目录组织 |
| 2    | 前端_路由.md     | 1.路由配置结构：定义路由的基础配置方式<br />2.路由懒加载：规定所有路由组件需通过动态导入实现加载优化<br />3.路由命名规则：明确路由名称和路径的命名规范 |
| 3    | 前端_状态管理.md | 1.状态管理结构：说明状态管理的基本架构设计<br />2.状态管理使用规范：规定状态管理在项目中的具体使用方式 |
| 4    | 前端_API接口.md  | 1.API定义：明确API请求方法的管理方式、类型定义等基础规则<br />2.响应格式定义：规范API响应数据的统一结构<br />3.状态码定义：划分不同类型状态码的含义与适用场景<br />4.请求拦截：说明请求拦截器的功能与实现要求<br />5.响应拦截：阐述响应拦截器的处理逻辑与特殊情况处理 |
| 5    | 前端_UI.md       | 1.布局规范：规定页面整体及各区域的布局方式<br />2.色彩规范：定义项目使用的主色调、中性色等色彩标准<br />3.字体规范：明确字体大小、权重、行高的统一规则<br />4.组件规范：制定基础组件尺寸及UI组件的定制标准<br />5.间距规范：确定基础间距单位及页面元素间的间距规则<br />6.表单规范：规范表单的布局、控件样式及验证方式<br />7.表格规范：定义表格的基础样式、功能及状态展示规则<br />8.按钮规范：明确按钮的类型、尺寸及状态样式标准<br />9.图标规范：规定图标库的选用及使用原则<br />10.交互规范：说明页面交互中的反馈机制、加载状态及动画效果要求 |
