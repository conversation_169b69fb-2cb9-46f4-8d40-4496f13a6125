<template>
  <div class="container">
    <nav-bar title="油站详情" />
    <baidu-map class="mapbox" :center="[oilStationList]" :currentOilStation="oilStationList" />
    <div class="cont" id="gasDetails">
      <!-- <div class="cont" id="gasDetails" v-dragging> -->
      <gnc :details="false" :yyz="false" :oilStationList="[oilStationList]" />
      <div class="van-hairline--top"></div>
      <!-- <van-cell-group> -->
      <van-cell class="pos_nav" :title="oilStationList.address" icon="location-o" />
      <van-cell class="pos_nav" :title="oilStationList.stationPhone">
        <template #icon>
          <img class="tel" src="@/assets/images/common/logo.png" />
        </template>
      </van-cell>
      <!-- </van-cell-group> -->
      <!-- <div class="no"></div> -->
      <div class="list">
        <div class="list_li" v-if="oilStationList.RoundJingDian">
          <div class="tit">周边景点</div>
          <div class="des">{{ oilStationList.RoundJingDian }}</div>
          <div class="van-hairline--top"></div>
        </div>
        <div class="list_li" v-if="oilStationList.RoundBBQ">
          <div class="tit">周边餐饮</div>
          <div class="des">{{ oilStationList.RoundBBQ }}</div>
          <div class="van-hairline--top"></div>
        </div>
        <div class="list_li" v-if="oilStationList.RoundStayRoom">
          <div class="tit">周边酒店</div>
          <div class="des">{{ oilStationList.RoundStayRoom }}</div>
          <div class="van-hairline--top"></div>
        </div>
        <div class="list_li" v-if="oilStationList.RoundBank">
          <div class="tit">周边银行</div>
          <div class="des">{{ oilStationList.RoundBank }}</div>
          <div class="van-hairline--top"></div>
        </div>
        <div class="list_li" v-if="oilStationList.otherServices">
          <div class="tit">其他服务</div>
          <div class="des">{{ oilStationList.otherServices }}</div>
          <div class="van-hairline--top"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import navBar from "@/components/navBar/NavHeader";
import gnc from "./component/gas_name_comt.vue";
import baiduMap from "./component/baiduMap.vue";
import { mapState } from "vuex";
import { GetNavigationInfo } from "./_service/";
export default {
  name: "gasstationmap",
  components: {
    gnc,
    baiduMap,
    navBar,
  },
  computed: {
    ...mapState(["rstParams", "deviceInfo"]),
  },
  data() {
    return {
      oilStationList: {},
    };
  },
  watch: {},
  methods: {},
  created() {
    // // this.oilStationList = JSON.parse(this.$route.query.list);
    // GetNavigationInfo({
    //   appid: 2,
    //   apptoken: this.rstParams.apptoken,
    //   stationCode: this.oilStationList.oilStationCode,
    // }).then((res) => {
    //   // const { data: { gasStationList } = { gasStationList: {} } } = res;
    //   // this.oilStationList = gasStationList;
    //   // this.currentOilStation = gasStationList[0];
    // });
  },
  mounted() {},
  activated() {
    console.log(JSON.parse(this.$route.query.list), "JSON.parse(this.$route.query.list)");
    this.oilStationList = JSON.parse(this.$route.query.list);
    // GetNavigationInfo({
    //   appid: 2,
    //   apptoken: this.rstParams.apptoken,
    //   stationCode: this.oilStationList.oilStationCode,
    // }).then((res) => {
    //   // const { data: { gasStationList } = { gasStationList: {} } } = res;
    //   // this.oilStationList = gasStationList;
    //   // this.currentOilStation = gasStationList[0];
    // });
  },
};
</script>
<style lang="scss" scoped>
.container {
  .mapbox {
    height: 88vh;
  }
  .cont {
    max-height: 70vh;
    position: absolute;
    left: 0;
    right: 0;
    // bottom: 90px !important;
    margin: auto;
    bottom: 0;
    border: 1px solid #cdc8c8;
    background: #ffffff;
    overflow: scroll;
    .pos_nav {
      /deep/ .van-icon {
        color: red;
      }
      /deep/ .van-cell__title {
        text-align: left;
      }
    }
    .tel {
      width: 40px;
      height: 36px;
      //   vertical-align: middle;
      margin-right: 12px;
      //   margin-top: 8px;
      margin-left: 5px;
      vertical-align: sub;
    }
    .no {
      height: 10px;
      background: #e8e2e2;
    }
    .list {
      margin: 20px 40px;
      text-align: left;
      .list_li {
        margin: 15px auto;
        .tit {
          font-size: 32px;
          color: #333333;
        }
        .des {
          margin: 20px 0 10px;
          font-size: 24px;
          color: #666666;
        }
      }
    }
  }
}
</style>
