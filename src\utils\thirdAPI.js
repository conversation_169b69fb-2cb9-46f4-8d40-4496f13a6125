/**
 * @object 操作原生第三方接口封装
 * @method  callBridge
 * @method  registeBridge
 * @method  setupWebViewJavascriptBridge
 * @method  getPhoto
 * @method  aliPay
 * @method  wxPay
 * @method  turnBack
 * @method  resetPage
 * @method  getBarHeight
 */
const bridgefunc = {
  callBridge: function(bridgeOpStr, paras, callback) {
    this.setupWebViewJavascriptBridge(function(bridge) {
      bridge.callHandler(bridgeOpStr, paras, function responseCallback(
        response
      ) {
        if (callback) {
          callback(response);
        }
      });
    });
  },
  registeBridge: function(bridgeOpStr, callback) {
    this.setupWebViewJavascriptBridge(function(bridge) {
      bridge.registerHandler(bridgeOpStr, function(data, responseCallback) {
        if (callback) {
          callback(data, responseCallback);
        }
      });
    });
  },
  setupWebViewJavascriptBridge: function(callback) {
    /* eslint-disable */
    if (window.WebViewJavascriptBridge) {
      return callback(WebViewJavascriptBridge);
    }
    /* eslint-enable */
    if (window.WVJBCallbacks) {
      return window.WVJBCallbacks.push(callback);
    }
    window.WVJBCallbacks = [callback];
    var WVJBIframe = document.createElement("iframe");
    WVJBIframe.style.display = "none";
    WVJBIframe.src = "wvjbscheme://__BRIDGE_LOADED__";
    document.documentElement.appendChild(WVJBIframe);
    setTimeout(function() {
      document.documentElement.removeChild(WVJBIframe);
    }, 0);
  },
  getPhoto: function(phototype, callback) {
    // 2 是拍照 1是从相册选择
    let jsonData = {};
    jsonData.phototype = phototype;
    jsonData.type = "getphoto";
    bridgefunc.callBridge("phonebridge", jsonData, callback);
  },
  aliPay: function(info, callback) {
    // 阿里支付
    let jsonData = {};
    jsonData.type = "alipay";
    jsonData.aliPreapy = info;
    bridgefunc.callBridge("phonebridge", jsonData, callback);
  },
  wxPay: function(info, callback) {
    // 支付回调
    let jsonData = {};
    jsonData.type = "wechatpay";
    jsonData.payinfo = info;
    bridgefunc.callBridge("phonebridge", jsonData, callback);
  },
  getMapLocation: function(callback) {
    let jsonData = {};
    jsonData.type = "locationinfo";
    bridgefunc.callBridge("phonebridge", jsonData, callback);
  },

  turnBack: function(n) {
    // n不传 默认返回上一级
    let jsonData = {};
    jsonData.type = "turnback";
    if (n && typeof n === "number") {
      jsonData.index = n + "";
    }
    bridgefunc.callBridge("phonebridge", jsonData);
  },
  resetPage: function(jsonData) {
    // 跳转到好客e站登录页
    // let webtype = 'store.state.webtype'
    let weburl = jsonData.weburl;
    if (typeof weburl === "undefined") {
      console.log("传参有误");
      return;
    } else if (weburl.indexOf("http") !== 0) {
      weburl = window.location.href.split("#")[0] + "#" + weburl;
    }
    // if (webtype !== '1') {
    //   window.location.href = weburl
    //   return
    // }
    console.log("weburl", weburl);
    jsonData.weburl = weburl;
    jsonData.type = "resetpage";
    jsonData.isnativetop = "0";
    bridgefunc.callBridge("phonebridge", jsonData);
  },
  getBarHeight: function(callback) {
    // 获取设备头高度（电量，时间那个高度）
    let jsonData = {};
    jsonData.type = "barheight";
    bridgefunc.callBridge("phonebridge", jsonData, function(height) {
      let heightStr = height + "";
      callback(parseInt(heightStr));
    });
  },
  registePageShow: function(callback) {
    bridgefunc.registeBridge("pagewillshow", function() {
      callback();
    });
  }
};

export default bridgefunc;
