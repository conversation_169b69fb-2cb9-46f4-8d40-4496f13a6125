<template>
  <div class="discount-contract">
    <!-- 头部 -->
    <nav-bar title="优惠合同" />
    <!-- 有优惠合同 -->
    <div v-if="1 === 2">
      <div class="tips">你的加油卡已绑定以下折扣</div>
      <van-cell-group>
        <div class="not-empty">
          <div class="not-empty-div">
            <span id="discount-name">88折扣1277</span>
          </div>
          <div class="not-empty-div">
            <span>油卡优惠合同或优惠规则已过期,详情请咨询站点</span>
          </div>
        </div>
      </van-cell-group>
    </div>
    <!-- 无优惠合同 -->
    <div class="empty" v-else>
      <img class="img" src="@/assets/footerNav/oil.png" alt="" />
      <div>暂未绑定优惠合同</div>
      <div>请咨询加油站员工，了解更多信息</div>
    </div>
  </div>
</template>

<script>
import navBar from "@/components/navBar/NavHeader";

export default {
  props: {},
  components: { navBar },
  data() {
    return {};
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    loadData() {},
  },
};
</script>

<style scoped lang="scss">
.discount-contract {
  .tips {
    padding: 20px;
    text-align: left;
    // margin-bottom: 30px;
    background-color: rgba(255, 255, 255, 100%) !important;
  }
  /deep/ .van-cell-group {
    background-color: rgba(239, 239, 239, 100%) !important;
    .not-empty {
      height: auto;
      margin-bottom: 2px;
      padding: 10px;
      background-color: #fff;
      .not-empty-div {
        height: 25px;
        display: flex;
        justify-content: space-between;
        #discount-name {
          color: #ff9200;
        }
      }
    }
  }
  .empty {
    height: 85%;
    background-color: #fff;
    .img {
      height: 68/96 * 300px;
      width: 300px;
      padding-top: 300px;
      padding-bottom: 30px;
    }
    div {
      color: #727272;
      margin: 20px 0;
    }
  }
}
</style>
