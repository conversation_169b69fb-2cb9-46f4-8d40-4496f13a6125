import cppeiBridge from "Utils/bridge";
// let resolveCallback = null;

// // 将onBangbangEncrypt方法挂载到window对象上
// window.onBangbangEncrypt = function (params) {
// //   console.log("bangbangEncrypt----5", params);
//   if (resolveCallback) {
//     resolveCallback(params);
//   }
// };

// export const getBangbangEncrypt = (data) => {
// // console.log("bangbangEncrypt------1");
//   return new Promise((resolve) => {
//     // console.log(data,"====入参");
//     resolveCallback = resolve;
//     cppeiBridge("bangbangEncrypt", data);
//   });
// };


let resolveCallback = null;
let queue = [];
let isProcessing = false;

// 将onGetDeviceId方法挂载到window对象上
window.onBangbangEncrypt1 = function (params) {
//   console.log("onBangbangEncrypt====回调", params);
    if (resolveCallback) {
        resolveCallback(params);
        isProcessing = false;
        processQueue();
    }
};

export const getBangbangEncrypt1 = (params) => {
    return new Promise((resolve) => {
        queue.push({ resolve, params: {...params} });
        processQueue();
    });
};

const processQueue = () => {
    if (!isProcessing && queue.length > 0) {
        isProcessing = true;
        const { resolve, params } = queue.shift();
        resolveCallback = resolve;
        cppeiBridge("bangbangEncrypt1", params);
    }
};