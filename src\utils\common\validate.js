/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-05-11 10:02:14
 * @LastEditors: Yongon
 * @LastEditTime: 2022-06-20 17:11:23
 * @Description: 格式校验工具方法
 */

export default {
  // 正则规则
  RegExp_Rules: {
    // 生成图片验证码使用
    CHARS: [
      // "0","i","l","o","I","O", // 容易认错的
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      // 字母小写
      "a",
      "b",
      "c",
      "d",
      "e",
      "f",
      "g",
      "h",
      "j",
      "k",
      "m",
      "n",
      "p",
      "q",
      "r",
      "s",
      "t",
      "u",
      "v",
      "w",
      "x",
      "y",
      "z",
      // 字母大写
      "A",
      "B",
      "C",
      "D",
      "E",
      "F",
      "G",
      "H",
      "J",
      "K",
      "L",
      "M",
      "N",
      "P",
      "Q",
      "R",
      "S",
      "T",
      "U",
      "V",
      "W",
      "X",
      "Y",
      "Z",
    ],

    // 省份简写
    PROVINCE_ABBRS: ["京", "沪", "津", "渝", "鲁", "冀", "晋", "蒙", "辽", "吉", "黑", "苏", "浙", "皖", "闽", "赣", "豫", "湘", "鄂", "粤", "桂", "琼", "川", "贵", "云", "藏", "陕", "甘", "青", "宁", "新", "港", "澳", "台"],

    // 手机正则表达式
    PHONE_REG: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,

    // 身份证号正则表达式
    IDCARD_REG: /^[1-9]\d{5}(19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,

    // 车牌正则表达式(含新能源汽车)
    // LICENSE_NUM_REG: /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$/,
    LICENSE_NUM_REG: /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}(([A-HJ-Z]{1}[A-HJ-NP-Z0-9]{5})|([A-HJ-Z]{1}(([DF]{1}[A-HJ-NP-Z0-9]{1}[0-9]{4})|([0-9]{5}[DF]{1})))|([A-HJ-Z]{1}[A-D0-9]{1}[0-9]{3}警)))|([0-9]{6}使)|((([沪粤川云桂鄂陕蒙藏黑辽渝]{1}A)|鲁B|闽D|蒙E|蒙H)[0-9]{4}领)|(WJ[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼·•]{1}[0-9]{4}[TDSHBXJ0-9]{1})|([VKHBSLJNGCE]{1}[A-DJ-PR-TVY]{1}[0-9]{5}))$/,

    // 密码正则表达式,密码必须满足某些规则
    PASSWORD_REG: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,20}$/,
  },

  /* 密码验证 */
  isPassWord(value) {
    const reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,20}$/;
    return reg.test(value);
  },
  /* 是否是手机号 */
  isPhone(value) {
    const reg = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
    return reg.test(value);
  },
  /* 是否为固话 */
  isTel(value) {
    const reg = /^(400|800)([0-9\\-]{7,10})|(([0-9]{4}|[0-9]{3})(-| )?)?([0-9]{7,8})((-| |转)*([0-9]{1,4}))?$/;
    return reg.test(value);
  },
  /* 是否是邮箱 */
  isEmail(value) {
    const reg = /^([a-zA-Z0-9_.-])+@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
    return reg.test(value);
  },
  /* 是否是网址 */
  isUrl(value) {
    const reg = /(^#)|(^http(s*):\/\/[^\s]+\.[^\s]+)/;
    return reg.test(value);
  },
  /* 是否是数字 */
  isNumber(value) {
    /*const reg = /^1\d{10}$/;
    return reg.test(value);*/
    return !isNaN(value);
  },
  /* 是否车牌号 */
  isCarNumber(value) {
    const reg = /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}(([A-HJ-Z]{1}[A-HJ-NP-Z0-9]{5})|([A-HJ-Z]{1}(([DF]{1}[A-HJ-NP-Z0-9]{1}[0-9]{4})|([0-9]{5}[DF]{1})))|([A-HJ-Z]{1}[A-D0-9]{1}[0-9]{3}警)))|([0-9]{6}使)|((([沪粤川云桂鄂陕蒙藏黑辽渝]{1}A)|鲁B|闽D|蒙E|蒙H)[0-9]{4}领)|(WJ[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼·•]{1}[0-9]{4}[TDSHBXJ0-9]{1})|([VKHBSLJNGCE]{1}[A-DJ-PR-TVY]{1}[0-9]{5}))$/;
    return reg.test(value);
  },
  /* 是否是日期 */
  isDate(value) {
    const reg = /^(\d{4})[-/](\d{1}|0\d{1}|1[0-2])([-/](\d{1}|0\d{1}|[1-2][0-9]|3[0-1]))*$/;
    return reg.test(value);
  },
  /* 是否是身份证 */
  isIdentity(value) {
    const reg = /^[1-9]\d{5}(19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    return reg.test(value);
  },
  /* 是否是整数 */
  isDigits(value) {
    const reg = /^-?\d+$/;
    return reg.test(value);
  },
  /* 是否是正整数 */
  isDigitsP(value) {
    const reg = /^[1-9]\d*$/;
    return reg.test(value);
  },
  /* 是否是负整数 */
  isDigitsN(value) {
    const reg = /^-[1-9]\d*$/;
    return reg.test(value);
  },
  /* 是否是非负整数(正整数或0) */
  isDigitsPZ(value) {
    const reg = /^\d+$/;
    return reg.test(value);
  },
  /* 是否是非正整数(负整数或0) */
  isDigitsNZ(value) {
    const reg = /^-[1-9]\d*|0/;
    return reg.test(value);
  },
  /* 验证最小长度、最大长度 */
  maxMinLength(value, minLength, maxLength) {
    if (value === undefined || value === null) return !minLength;
    if (minLength && value.toString().length < minLength) return false;
    return !(maxLength !== undefined && maxLength !== null && value.toString().length > maxLength);
  },
  /* 验证最小值、最大值 */
  maxMin(value, min, max) {
    if (value === undefined || value === null) return min === undefined || min === null;
    if (min !== undefined && min !== null && value < min) return false;
    return !(max !== undefined && max !== null && value > max);
  },
  /* 是否是中文 */
  isChina(value) {
    const reg = /^[\u4E00-\u9FA5]{2,4}$/;
    return reg.test(value);
  },
  /* 是否是端口号 */
  isPort(value) {
    const reg = /^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/;
    return reg.test(value);
  },
  /* 是否是IP */
  isIP(value) {
    const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
    return reg.test(value);
  },
  /* 是否是经度 -180.0～+180.0（整数部分为0～180，必须输入1到5位小数） */
  isLongitude(value) {
    const reg = /^[-|+]?(0?\d{1,2}\.\d{1,5}|1[0-7]?\d{1}\.\d{1,5}|180\.0{1,5})$/;
    return reg.test(value);
  },
  /* 是否是纬度 -90.0～+90.0（整数部分为0～90，必须输入1到5位小数） */
  isLatitude(value) {
    const reg = /^[-|+]?([0-8]?\d{1}\.\d{1,5}|90\.0{1,5})$/;
    return reg.test(value);
  },
  /* 是否是身份证(强校验) */
  isIdentityStrong(value) {
    if (!this.isIdentity(value)) return "身份证号码格式错误";
    const ai = value.length === 18 ? value.substring(0, 17) : value.substring(0, 6) + "19" + value.substring(6, 15);
    // 验证出生年月
    const year = ai.substring(6, 10); // 年
    const birthday = year + "/" + ai.substring(10, 12) + "/" + ai.substring(12, 14);
    if (!this.isDate(birthday)) return "身份证号码出生日期无效";
    const now = new Date();
    if (now.getFullYear() - parseInt(year) > 150 || now.getTime() - new Date(birthday).getTime() < 0) return "身份证号码出生日期不在有效范围";
    // 验证地区码
    const areaCodes = ["11", "12", "13", "14", "15", "21", "22", "23", "31", "32", "33", "34", "35", "36", "37", "41", "42", "43", "44", "45", "46", "50", "51", "52", "53", "54", "61", "62", "63", "64", "65", "71", "81", "82", "91"];
    if (areaCodes.indexOf(ai.substring(0, 2)) === -1) return "身份证号码地区编码错误";
    // 验证最后一位
    if (value.length === 18) {
      const valCode = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];
      const wi = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      let totalMulAiWi = 0;
      for (let i = 0; i < 17; i++) totalMulAiWi += parseInt(ai.charAt(i)) * wi[i];
      if (value !== ai + valCode[totalMulAiWi % 11]) return "身份证号码最后一位错误";
    }
  },
  //输入为整数或者最多保留2位小数
  isNumDouble(rule, value, callback) {
    let reg = /^\d+(\.\d{0,2})?$/;
    if (value === "" || value == null) {
      callback();
    } else {
      if (reg.test(value)) {
        callback();
      } else {
        return callback(new Error("只能输入数字，最多保留两位小数"));
      }
    }
  },
  // 验证手机号
  isMobile(rule, value, callback) {
    let reg = /^1\d{10}$/;
    if (value === "" || value == null) {
      callback();
    } else {
      if (reg.test(value)) {
        callback();
      } else {
        return callback(new Error("电话格式不正确,请重新输入"));
      }
    }
  },
  //验证手机号
  isCheckTel(rule, value, callback) {
    if (!value) {
      return callback(new Error("手机号不能为空"));
    } else {
      const reg = /^1[3|4|5|7|8][0-9]\d{8}$/;
      console.log(reg.test(value));
      if (reg.test(value)) {
        callback();
      } else {
        return callback(new Error("请输入正确的手机号"));
      }
    }
  },
  // 身份证验证
  isCheckIdCard(rule, value, callback, source, options, isRequired = false) {
    let regIdCard = /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X|x)$/;
    if (!value && !isRequired) {
      callback();
    } else if (!value) {
      callback(new Error("请输入身份证号"));
    } else if (!regIdCard.test(value)) {
      callback(new Error("身份证号码格式错误"));
    } else {
      callback();
    }
  },
  isCheckAge(rule, value, callback) {
    let reg = /^\+?[1-9][0-9]*$/;
    if (!value) {
      callback(new Error("请填写非零正整数"));
    } else {
      if (reg.test(value) == false) {
        callback(new Error("请输入非零的正整数"));
      } else {
        callback();
      }
    }
  },
  // 长度限制
  isLongLength(rule, value, callback) {
    let reg = /^\+?[1-9][0-9]*$/;
    if (!value) {
      callback(new Error("请填写非零正整数"));
    } else {
      if (reg.test(value) == false) {
        callback(new Error("请输入非零的正整数"));
      } else {
        callback();
      }
    }
  },
};
