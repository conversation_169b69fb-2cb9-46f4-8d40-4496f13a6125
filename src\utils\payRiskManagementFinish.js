/*
 * @Author: Yongon
 * @Date: 2022-04-19 17:10:20
 * @LastEditors: ltw <EMAIL>
 * @LastEditTime: 2024-05-23 08:45:02
 * @Description: file content
 */
import request from "./http"; // 导入axios封装函数
import { storage } from "Utils/common/";
// 风控异步结束调用
const payRiskManagementFinish = function(orderNo, paystatus, tradeType ,deviceId = "") {
    const deviceInfo = storage.ss.get("deviceInfo");
    const defaultLat = "39.920248";
    const defaultLon = "116.407718";
    let params = {
      address:
        deviceInfo?.province +
        ";" +
        deviceInfo?.city +
        ";" +
        deviceInfo?.district,
      appid: 2,
      apptoken: storage.ss.get("apptoken"),
      companyCode: storage.ss.get("defaultCompanyCode"),
      driverPhone: storage.ss.get("phone"),
      latitude: deviceInfo?.lat ?? defaultLat,
      longitude: deviceInfo?.lon ?? defaultLon,
      orderNo: orderNo,
      status: paystatus,
      tradeType: tradeType,
      // deviceId: deviceInfo?.deviceId ?? "",
      deviceId: deviceId,
    };
    request(
      "POST",
      `/appcenter/riskManagement/v1/payRiskManagement`,
      params,
      false,
      true,
      true,
      false,
      true
    )
      .then((res) => {
        // 处理请求成功的情况
        console.log("支付风险管理请求成功", res);
        // 这里添加处理成功的业务逻辑
      })
      .catch((err) => {
        // 这里处理请求失败的内部错误
        console.error("支付风险管理请求失败", err);
        // 可以在这里通过 this.$message 或其他方式通知用户
        // 或者通过 this.$emit 发送事件到父组件
      });
  };

export { payRiskManagementFinish };
