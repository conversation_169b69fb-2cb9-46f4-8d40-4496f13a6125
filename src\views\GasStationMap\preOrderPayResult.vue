<template>
  <div class="order_pay_result">
    <div class="title">消费账单</div>
    <div class="stationName">—— 中国石油 {{tradeOrderMix.stationName}} ——</div>
    <div class="notice">请向加油员出示账单</div>
    <div class="orderDetail">
      <div class="pay_title">实付金额</div>
      <div class="pay_money">￥<b>{{tradeOrderMix.rcvAmt}}</b></div>
      <van-cell-group>
        <van-cell title="订单号" :value="tradeOrderMix.preAuthOrderNo" />
        <van-cell title="商品" v-if="tradeOrderMix.oilName" :value="tradeOrderMix.oilName" />
        <van-cell title="单价" v-if="tradeOrderMix.oilPrice*1>0" :value="'￥'+tradeOrderMix.oilPrice + (tradeOrderMix.oilName.indexOf('氢气') != -1?'/千克':'/升')" />
        <van-cell title="数量" v-if="tradeOrderMix.oilQty*1>0" :value="tradeOrderMix.oilQty+(tradeOrderMix.oilName.indexOf('氢气') != -1?'千克':'升')" />
        <van-cell title="订单金额" :value="'￥'+tradeOrderMix.totalAmount+'元'" />
        <van-cell title="折扣金额" v-if="tradeOrderMix.cardDiscountAmt*1 > 0" :value="'￥'+tradeOrderMix.cardDiscountAmt+'元'" class="discount" />
        <van-cell title="支付方式" value="移动支付" />
        <van-cell title="核销时间" :value="tradeOrderMix.paymentTime" />
        <van-cell title="车牌号" v-if="tradeOrderMix.plateNo" :value="tradeOrderMix.plateNo" />
        <!-- <van-cell title="预授权时间" :value="tradeOrderMix.preAuthTime" />
        <van-cell title="预授权金额" :value="tradeOrderMix.preAuthAmount" />
        <van-cell title="预授权码" :value="tradeOrderMix.preAuthCode" /> -->
      </van-cell-group>
      <van-button type="primary" size="large" @click="goBackhome">确认并返回首页</van-button>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tradeOrderMix: {},
    }
  },
  created() {
    this.Init();
  },
  activated() {},
  methods: {
    // 进入页面初始化
    Init() {
      // 获取路由所传参数
      this.tradeOrderMix = JSON.parse(this.$route.query.tradeOrderMix);
    },
    goBackhome() {
      this.$router.push({path: "/gasstationmap",})
    }
  },
}
</script>
<style lang="scss" scoped>
.order_pay_result {
  // background: url("../../../assets/images/common/my_bg.png") no-repeat top center;
  background: url("../../assets/images/winter/my_bg.png") no-repeat top center;
  background-size: contain;
  .title {
    margin-top: 26vw;
    color: #fff;
  }
  .stationName {
    margin-top: 6vw;
    color: #fff;
  }
  .notice {
    margin-top: 3vw;
    color: #fff;
    font-weight: bold;
    font-style: italic;
    letter-spacing: 2px;
    font-size: 1.8em;
    text-shadow: #fff 0px 0px 10px;
  }
  .orderDetail {
    margin: 4vw 3vw 0;
    background: #fff;
    border-radius: 3vw;
    max-height: 70vh;
    @include useScrollByBrowser(0);
    .pay_title {padding-top: 6vw;}
    .pay_money {
      padding-top: 2vw;
      b { font-size: 2em; }
    }
    .van-cell {
      .van-cell__title {
        color: #969799;
        text-align: left;
      }
      .van-cell__value {
        color: #323233;
        min-width: 70%;
        span {
          display: inline-block;
          text-align: left;
          word-break: break-all;
        }
      }
      &.discount {
        .van-cell__value { color: #ff9200; }
      }
    }
    .van-button--large {
      background-color: #ff9200;
      border: 1px solid #ff9200;
      margin: 4vw 0;
      width: 90%;
      border-radius: 2vw;
      height: 40px;
    }
  }
}
</style>