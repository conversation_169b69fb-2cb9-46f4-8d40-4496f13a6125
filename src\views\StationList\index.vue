<template>
  <div class="page-station-list">
    <van-pull-refresh class="pull-refresh" v-model="refreshing" @refresh="onRefresh">
      <van-list id="list" v-model="isLoading" :finished="isFinished" @load="onLoad" :immediate-check="false"
                finished-text="- 没有更多了 -">
        <ul class="gas-station">
          <li class="gas-station-item" v-for="(v,k) in StationList" :key="k">
            <img class="gas-station-logo" src="@/assets/images/OrderList/logo.png" alt="">
            <div class="gas-station-info">
              <div class="gas-station-info__name">{{ v.StationName }}</div>
              <div class="gas-station-info__distance">距离<span>{{ v.Distance }}</span>公里</div>
            </div>
            <button class="btn-gas" @click="goGas(v)">加油</button>
          </li>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script>

import { GetStationInfos } from './_service/'
import { Toast, PullRefresh, List } from "vant";
import Vue from 'vue';
import { storage } from "Utils/common";

Vue.use(PullRefresh);
Vue.use(List);
export default {
  props: {},
  components: {},
  data() {
    return {
      refreshing: false,
      isLoading: false,
      isFinished: false,
      pageIndex: 1,
      pageSize: 10,
      StationList: []
    };
  },
  computed: {},
  created() {
  },
  mounted() {
    this.GetStationList();
  },
  watch: {},
  methods: {
    goGas(v) {
      storage.ss.set('oilInfo', v)
      this.$router.replace({path: '/chooseGun'})
    },
    // 下拉初始化
    onRefresh() {
      this.refreshing = true
      this.isFinished = false
      this.pageIndex = 1
      this.StationList = []
      this.GetStationList()
    },
    // 上拉加载更多
    onLoad() {
      this.pageIndex++
      this.GetStationList();
    },
    back() {
      setTimeout(_ => {
        this.$router.back();
      }, 1000)
    },
    GetStationList() {
      GetStationInfos({
        "posx": this.$route.query.lng,
        "posy": this.$route.query.lat,
        "distance": 20,
        "type": 0, // 一般网点 0 ，发卡网点 1
        "pageindex": this.pageIndex,
        "pagesize": this.pageSize,
        "cityCode": "1",
      }).then(data => {
        if (data.InfoCode === 1) {
          if (data.Data.GasStationList.length < this.pageSize) this.isFinished = true
          this.StationList.push(...data.Data.GasStationList)
        } else {
          if (data.Info === "暂无数据") {
            this.isFinished = true
          } else {
            Toast(data.Info)
            this.back()
          }
        }
        this.refreshing = false
        this.isLoading = false
      }).catch(err => {
        Toast("网络请求失败")
        this.back()
      })
    },
  }
};
</script>

<style scoped lang="scss">
.page-station-list {
  @include useScrollByBrowser(0);
  text-align: left;
}

.gas-station {
  padding: 10px 12px;
  background-color: #f5f5f5;
  min-height: calc(100vh);

  &-item {
    background-color: #fff;
    margin-bottom: 10px;
    padding: 20px 15px 20px;
    border-radius: 4px;
    @include frsb;
  }

  &-logo {
    width: 34px;
    height: 34px;
    display: inline-block;
    margin-right: 10px;
    border-radius: 50%;
    overflow: hidden;
    margin-top: -5px;
  }

  &-info {
    flex: 1;
    overflow: hidden;

    &__name {
      font-size: 16px;
      text-align: left;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    &__distance {
      font-size: 12px;
      margin-top: 10px;

      > span {
        padding: 0 5px;
        font-size: 15px;
      }
    }
  }

  .btn-gas {
    background-color: #fff;
    border: 1px solid #FF7727;
    border-radius: 12px;
    font-size: 12px;
    width: 62px;
    height: 25px;
    color: #FF7727;
    line-height: 1;
    margin-left: 24px;
    opacity: .8;

    @include bgActive(#eee);

    &:active {
      border: 1px solid #ff3920;
      color: #ff3920;
    }
  }
}
</style>
