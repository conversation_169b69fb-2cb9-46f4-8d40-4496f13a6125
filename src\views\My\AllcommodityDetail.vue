<!--
 * @Author: Yongon
 * @LastEditors: ltw <EMAIL>
 * @Description: file content
-->
<template>
  <div class="commodity">
    <nav-bar title="消费明细" />
    <!-- <div class="commodity_box" v-if="commodityDetail.oilName">
      <span class="name">{{commodityDetail.oilName}}</span>
      <span class="total" v-if="commodityDetail.oilQty">*{{commodityDetail.oilQty}}</span>
      <span class="money" v-if="commodityDetail.oilPrice">￥{{(commodityDetail.oilPrice * commodityDetail.oilQty).toFixed(2)}}</span>
    </div> -->
    <div class="commodity_box">
      <span class="name">{{commodityDetail.oilName || " "}}</span>
      <span class="total" v-if="commodityDetail.oilQty">*{{commodityDetail.oilQty}}</span>
      <span class="money" v-if="commodityDetail.oilPrice">￥{{(commodityDetail.oilPrice * commodityDetail.oilQty).toFixed(2)}}</span>
    </div>
    <div class="commodity_box" v-for="(item,index) in commodityDetail.saleItems" :key="index">
      <span class="name">{{item.gradeName || " "}}</span>
      <span class="total">*{{item.quantity}}</span>
      <span class="money">￥{{item.amount}}</span>
    </div>
  </div>
</template>
<script>
import navBar from "@/components/navBar/NavHeader";
import { storage } from 'Utils/common';
export default {
  components: { navBar},
  data () {
    return {
      recordDetail: {},
      commodityDetail: {},
    }
  },
  created() {
    this.init();
  },
  methods: {
    init () {
      this.recordDetail = JSON.parse(this.$route.query.recordDetail);
      // tradeType 1 -混合交易（油+非油）2油品交易 3非油交易,wheretype 1预约加油 2室内支付 3 不下车加油
      if(this.recordDetail?.tradeType == 2 && this.recordDetail?.whereType != 2) {
        return false 
      }
      this.$http('post',`/appcenter/trade/v1/getIndoorPayOrderInfo`,{
        apptoken: storage.ss.get('apptoken'),
        appid: 2, // 后端没使用 随意传
        orderNo: this.recordDetail?.orderNo,
      }, true, true, true).then(res => {
        if(res.infoCode == 1) {
          this.commodityDetail = res?.data || {};
        }
      }).catch(err => {})
    }
  },
}
</script>
<style lang="scss" scoped>
.commodity {
  &_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    border-radius: 3vw;
    padding: 3vw;
    margin: 4vw 2vw 0;
    span {
      display: inline-block;
      &.name {
        text-align: left;
      }
      &.total {
        margin: 0 5vw;
      }
      &.money {
        font-weight: bold;
        text-align: right;
      }
    }
    
  }
}
</style>