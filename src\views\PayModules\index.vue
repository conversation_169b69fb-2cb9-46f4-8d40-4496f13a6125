<!--
 * @Author: Ltw
 * @Date: 2022-06-21 15:44:44
 * @LastEditors: ltw <EMAIL>
 * @LastEditTime: 2024-05-10 15:02:41
 * @Description: file content
-->
<template>
  <div class="payment_code">
    <div class="payment_code_cont">
      <div class="tips">请勿泄漏您的付款码</div>
      <div class="code">
        <div class="codeDetil">
          <img id="barcode" />
          <div class="codeTips">
            <div class="left" v-if="!showPayCode">
              {{ payCode.slice(0, -4) + "****" }}
            </div>
            <div class="left" v-if="showPayCode">
              {{ payCode }}
            </div>
            <div class="right" @click="showPayCode = !showPayCode">
              {{ showPayCode ? "隐藏" : "点击查看" }}
            </div>
          </div>
          <canvas id="QRCode_header"></canvas>
          <div class="countDown">{{ count }}秒自动更新</div>
        </div>
        <!-- 获取失败 -->
        <div class="err_code" v-if="errCodeState">
          <div class="err_code_cont">
            <div class="err_code_tips">
              <img src="../../assets/images/icon/err_code.png" alt="" />
              <p>您距离网点过远,请在有效范围内使用付款码</p>
            </div>
          </div>
        <!-- 刷新 -->
        <div class="refresh_box" @click="getPayCode">
            <div class="refresh_btn">
              刷新
              <van-icon name="replay" />
            </div>
          </div>
        </div>
        <!-- Ios截图提示 -->
        <div class="screenshot_code" v-if="screenshotCodeState">
          <div class="screenshot_code_cont">
            <div class="screenshot_code_tips">
              <p>
                付款码只用于付款时展示给收银员，为防诈骗，请勿将付款码及数字截屏发送给他人
              </p>
            </div>
          </div>
          <!-- 刷新 -->
          <div class="refresh_box">
            <div class="refresh_btn">
              <van-button type="info" class="orderOil" @click="screenshotFun"
                >我知道了</van-button
              >
            </div>
          </div>
        </div>
      </div>
      <logo class="logo" />
    </div>
  </div>
</template>

<script>
import VueBarcode from "vue-barcode";
import createCodeMixins from "./mixins/mixin.js";
import logo from "@/components/logo";
import { mapState } from "vuex";
import { storage, isAndroid, compareVersions } from "Utils/common/";
import {
  GetUnPayOrderInfo,
  GetIndoorPayOrderInfo,
  GetNavigationList,
} from "./_service/";
import { isCancellation } from "Utils/isCancellation";
import { payRiskManagement } from "Utils/payRiskManagement";
import { recordJsLog } from "@/utils/recordJsLog";
import { useGetLocation } from "@/utils/useGetLocation";

export default {
  name: "paymentCode",
  mixins: [createCodeMixins],
  components: {
    barcode: VueBarcode,
    logo,
  },
  props: {},
  data() {
    return {
      barcodeValue: "https://www.baidu.com/",
      timer: null,
      count: 60,
      pollingTimes: 0, //轮循次数
      orderNo: "", //订单号
      payCodeBl: true, //生成支付码
      payCode: "", //支付码
      showPayCode: false, //隐藏部分支付码
      setTimer1: null,
      setTimer2: null,
      // GetUnPayOrderInfoData: "", //查询室内支付待支付返回数据
      errCodeState: true, //获取失败状态
      screenshotCodeState: false, //截图提示状态
      payRiskState: null, // 支付风控状态
      payOrderInfoData: null, //室内支付订单信息
    };
  },
  computed: {
    ...mapState(["rstParams", "deviceInfo"]),
  },
  created() {},
  activated() {
    console.log("activated-payMoudles");
    // this.generatePayCode();
    recordJsLog("getLocation", { route: "paymentCode" });
    this.$cppeiBridge("getLocation", { route: "paymentCode" });
  },
  mounted() {
    /**
     * @description:  室内支付sdk获取支付码（唤起sdk）成功后根据支付码生成二维码
     * @return {*}
     */
    console.log("mounted-payMoudles");
    // this.generatePayCode();
    window.onGeneratePayCode = this.onGeneratePayCode;
    window.onConfirmPayCode = this.onConfirmPayCode;
    window.onGetLocation = this.onGetLocation;
    window.onIOSScreenshot = this.onIOSScreenshot;
    window.onDeviceFinger = this.onDeviceFinger;
    setTimeout(() => {
      recordJsLog("getLocation", { route: "paymentCode" });
      this.$cppeiBridge("getLocation", { route: "paymentCode" });
      recordJsLog("routeChange", { route: "paymentCode" });
      this.$cppeiBridge("routeChange", { route: "paymentCode" });
      console.log(window.onGetLocation, window.onConfirmPayCode);
    }, 300);
  },
  methods: {
    /**
     * @description: SDK回调定位信息
     * @param {*} params
     * @return {*}
     */

    onGetLocation(params) {
      console.log(params, "params");
      useGetLocation(params, this.callBackFn);
    },
    /**
     * 设备指纹识别回调
     *
     * @param obj - 回调对象，包含错误信息和设备信息
     */
    onDeviceFinger(obj){
      recordJsLog("onDeviceFinger", obj);
      console.log('index----onDeviceFinger',obj);
      
      if (obj.error === '') {
        if (this.payRiskState === 2) {
          payRiskManagement(this.orderNo, "", 2, obj.message).then((isInterrupt) => {
            if (!isInterrupt) {
              // promise 回调isInterrupt为false，则不阻断，true阻断
              // 唤sdk输入支付密码  成功之后继续轮循查询室内支付订单信息接口  查看状态是否成功
              // 轮询不能放在轮询时需要调用函数中，否则会引起裂变反应，第一次轮询调用1次，第二次轮询会调2次，第三次会调四次，依次翻倍
              this.confirmPayCode(this.payOrderInfoData);
            }
          });
        } else if (this.payRiskState === 0) {
          payRiskManagement(this.orderNo, 0, 2, obj.message);
        } else if (this.payRiskState === 1) {
          payRiskManagement(this.orderNo, 1, 2, obj.message);
        } else{
          this.$dialog
          .alert({
            title: "提示",
            message: obj.error + "-" + obj.message,
          })
          .then(() => {
          });
        }
      }
    },
    /**
     * @description: 原生SDK设置默认卡回调
     * @param {*} res
     * @return {*}
     */

    /**
     * @description: 定位回调具体页面处理逻辑
     * @param {*} params
     * @return {*}
     */
    callBackFn(bool) {
      if (bool) {
        this.GetNavigationList();
        // }
      } else {
        // 手动打开，重新赋值默认经纬度，请求油站地图信息
        this.errCodeState = true;
        this.$toast("获取定位失败，请点击刷新重新获取!");
      }
    },
    /**
     * @description: 获取导航信息
     * @return {*}
     */
    GetNavigationList() {
      return GetNavigationList({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        distance: _const.distance,
        posx: storage.ss.get("deviceInfo")?.lon || 116.407718,
        posy: storage.ss.get("deviceInfo")?.lat || 39.920248,
        cityCode: "",
        type: "",
        tradeType: "2",
      })
        .then((res) => {
          if (res?.data?.gasStationList?.length > 0) {
            const filtergasStation = res?.data?.gasStationList;
            console.log(filtergasStation, "filtergasStation2222");
            if (filtergasStation.length > 0) {
              if (parseFloat(filtergasStation[0].distance) > 0.5) {
                console.log(filtergasStation, "filtergasStation");
                this.errCodeState = true;
                return;
              }
              this.generatePayCode();
            }
          } else {
          }
        })
        .catch((err) => {});
    },

    /**
     * @description: 刷新
     * @return {*}
     */

    getPayCode() {
      setTimeout(() => {
        recordJsLog("getLocation", { route: "paymentCode" });
        this.$cppeiBridge("getLocation", { route: "paymentCode" });
        console.log(window.onGetLocation, window.onConfirmPayCode);
      }, 300);
    },

    countdown() {
      this.timer = setInterval(() => {
        if (this.count > 0) {
          this.count = this.count - 1;
          // this.count--;
        } else {
          clearInterval(this.setTimer2);
          this.setTimer2 = null;
          clearInterval(this.timer); // 清除定时器
          this.payCodeBl = true;
          this.generatePayCode();
          this.timer = null;
        }
      }, 1000);
    },
    /**
     * @description: 查询室内支付订单信息接口
     * @return {*}
     */

    GetIndoorPayOrderInfo(bol = true) {
      GetIndoorPayOrderInfo({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        // ifPolling: bol,
        orderNo: this.orderNo,
      }).then((res) => {
        if (res.infoCode == 1) {
          //支付输入密码成功
          clearInterval(this.setTimer1);
          this.setTimer1 = null;
          this.$router.push({
            path: "/paySucs",
            query: { tradeOrderMix: JSON.stringify(res.data) },
          });
          if(compareVersions(storage.ss.get("deviceInfo").version || '', '1.1.2') >= 0) {
            this.payRiskState = 0;
            recordJsLog('deviceFinger', {});
            this.$cppeiBridge("deviceFinger", {});
            console.log(this.payRiskState,'this.payRiskState');
          } else {
            payRiskManagement(res.data.orderNo, 0, 2);
          }
        }
        //解决页面跳转到别的页面轮循还在继续的bug
        else {
          if (this.$route.path === "/paymentCode") {
            this.setTimer1 = setInterval(() => {
              if (
                this.pollingTimes <
                _const.pollingTimes.payOrderTotalTime /
                  _const.pollingParams.timeInterval
              ) {
                this.pollingTimes++;
                this.GetIndoorPayOrderInfo();
              } else {
                this.GetIndoorPayOrderInfo(false);
              }
            }, _const.pollingParams.timeInterval);
          }
        }
      });
    },
    /**
     * @description: 查询室内支付待支付订单接口
     * @param {*} bol
     * @return {*}
     */

    GetUnPayOrderInfo(bol = true) {
      GetUnPayOrderInfo({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
      }).then((res) => {
        if (res.infoCode == 1) {
          clearInterval(this.setTimer2);
          this.setTimer2 = null;
          this.orderNo = res.data.orderNo;
          if(compareVersions(storage.ss.get("deviceInfo").version || '', '1.1.2') >= 0) {
            this.payOrderInfoData = res.data;
            this.payRiskState = 2;
            recordJsLog('deviceFinger', {});
            this.$cppeiBridge("deviceFinger", {});
            console.log(this.payRiskState,'this.payRiskState');
          } else {
            payRiskManagement(res.data.orderNo, "", 2).then((isInterrupt) => {
              if (!isInterrupt) {
                // promise 回调isInterrupt为false，则不阻断，true阻断
                // 唤sdk输入支付密码  成功之后继续轮循查询室内支付订单信息接口  查看状态是否成功
                // 轮询不能放在轮询时需要调用函数中，否则会引起裂变反应，第一次轮询调用1次，第二次轮询会调2次，第三次会调四次，依次翻倍
                this.confirmPayCode(res.data);
              }
            });
          }
        }
      });
    },
    unPayOrderPolling() {
      this.setTimer2 = setInterval(() => {
        this.GetUnPayOrderInfo();
      }, _const.pollingParams.timeInterval);
    },
    confirmPayCode(item) {
      recordJsLog("confirmPayCode", {
        order: JSON.stringify({
          orderId: item.orderNo,
          amount: item.totalAmount,
          orgCode: item.orgCode,
        }),
      });

      this.$cppeiBridge("confirmPayCode", {
        order: JSON.stringify({
          orderId: item.orderNo,
          amount: item.totalAmount,
          orgCode: item.orgCode,
        }),
      });
    },
    /**
     * @description: 生成支付码
     * @return {*}
     */

    generatePayCode() {
      // isCancellation().then((result) => {
      //   // result=false时为注销状态,中断后续验证及请求
      //   if (!result.isCancel) {
      //     return;
      //   }
      // });
      recordJsLog("generatePayCode",{});

      this.$cppeiBridge("generatePayCode", {});
    },
    /**
     * @description: 生成支付码回调
     * @param {*} obj
     * @return {*}
     */

    async onGeneratePayCode(obj) {
      recordJsLog("onGeneratePayCode", obj);

      if (this.$route.path === "/paymentCode") {
        if (obj.error === 0 && this.payCodeBl) {
          //生成支付码
          this.payCodeBl = false;
          this.payCode = obj.message;
          await this.getBarCode(obj.message);
          await this.getQRCode(obj.message);
          this.count = 60;
          this.errCodeState = false;
          console.log(123123);
          // 生成二维码后 再查询室内支付待支付订单接口
          this.unPayOrderPolling();
        } else {
          // 模拟软件商店审核测试人员进行上线审核
          if (this.$setting.AuditPhone === storage.ss.get("phone")) {
            this.$dialog
              .alert({
                title: "提示",
                message: "油卡移动支付未开通，请线下开通移动支付",
              })
              .then(() => {});
          } else if (obj.error === 0 && !this.payCodeBl) {
            console.log("定位sdk多次回调，但是倒计时未结束");
          } else {
            this.$Dialog
              .alert({
                title: "支付码生成失败",
                message: obj.error + "-" + obj.message,
              })
              .then(() => {});
          }
        }
      }
    },
    /**
     * @description: 确认室内支付订单回调
     * @param {*} obj
     * @return {*}
     */

    async onConfirmPayCode(obj) {
      recordJsLog("onConfirmPayCode", obj);
      console.log("payCodeBl", this.payCodeBl);
      console.log("onConfirmPayCode", JSON.stringify(obj));
      console.log(this.timer + "----timer");
      console.log(this.setTimer1 + "----setTimer1");
      console.log(this.setTimer2 + "----setTimer2");
      // if (this.$route.path === "/paymentCode") {
      if (obj.error === 0 && !this.payCodeBl) {
        //查看支付密码输入后的状态是否成功
        this.GetIndoorPayOrderInfo();
        clearInterval(this.setTimer2);
        this.setTimer2 = null;
      } else if (obj.error === "71311001") {
        this.$dialog
          .alert({
            title: "提示",
            message:
              "由于长时间未使用，移动支付安全组件已失效，需重新注册，否则无法使用支付相关功能",
            showCancelButton: true,
            messageAlign: "left",
          })
          .then(() => {
            recordJsLog("saveInfo");

            this.$cppeiBridge("saveInfo", {});
          })
          .catch(() => {
            // on cancel
          });
      } else {
        this.$Dialog
          .alert({
            title: "支付失败",
            message: obj.error + "-" + obj.message,
          })
          .then(() => {});
      }
      // }
    },

    // 截图按钮
    screenshotFun() {
      this.screenshotCodeState = false;
    },
    // 截图桥接回调
    onIOSScreenshot(obj) {
      console.log(obj);
      // 判断环境
      if (isAndroid()) {
      } else {
        if (obj.error === 0) {
          if (!this.errCodeState) {
          this.screenshotCodeState = true;
        }
        }
      }
    },
  },

  /**
   * @description: 原生端SDK注册回调(userId会用于APP激活SDK，需监听回调onRegister)
   * @param {*} res
   * @return {*}
   */
  onRegister(res) {
    recordJsLog("onRegister", res);

    console.log("PayModules onRegister", JSON.stringify(res));
    if (res.error == 0) {
      this.$dialog
        .alert({
          title: "提示",
          message: "已注册成功！请继续操作",
        })
        .then(() => {});
    } else {
      this.$dialog
        .alert({
          title: "温馨提示",
          message:
            `<h4 style="margin:-5px 0;text-align:left">SDK注册失败,请多次尝试，如果多次尝试失败请联系相关管理人员</h4>` +
            `<p>` +
            res.error +
            "-" +
            res.message +
            `</p>`,
        })
        .then(() => {
          this.reloadLogin();
        });
    }
  },

  /**
   * @description: 重新登录
   * @return {*}
   */

  reloadLogin() {
    this.$cppeiBridge("clearToken", {});
    storage.ss.remove("apptoken");
    storage.ss.clear();
    this.$router.replace({
      path: "/loginCenter",
    });
  },

  /**
   * @description: 生命周期 destroyed销毁定时器
   * @return {*}
   */

  destroyed() {
    recordJsLog("routeChange", { route: "leave" });
    this.$cppeiBridge("routeChange", { route: "leave" });
    clearInterval(this.timer);
    this.timer = null;
    clearInterval(this.setTimer1);
    this.setTimer1 = null;
    clearInterval(this.setTimer2);
    this.setTimer2 = null;
  },
};
</script>
<style lang="scss" scoped>
.payment_code {
  width: 100%;
  background-color: hsl(34, 100%, 50%) !important;
  overflow: hidden;
  .payment_code_cont {
    width: 610px;
    margin: 0 auto;
    text-align: left;
    .tips {
      font-size: 24px;
      background: url("~@/assets/images/gasstationmap/tanhao.png") left 5px
        no-repeat;
      padding-left: 30px;
      background-size: 20px;
      color: #ffffff;
      margin: 20vw 0 40px;
    }
    .code {
      width: 100%;
      height: 770px;
      border-radius: 20px;
      background: #ffffff;
      position: relative;
      overflow: hidden;
      .codeDetil {
        width: 517px;
        margin: 10px auto;
        overflow: hidden;
        img {
          width: 100%;
          margin: 30px 0;
        }
        .codeTips {
          font-size: 24px;
          color: #666666;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        #QRCode_header {
          margin: 0 auto;
          display: flex;
          text-align: center;
        }
        .countDown {
          color: #ff9200;
          text-align: center;
        }
      }
      .code_bottom {
        width: 100%;
        height: 85px;
        line-height: 85px;
        background: #ffd9c3;
        position: absolute;
        bottom: 0;
        display: flex;
        justify-content: space-around;
        .code_num {
          color: #333333;
        }
        .code_price {
          color: #ff2244;
        }
      }
      .err_code {
        position: absolute;
        left: 0;
        top: 0;
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        background: #ffffff;
        display: flex;
        flex-direction: column;
        text-align: center;
        justify-content: center;
        padding: 0 50px;
        .err_code_tips {
          font-size: 34px;
        }
        .refresh_box {
          color: #aaa9a9;
          .refresh_btn {
            margin-top: 60px;
          }
        }
      }
      .screenshot_code {
        position: absolute;
        left: 0;
        top: 0;
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        background: #ffffff;
        display: flex;
        flex-direction: column;
        text-align: center;
        // justify-content: center;
        padding: 300px 30px 0;
        .screenshot_code_tips {
          font-size: 28px;
        }
        .refresh_box {
          .refresh_btn {
            margin-top: 150px;
          }
        }
      }
    }

    .logo {
      display: flex;
      width: 80px;
      height: 80px;
      margin: 80px auto 0;
    }
  }
}
</style>
