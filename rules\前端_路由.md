---
description: 前端路由配置规范
globs: *.ts,*.vue
alwaysApply: true
---

### router路由

#### 总体描述

本文档围绕前端路由展开，介绍了路由配置结构、路由懒加载的实现方式以及路由命名规则，为前端路由的设计和开发提供了详细的规范和示例。

#### 应用范围
本规范适用于所有使用 Vue Router 进行前端路由管理的项目，确保路由的配置、加载和命名符合规范。

#### 使用要求
开发人员在进行前端路由开发时，需要按照本规范中的路由配置结构、路由懒加载和路由命名规则进行开发。在配置路由时，要合理设置路由的路径、名称、组件和元信息。在实现路由懒加载时，要使用动态导入的方式。在命名路由时，要遵循大驼峰命名和 kebab-case 路径的规则。

#### 规则1 路由配置结构

```typescript
// /router/index.js
import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: {
      title: '首页',
      requiresAuth: false
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      title: '仪表盘',
      requiresAuth: true
    },
    children: [
      // 子路由
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior() {
    // 滚动行为
    return { top: 0 }
  }
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 路由权限控制
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next({ name: 'Login' })
  } else {
    next()
  }
})

export default router
```

#### 规则2 路由懒加载

所有路由组件都应使用动态导入实现懒加载：

```typescript
component: () => import('@/views/UserProfile.vue')
```

反例

```typescript
// /router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue';

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页',
      requiresAuth: false
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior() {
    // 滚动行为
    return { top: 0 }
  }
})

export default router
```



#### 规则3 路由命名规则

- 用大驼峰命名路由名称
- 保持路由路径使用 kebab-case

- 使用嵌套路由组织复杂页面

#### 