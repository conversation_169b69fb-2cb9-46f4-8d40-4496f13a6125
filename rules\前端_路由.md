---
description: 中油智行车队卡司机端H5前端路由配置规范
globs: *.js,*.vue
alwaysApply: true
---

# 前端路由配置规范

## 📋 总体描述

本文档定义了中油智行车队卡司机端H5项目的前端路由配置规范，基于Vue Router 3.x版本，涵盖路由配置结构、懒加载实现、命名规则、路由守卫等核心内容，确保路由系统的规范性和可维护性。

## 🎯 应用范围

- **路由配置**：所有页面路由的定义和配置
- **路由守卫**：权限控制和页面缓存管理
- **路由跳转**：页面间的导航和参数传递
- **路由缓存**：页面缓存策略和生命周期管理

## 📖 使用要求

1. **严格遵循**：所有路由配置必须符合本规范
2. **统一管理**：路由定义集中在`src/router/routes.js`文件中
3. **懒加载**：所有路由组件必须使用动态导入
4. **命名规范**：路由名称和路径遵循统一命名规则
5. **元信息**：合理配置路由元信息，支持页面标题和缓存控制

## 🏗️ 规则1：路由配置结构

### 基础路由配置

**应用范围**：所有页面路由的基础配置

**配置文件**：`src/router/index.js` 和 `src/router/routes.js`

**标准配置结构**：

```javascript
// src/router/index.js
import Vue from "vue";
import VueRouter from "vue-router";
import store from "../store/index";
import routes from "./routes";
import { isAndroid } from "Utils/common/";

Vue.use(VueRouter);

const router = new VueRouter({
  base: "/fleetcard_driver",
  mode: "history",
  routes,
});

// 解决router版本3.1.3报错问题
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject);
  return originalPush.call(this, location).catch((err) => err);
};

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 取消之前的请求
  if (window._axiosPromiseArr) {
    window._axiosPromiseArr.forEach((ele, index) => {
      ele.cancel();
      delete window._axiosPromiseArr[index];
    });
  }

  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title;
    // iOS设备标题更新hack
    if (!isAndroid()) {
      const hackIframe = document.createElement("iframe");
      hackIframe.style.display = "none";
      hackIframe.src = "/static/html/fixIosTitle.html?r=" + Math.random();
      document.body.appendChild(hackIframe);
      setTimeout(() => {
        document.body.removeChild(hackIframe);
      }, 300);
    }
  }

  // 路由缓存管理
  const { navigations } = store.state;
  let isHasIndex = navigations.findIndex((el) => el.name === to.name);

  if (isHasIndex >= 0) {
    if (isHasIndex === navigations.length - 1) {
      console.log("router 刷新");
    } else {
      // 后退操作
      store.commit("Upadate_curRouterType", { type: "back" });
      store.commit("Delete_otherRouterPath", { index: isHasIndex });
    }
  } else {
    // 前进操作
    store.commit("Upadate_curRouterType", {
      type: to.meta.isNoAni && !!to.meta.isNoAni ? "" : "forward",
    });

    let keyP = Math.random().toString(16).substring(2);
    store.commit("Update_navigations", { name: to.name, keyP });
  }

  next();
});

export default router;
```

### 路由定义结构

**配置文件**：`src/router/routes.js`

**标准路由定义**：

```javascript
// src/router/routes.js
export default [
  {
    path: "/",
    redirect: "",
    meta: {
      index: 0,
      title: "登录",
      footNav: false,
    },
  },
  {
    path: "/authority",
    name: "authority",
    component: () => import("Views/UserModules/Authority"),
    meta: {
      index: 1,
      title: "隐私权限设置",
      footNav: false,
    },
  },
  {
    path: "/choosegun",
    name: "choosegun",
    component: () => import("Views/ChooseGun/"),
    meta: {
      index: 1,
      title: "选择油枪",
    },
  },
  {
    path: "/stationList",
    name: "stationList",
    component: () => import("Views/StationList/"),
    meta: {
      index: 2,
      title: "油站列表",
    },
  }
];
```

## 🚀 规则2：路由懒加载

### 懒加载实现方式

**应用范围**：所有路由组件必须使用懒加载

**实现方法**：使用动态import语法

**正确示例**：

```javascript
// 标准懒加载写法
{
  path: "/authority",
  name: "authority",
  component: () => import("Views/UserModules/Authority"),
  meta: {
    title: "隐私权限设置",
  },
}

// 使用别名简化路径
{
  path: "/choosegun",
  name: "choosegun",
  component: () => import("Views/ChooseGun/"),
  meta: {
    title: "选择油枪",
  },
}
```

**错误示例**：

```javascript
// ❌ 错误：直接导入组件，不支持懒加载
import Authority from "Views/UserModules/Authority";

const routes = [
  {
    path: "/authority",
    name: "authority",
    component: Authority, // 错误：同步导入
    meta: {
      title: "隐私权限设置",
    },
  }
];
```

### 懒加载优势

1. **减少首屏加载时间**：只加载当前页面需要的代码
2. **提升用户体验**：页面切换更加流畅
3. **优化包体积**：代码按需分割，减少单个bundle大小
4. **提高缓存效率**：未修改的页面代码可以继续使用缓存

## 📝 规则3：路由命名规则

### 3.1 路由名称命名

**命名规则**：使用camelCase命名法

**应用范围**：所有路由的name属性

**正确示例**：

```javascript
{
  path: "/authority",
  name: "authority",           // ✅ 正确：camelCase
  component: () => import("Views/UserModules/Authority"),
},
{
  path: "/gasstationmap",
  name: "gasstationmap",       // ✅ 正确：camelCase
  component: () => import("Views/GasStationMap/"),
},
{
  path: "/chooseOrder",
  name: "chooseOrder",         // ✅ 正确：camelCase
  component: () => import("Views/chooseOrder/"),
}
```

### 3.2 路由路径命名

**命名规则**：使用kebab-case或camelCase，保持一致性

**应用范围**：所有路由的path属性

**项目实际使用**：

```javascript
// 项目中混合使用，但保持语义清晰
{
  path: "/authority",          // kebab-case风格
  name: "authority",
},
{
  path: "/choosegun",          // 小写连写
  name: "choosegun",
},
{
  path: "/gasstationmap",      // 小写连写
  name: "gasstationmap",
}
```

### 3.3 路由元信息配置

**必需字段**：

- **title**：页面标题，用于document.title设置
- **index**：页面层级，用于缓存管理
- **footNav**：是否显示底部导航（可选）

**标准配置**：

```javascript
{
  path: "/stationList",
  name: "stationList",
  component: () => import("Views/StationList/"),
  meta: {
    index: 2,                  // 页面层级
    title: "油站列表",         // 页面标题
    footNav: true,             // 显示底部导航
    isNoAni: false,            // 是否禁用动画
  },
}
```

## 🔒 规则4：路由守卫和缓存管理

### 4.1 全局前置守卫

**功能职责**：

1. **请求取消**：取消上一个页面的未完成请求
2. **标题设置**：动态设置页面标题
3. **缓存管理**：管理页面缓存和导航历史
4. **权限控制**：页面访问权限验证

### 4.2 页面缓存策略

**缓存机制**：基于Vuex状态管理的导航历史

**缓存规则**：

1. **前进**：新页面加入缓存栈
2. **后退**：清除后退页面的缓存
3. **刷新**：保持当前页面缓存
4. **重定向**：清除所有缓存

### 4.3 路由跳转最佳实践

**编程式导航**：

```javascript
// 推荐：使用路由名称跳转
this.$router.push({ name: 'stationList', params: { id: 123 } });

// 推荐：带查询参数跳转
this.$router.push({
  name: 'gasstationmap',
  query: { lat: 39.9, lng: 116.4 }
});

// 替换当前历史记录
this.$router.replace({ name: 'authority' });

// 后退
this.$router.go(-1);
```

**声明式导航**：

```vue
<template>
  <!-- 使用router-link组件 -->
  <router-link :to="{ name: 'stationList' }" class="nav-link">
    油站列表
  </router-link>

  <!-- 带参数的路由链接 -->
  <router-link
    :to="{ name: 'gasstationmap', query: { from: 'list' } }"
    class="nav-link"
  >
    地图查看
  </router-link>
</template>
```

## 🚨 常见错误和避免方法

### 错误1：路由重复定义

**错误示例**：
```javascript
// ❌ 错误：路由名称重复
[
  { path: "/home", name: "home", component: () => import("Views/Home/") },
  { path: "/main", name: "home", component: () => import("Views/Main/") }, // 名称重复
]
```

**正确做法**：
```javascript
// ✅ 正确：确保路由名称唯一
[
  { path: "/home", name: "home", component: () => import("Views/Home/") },
  { path: "/main", name: "main", component: () => import("Views/Main/") },
]
```

### 错误2：缺少路由元信息

**错误示例**：
```javascript
// ❌ 错误：缺少必要的meta信息
{
  path: "/stationList",
  name: "stationList",
  component: () => import("Views/StationList/"),
  // 缺少meta配置
}
```

**正确做法**：
```javascript
// ✅ 正确：完整的meta配置
{
  path: "/stationList",
  name: "stationList",
  component: () => import("Views/StationList/"),
  meta: {
    index: 2,
    title: "油站列表",
    footNav: true,
  },
}
```

### 错误3：路由跳转参数错误

**错误示例**：
```javascript
// ❌ 错误：使用不存在的路由名称
this.$router.push({ name: 'nonExistentRoute' });

// ❌ 错误：参数类型不匹配
this.$router.push({ name: 'stationList', params: { id: '123' } }); // 应该是数字
```

**正确做法**：
```javascript
// ✅ 正确：使用正确的路由名称和参数
this.$router.push({ name: 'stationList', query: { id: 123 } });
```