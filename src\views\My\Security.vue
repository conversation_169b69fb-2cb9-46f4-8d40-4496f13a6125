<template>
  <div class="security">
    <nav-bar title="账户安全" />
    <div class="security-pswd">
      <van-cell
        is-link
        title="修改账户密码"
        link-type="reLaunch"
        to="/my/resetPswd"
      >
      </van-cell>
      <van-cell
        is-link
        title="重置支付密码"
        link-type="reLaunch"
        to="/my/resetPayPswd"
      >
      </van-cell>
    </div>
    <!-- <div>
      <van-cell title="指纹登录">
        <van-switch v-model="checked1" :active-color="$setting.themeColor" size="15px" />
      </van-cell>
      <van-cell title="指纹支付">
        <van-switch v-model="checked2" :active-color="$setting.themeColor" size="15px" />
      </van-cell>
    </div> -->
  </div>
</template>

<script>
import navBar from "@/components/navBar/NavHeader";

export default {
  props: {},
  components: { navBar },
  data() {
    return {
      checked1: false,
      checked2: false,
    };
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {},
};
</script>

<style scoped lang="scss">
.security {
  .security-pswd {
    margin-bottom: 20px;
  }
  .van-cell {
    text-align: left;
  }
}
</style>
