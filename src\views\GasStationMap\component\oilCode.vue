<!--
 * @Author: Yongon
 * @Date: 2022-06-15 15:17:16
 * @LastEditors: yangle<PERSON> <EMAIL>
 * @LastEditTime: 2023-08-01 16:04:08
 * @Description: file content
-->
<template>
  <div class="model-oil" id="model-oil">
    <div class="countDow_box">
      <div class="countDow">下单时间:{{ fishPreOrderInfo.preAuthTime }}</div>
      <div class="countDow_notice">超过24小时未使用，订单取消，自动退款</div>
    </div>
    <div class="oiler-detailed">
      <div class="oil-pos">
        <div class="logo">
          <img src="@/assets/images/common/logo.png" alt="" />
        </div>
        <div class="oil-pos-tit">
          <p>{{ preOrderStationInfo.stationName }}</p>
          <span>距离 {{ preOrderStationInfo.distance }} 公里</span>
        </div>
        <div class="tag">
          <van-tag type="success">{{ preOrderStationInfo.status | oilStatus }}</van-tag>
        </div>
      </div>
      <div class="oil-detail-list">
        <div>
          <span>加油码：</span><span>{{ fishPreOrderInfo.preAuthCode }}</span>
        </div>
        <div>
          <span>ID：{{ fishPreOrderInfo.preAuthOrderNo }}</span>
        </div>
        <div class="tips">务必妥善保管您的加油码和验证码，切勿告知他人</div>
      </div>
      <div class="btn-list">
        <van-button type="warning" :color="$setting.themeColor" @click="baiduNav">导航</van-button>
        <van-button type="default" color="#e7e7e7" @click="CancelPreAuthAddOrder">取消</van-button>
      </div>
    </div>
  </div>
</template>

<script>
import { CancelPreAuthAddOrder } from "@/views/GasStationMap/_service";
import { storage } from "Utils/common/";
import { mapState, mapMutations } from "vuex";
import { recordJsLog } from "@/utils/recordJsLog";

export default {
  name: "",
  components: {},
  props: {
    //完成预下单信息
    fishPreOrderInfo: {
      type: Object,
      default: {},
    },
    //预下单的加油站信息
    preOrderStationInfo: {
      type: Object,
      default: {},
    },
    //
  },
  data() {
    return {};
  },
  computed: {
    ...mapState(["rstParams"]),
  },
  methods: {
    // 取消预授权加油订单
    CancelPreAuthAddOrder() {
      this.$Dialog
        .confirm({
          title: "温馨提示",
          message: "取消订单后，加油码即刻失效，预授权金额将恢复，您确认要取消吗？",
        })
        .then(() => {
          CancelPreAuthAddOrder({
            appid: 2,
            apptoken: storage.ss.get("apptoken"),
            preAuthOrderNo: this.fishPreOrderInfo.preAuthOrderNo,
            channel: this.rstParams.channel,
            cancelReason: this.rstParams.cancelReason,
          })
            .then((res) => {
              this.$emit("oilCodeCallback", res);
            })
            .catch((err) => {
              this.$Dialog.alert({ title: "温馨提示", message: err });
            });
        })
        .catch(() => {});
    },
    // 预授权加油--唤起百度地图导航
    baiduNav() {
      recordJsLog('startNavi', {
        startLon: storage.ss.get("deviceInfo").lon,
        startLat: storage.ss.get("deviceInfo").lat,
        endLon: Number(this.preOrderStationInfo.posx),
        endLat: Number(this.preOrderStationInfo.posy),
      });

      this.$cppeiBridge("startNavi", {
        startLon: storage.ss.get("deviceInfo").lon,
        startLat: storage.ss.get("deviceInfo").lat,
        endLon: Number(this.preOrderStationInfo.posx),
        endLat: Number(this.preOrderStationInfo.posy),
      });
    },
  },
  created() {},
  mounted() {},
};
</script>

<style lang="scss" scoped>
.model-oil {
  width: 690px;
  // height: 669px;
  border-radius: 20px;
  position: absolute;
  left: 0;
  right: 0;
  // bottom: 120px !important;
  margin: 5vw auto 0;
  .countDow_box {
    color: #fff;
    background: #ff9200;
    border-radius: 2vw;
    margin-bottom: 2vw;
    box-shadow: 0px 1px 3px 1px #c5c2c2;
    padding: 4vw 0;
    .countDow {
      font-size: 1.2em;
      margin-bottom: 2vw;
    }
  }

  .oiler-detailed {
    width: 100%;
    height: 569px;
    background: #ffffff;
    .oil-pos {
      width: 90%;
      margin: 0px 5% 10px;
      padding-top: 30px;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      .logo {
        img {
          border-radius: 80px;
          width: 80px;
          height: 80px;
        }
      }
      .oil-pos-tit {
        width: 45vw;
        @include oilPosTit();
        p {
          @include oilPosTitP();
        }
        span {
          font-size: 26px;
          color: #989898;
        }
      }
      .tag {
        .van-tag--success {
          color: #31c993;
          background: #d5fff0;
        }
      }
    }
    .oil-detail-list {
      width: 90%;
      margin: 30px auto 0;
      text-align: left;
      div {
        margin-bottom: 5vw;
        color: #666666;
        font-size: 32px;
        span {
          &:nth-child(2) {
            color: #151515;
          }
        }
      }
      .tips {
        font-size: 24px;
        background: #fff1db url("~@/assets/images/gasstationmap/tanhao.png") 2vw center no-repeat;
        padding-left: 50px;
        background-size: 20px;
        color: #fe8300;
        line-height: 8vw;
      }
    }
    .btn-list {
      display: flex;
      justify-content: space-around;
      button {
        width: 250px;
        font-size: 30px;
        border-radius: 2vw;
        &.van-button--default span {
          color: $sysAppColor;
        }
      }
    }
  }
}
</style>
