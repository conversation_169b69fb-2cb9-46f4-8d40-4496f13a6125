<template>
  <div>
    <!-- 切换企业顶部展示 -->
    <div class="companybox">
      <span v-if="defaultCompany">{{ defaultCompany.companyName || "" }} - {{ defaultCompany.companyCode }}</span>
      <van-button plain round size="small" @click="changeCompanyBtn" v-if="companyList.length > 0">切换企业</van-button>
    </div>

    <!-- 切换企业弹窗 -->
    <van-overlay :show="showchange" class="changeCompany_container">
      <div class="wrapper" @click.stop>
        <div class="block">
          <h3>切换企业</h3>
          <div class="box">
            <div v-for="(item, index) in companyList" :key="index">
              <van-cell border :title="item.companyName + '-' + item.companyCode" clickable
                :class="{ migrated: item.migrateFlag === 1 }" @click="handleClick(item)">
                <template #right-icon>
                  <span v-if="item.migrateFlag === 1" class="migrated-tag">已迁移</span>
                  <van-button v-if="item.isActive == 2" type="default" size="mini" round
                    @click.stop="sdkActive(item)">设置支付密码</van-button>
                </template>
              </van-cell>
            </div>
            <van-button class="hidebtn" plain round size="large" @click="showchange = false">取消</van-button>
          </div>
        </div>
      </div>
    </van-overlay>
  </div>
</template>

<script>
import { storage } from "Utils/common/";
import { mapState } from "vuex";
import filter from "@/filters/index.js";
import { getActivatedList, changeState, updateDefaultCompanyCode } from "../_service/index.js";
import { recordJsLog } from "@/utils/recordJsLog";

export default {
  props: {},
  data() {
    return {
      companyList: [],
      defaultCompany: {},
      showchange: false,
      companyCode: "",// 暂存的卡信息  方便回调回来掉接口
    };
  },
  created() { },
  activated() {
    console.log("ChangeCompany activated:" + this.isQianYi);
    if (this.isQianYi == 1) {
      return;
    }
    this.Init();
  },
  mounted() { },
  computed: {
    ...mapState(["isQianYi"]),
  },
  watch: {
    isQianYi() {
      console.log("changeCompany watch, isQianYi: " + this.isQianYi);
      if (this.isQianYi == 2) {
        this.Init();
      }
    },
  },
  methods: {
    /**
    *  
    *  @description: 切换企业
    * @param item 
    */
    handleClick(item) {
      // this.companyCode = item.companyCode;
      // this.showchange = false;
      // this.sdkActive(item);
      if (item.migrateFlag === 1) {
        this.$dialog.alert({
          message: `您选择使用企业（${item.companyName}）已迁移，请下载并登录中油暂行App，以继续使用相关服务。`
        });
      } else {
        this.choiceEnterprise(item);
      }
    },
    /**
     * @description: 设置支付密码 - sdk回调
     * @return {*}
     */

    onActivateCardsFn(params) {
      params.successCards = params.successCards ? params.successCards : [];
      params.failedCards = params.failedCards ? params.failedCards : [];
      if (params.successCards.length > 0) {
        // 调用后台设置激活状态
        this.showchange = false;
        this.changeState(params.successCards);

      } else {
        /**
        * SDK设置支付密码成功后但后台未更新激活状态意外退出，下次进到我的不会在显示该企业，
        * 但在首页切换企业会显示设置支付密码，调用SDK会提示卡片不是待激活状态(错误码确认)，该状态直接调用后台更新激活状态即可
        */
        const failedErrorList = params.failedCards.filter(item => item.error == '20000007').map(item => item.pan);
        const otherFailedCardsList = params.failedCards.filter(item => item.error != '20000007');
        if (failedErrorList && failedErrorList.length > 0) {
          this.changeState(failedErrorList);
        }

        if (otherFailedCardsList && otherFailedCardsList.length > 0) {
          this.$Dialog.alert({
            title: "设置支付密码失败",
            message: filter.errorMsg(otherFailedCardsList),
          }).then(() => { });
        }
      }
    },

    /**
    * @description: 切换企业 - 更改sdk默认电子卡号
    * @param {*} params
    * @return {*}
    */

    onSetDefaultCardFn(params) {
      console.log(params, "onSetDefaultCardFn");
      if (params.error == 0) {
        console.log("110");
        // 更改后台当前账号默认企业
        updateDefaultCompanyCode({
          appid: 2,
          apptoken: storage.ss.get("apptoken"),
          companyCode: this.companyCode,
          phone: storage.ss.get("phone"),
          type: 2, // 1企业端，2司机端
        })
          .then((res) => {
            if (res.infoCode === 1) {
              storage.ss.set("defaultCompanyCode", res.data.defaultCompanyCode);
              storage.ss.set("apptoken", res.data.apptoken);
              storage.ss.set("userId", res.data.id);
              storage.ss.set("cardNo", res.data.cardNo);
              setTimeout(() => {
                this.getActivatedList(false);
                // 切换企业成功后获取新企业对应司机的余额车牌等信息
                this.$emit("updateInfo");

                // this.$cppeiBridge("saveInfo", {
                //   token: res.data.apptoken,
                //   userId: res.data.id,
                // });
              }, 500);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      } else {
        this.companyCode = '';
        this.$Dialog.alert({
          title: "设置默认卡失败",
          message: params.error + "-" + params.message,
        });
      }
    },

    /**
     * @description: 页面初始化渲染
     * @return {*}
     */

    Init() {
      console.log("changeCompany init 初始化", Number(storage.ss.get("statusHeight") ?? 40));
      this.defaultCompany = { companyCode: storage.ss.get("defaultCompanyCode") };
      // 获取已激活企业列表
      this.getActivatedList(false);
      document.querySelector(".companybox").style.paddingTop = Number(storage.ss.get("statusHeight") ?? 40) + "px";
    },

    /**
     * @description: 点击切换企业
     * @return {*}
     */

    changeCompanyBtn() {
      this.companyList = [];
      this.getActivatedList(true);
    },

    /**
     * @description: 获取已激活企业列表
     * @param {*} isShow 切换弹窗显隐
     * @return {*}
     */

    getActivatedList(isShow) {
      getActivatedList({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        // apptoken: 'a5160020b693f90eeef26613d76bf260',
        phone: storage.ss.get("phone"),
        // phone: 18398570904,
        curPage: 1,
        pageSize: 99,
        type: 2, // 1企业端，2司机端
      })
        .then((res) => {
          if (res.infoCode == 1) {
            this.showchange = isShow;
            if (res.data.length > 0) {
              // 当前登录企业展示
              [this.defaultCompany] = res.data.filter((item) => {
                return item.companyCode == storage.ss.get("defaultCompanyCode");
              });
              console.log("companyList 当前登录企业：", JSON.stringify(this.defaultCompany));
              // 待切换企业列表
              this.companyList = res.data.filter((item) => {
                return item.companyCode != storage.ss.get("defaultCompanyCode");
              });
              console.log("companyList 待切换企业列表：", JSON.stringify(this.companyList));
              // 追加迁移判断
              if (this.defaultCompany && this.defaultCompany.migrateFlag === 1) {
                this.$dialog.alert({
                  message: `您当前使用企业（${this.defaultCompany.companyName}）已迁移，请下载并登录中油暂行App，以继续使用相关服务。`
                });
              }
            }
          } else {
            this.$Dialog.alert({
              title: "温馨提示",
              message: res.info,
            });
          }
        })
        .catch((err) => {
          this.$Dialog.alert({
            title: "温馨提示",
            message: err,
          });
        });
    },

    /**
     * @description: 设置支付密码 - sdk 唤起键盘
     * @param {*} item
     * @return {*}
     */

    sdkActive(item) {
      if (item.cardNo) {
        console.log('changeCompany 触发原生activateCards:', [item.cardNo]);
        recordJsLog('activateCards', {
          pans: [item.cardNo],
          route: 'changeCompany'
        });

        this.$cppeiBridge("activateCards", {
          pans: [item.cardNo],
          route: 'changeCompany'
        });
      } else {
        this.$Dialog.alert({
          title: "温馨提示",
          message: "无卡号,无法激活",
        });
      }
    },

    /**
     * @description: 设置支付密码成功回调后 - 通知后台更改对应企业状态
     * @param {*} params
     * @return {*}
     */

    changeState(params) {
      changeState({
        phone: storage.ss.get("phone"),
        companyCodeList: [],
        cardNoList: params,
        type: 2, // 1企业端，2司机端
      })
        .then((res) => {
          if (res.infoCode == 1) {
            this.$toast("设置支付密码成功");
            this.getActivatedList(false);
          }
        })
        .catch((err) => {
          Toast(err);
        });
    },

    /**
     * @description: 选择企业切换
     * @param {*} item
     * @return {*}
     */

    choiceEnterprise(item) {
      console.log('changeCompany 切换企业：', JSON.stringify(item));
      if (item.isActive == 2) {
        this.$Dialog.alert({
          title: "温馨提示",
          message: "请设置支付密码后再切换企业",
        });
        return;
      }
      this.companyCode = item.companyCode;
      // 调用sdk设置默认卡
      recordJsLog('setDefaultCard', {
        pan: item.cardNo,
        route: 'changeCompany'
      });

      this.$cppeiBridge("setDefaultCard", {
        pan: item.cardNo,
        route: 'changeCompany'
      });
      console.log('changeCompany setDefaultCard');


    }
  },
};
</script>

<style lang="scss" scoped>
.companybox {
  width: 100vw;
  height: 89px;
  line-height: 89px;
  background: $sysAppColor;
  color: #fff;
  position: absolute;
  top: 0;
  z-index: 1;
  text-align: left;

  >span {
    margin-left: 4vw;
    display: inline-block;
    width: 70%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
  }

  .van-button {
    padding: 0px 4vw;
    height: 26px;
    float: right;
    color: white;
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.3);
    margin-right: 4vw;
    margin-top: 2.6vw;
  }
}

.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  position: relative;
  width: 80vw;
  height: 100vw;
  background-color: #fff;
  border-radius: 3vw;

  /deep/ .van-cell__title {
    text-align: left;
  }

  .box {
    height: 70vw !important;
    overflow-y: auto;
    @include useScrollByBrowser(0);

    /deep/ .van-button {
      padding: 0 3vw;
    }

    /deep/ .van-cell--clickable {
      width: 90%;
      border-bottom: 1px solid #e5e0e0;
      padding: 10px 0px;
      margin: 0 auto;
    }
  }

  .hidebtn {
    position: absolute;
    left: 5vw;
    bottom: 4vw;
    width: 90%;
    height: 9vw;
  }
}

.migrated {
  color: #969799;
}

.migrated-tag {
  color: #969799;
  font-size: 14px;
}

::v-deep .migrated-tag {
  font-size: 14px !important;
}
</style>
