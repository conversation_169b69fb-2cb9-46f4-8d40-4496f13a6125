<template>
  <div class="oilDetil" id="oilDetil">
    <van-number-keyboard v-model="amountMoeny" :show="showkeyboard" :maxlength="6" @blur="showkeyboard = false" close-button-text="完成" theme="custom" extra-key="." @close="showkeyboard = false" :style="{ bottom: bottomHeight + 'px' }">
      <template #title-left>
        <van-form validate-first>
          <van-field
            readonly
            clickable
            :value="amountMoeny"
            :rules="[
              {
                required: true,
                validator: () => amountMoeny >= 50,
                message: '最小加油50元',
                trigger: 'onChange',
              },
            ]"
          />
        </van-form>
        <div class="yuan" type="text">元</div>
      </template>
    </van-number-keyboard>
    <div class="oil-pos">
      <div class="logo">
        <img src="@/assets/images/common/logo.png" alt="" />
      </div>
      <div class="oil-pos-tit">
        <p>{{ currentOilStation.stationName }}</p>
        <span>距离 {{ currentOilStation.distance }} 公里</span>
      </div>
      <div class="tag">
        <van-button
          plain
          round
          type="warning"
          size="small"
          @click="
            $router.push({
              path: '/gaslist',
              query: { list: JSON.stringify(oilStationList) },
            })
          "
          >更换加油站</van-button
        >
        <!-- <van-tag type="success">标签</van-tag> -->
      </div>
    </div>
    <div class="oil-tips-cont">
      <div class="tit-nav">
        <div class="tit">油品号</div>
      </div>
      <div class="oil-list">
        <div v-for="(item, index) in oilType" :key="index" @click="oilTypeNum = index" :class="index == oilTypeNum ? 'activedOilType' : ''">
          {{ item.oilNametxt }}
          <!-- OilNametxt OilName -->
        </div>
      </div>
    </div>
    <div class="oil-tips-cont" v-show="amountMoeny < 50">
      <div class="tit-nav">
        <div class="tit oilAmountTit">加油金额（元）</div>
        <div class="tit oilAmountTit" @click="onShowkeyboard">更多金额 ></div>
      </div>
      <div class="oil-list">
        <div v-for="(item, index) in oilAmount" :key="index" @click="oilAmountNum = index" :class="index == oilAmountNum ? 'activedOilType' : ''">
          {{ item }}
        </div>
      </div>
    </div>
    <van-field readonly class="more_amount" v-model="amountMoeny" v-show="amountMoeny >= 50" @click="showkeyboard = !showkeyboard" label="更多金额" />
    <div class="oil-tips-cont oil-tips-cont-carno">
      <div class="tit-nav">
        <div class="tit balanceTit" v-if="cardInfo.carno">车牌号:{{ cardInfo.carno }}</div>
        <div class="tit balanceTit" v-else @click="$router.push({ path: '/my/companyDetail' })">
          车牌号:请维护车牌信息
        </div>
        <!-- <div class="tit oilAmountTit" @click="showLicense = true">更换 ></div> -->
      </div>
      <div class="oil-list">
        <div class="balance" v-if="cardInfo.moneyType == 0">
          当前卡余额：<span>¥ {{ cardInfo.balance }}</span>
        </div>
        <div class="balance" v-if="cardInfo.moneyType == 1">
          共享账户余额：<span>¥ {{ cardInfo.shareBalance }}</span>
        </div>
      </div>
    </div>
    <van-button class="orderOil" type="default" :disabled="isDisabled" @click="PreAuthAddOrderBtn"> {{ count == 0 ? "预授权加油" : count + "s" }}</van-button>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import { storage, compareVersions } from "Utils/common/";
import { PreAuthAddOrder } from "@/views/GasStationMap/_service/";
import { isCancellation } from "Utils/isCancellation";
import { payRiskManagement } from "Utils/payRiskManagement";
import { recordJsLog } from "@/utils/recordJsLog";

//更改倒计时时间
const TIME_COUNT = 29;
export default {
  name: "",
  components: {},
  props: {
    /* 父组件获取加油卡信息 */
    cardInfo: {
      type: Object,
      default: {},
    },
    //价格
    amountMoeny: {
      type: String,
      default: "",
    },
    //油品信息
    oilType: {
      type: Array,
      default: [],
    },
    //加油信息
    currentOilStation: {},
    //下标
    oilTypeNum: 0,
    oilStationList: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      showkeyboard: false, //显示键盘与否

      oilAmount: [300, 500, 1000, 1500],
      oilAmountNum: 0,
      bottomHeight: 60, // 首页的加油与底部的安全距离
      isDisabled: false,
      timer: null, // 定时器
      count: 0, //倒计时秒
    };
  },
  watch: {},
  computed: {
    ...mapState(["rstParams"]),
  },
  created() {},
  mounted() {},
  methods: {
    /**
     * @description: 唤起数字键盘
     * @return {*}
     */

    onShowkeyboard() {
      this.showkeyboard = !this.showkeyboard;
    },

    /**
     * @description: 预授权下单
     * @return {*}
     */

    PreAuthAddOrderBtn() {
      // isCancellation()
      // .then((result) => {
      // result=false时为注销状态,中断后续验证及请求
      // if (!result.isCancel || result.errorApi) {
      //   return;
      // }
      if (!this.rstParams.carNo) {
        this.$Dialog
          .confirm({
            title: "温馨提示",
            message: "您还没有车牌号,请添加车牌号",
          })
          .then(() => {
            this.$router.push({ path: "/my/companyDetail" });
          })
          .catch(() => {});
        return;
      }
      let preAuthAmount = this.amountMoeny >= 50 ? this.amountMoeny : this.oilAmount[this.oilAmountNum];
      if ((this.cardInfo.moneyType == 0 && preAuthAmount > this.cardInfo.balance) || (this.cardInfo.moneyType == 1 && preAuthAmount > this.cardInfo.shareBalance)) {
        this.$Dialog.alert({ title: "温馨提示", message: "余额不足" }).then(() => {});
        return;
      }
      if (preAuthAmount * 1 > 5000) {
        this.$Dialog.alert({ title: "温馨提示", message: "预约加油金额必须小于5000" }).then(() => {});
        return false;
      }
      PreAuthAddOrder({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        stationCode: this.currentOilStation.oilStationCode,
        // stationCode: "BC02" || this.currentOilStation.oilStationCode,
        oilCode: this.oilType[this.oilTypeNum].oilCode,
        oilName: this.oilType[this.oilTypeNum].oilName,
        preAuthMode: this.rstParams.preAuthMode,
        preAuthAmount: this.amountMoeny >= 50 ? this.amountMoeny : this.oilAmount[this.oilAmountNum],
        preAuthType: this.rstParams.preAuthType,
        channel: this.rstParams.channel,
        carNo: this.rstParams.carNo,
      })
        .then((res) => {
          console.log(3);
          console.log(res);
          this.preOrderInfo = res.data;
          if (res.infoCode == 1) {
            //预下单成功 唤起sdk支付
            this.$toast("唤起支付");
            this.confirmPreTrade();
          } else {
            this.$Dialog.alert({ title: "温馨提示", message: res.info }).then(() => {});
          }
        })
        .catch((err) => {
          this.$toast("预授权加油失败");
          this.$Dialog.alert({ title: "温馨提示", message: err }).then(() => {});
        });
      // })
      // .catch((err) => {});
    },

    /**
     * @description: 预授权加油下单唤起sdk
     * @return {*}
     */

    confirmPreTrade() {
      let order = {
        outOrderNo: this.preOrderInfo.preAuthOrderNo, //订单号
        amount: this.preOrderInfo.preAuthAmount, //预授权金额
        oilCode: this.preOrderInfo.oilCode, //油品编码
        volumn: this.preOrderInfo.volumn, //  油量 可为空
        orgCode: this.preOrderInfo.stationCode, //即 加油站编码
      };
      //! add by kj - 激活卡片后禁用button，SDK回调后消失（2022.1.30）
      // this.isDisabled = true;
      // setTimeout(() => {
      //   this.isDisabled = false;
      // }, 30 * 1000);
      // 倒计时30秒
      this.countdown();
      if(compareVersions(storage.ss.get("deviceInfo").version || '', '1.1.2') >= 0) {
        this.$emit("oilSelectFunction", order);
      } else {
        payRiskManagement(this.preOrderInfo.preAuthOrderNo, "", 1).then((res) => {
          if (!res) {
            console.log(
              'oilSelect confirmPreTrade Params ====> ',
              JSON.stringify({
                pan: '123123',
                order: JSON.stringify(order)
              })
            );
            recordJsLog('confirmPreTrade', {
              pan: "123123",
              order: JSON.stringify(order),
            });

            // promise 回调人res为false，则不阻断，true阻断
            this.$cppeiBridge("confirmPreTrade", {
              pan: "123123",
              order: JSON.stringify(order),
            });
          }
        });
      }
    },

    /**
     * @description: 倒计时60秒
     * @return {*}
     */

    countdown() {
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.isDisabled = true;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.isDisabled = false;
            clearInterval(this.timer); // 清除定时器
            this.timer = null;
          }
        }, 1000);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.oilDetil {
  width: 700px;
  padding: 0 25px;
  height: 106vw;
  position: absolute;
  // bottom: 80px;
  background: #ffffff;
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
  /deep/ .van-cell {
    width: 100vw;
    border-top: 1px solid;
    border-bottom: 1px solid;
    border-color: #e5e5e6;
  }
  .yuan {
    position: relative;
    bottom: 60px;
    margin-left: 80vw;
  }
  .oil-pos {
    margin-top: calc(777px * 0.034);
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    align-items: center;
    .logo {
      img {
        border-radius: 80px;
        background: #ececec;
        padding: 26.3px;
        width: 53.7px;
        height: 53.7px;
      }
    }
    .oil-pos-tit {
      width: 45vw;
      @include oilPosTit();
      p {
        @include oilPosTitP();
      }
      span {
        font-size: 26px;
      }
    }
  }
  .more_amount {
    border: 1px solid #e5e5e6 !important;
    width: 90vw;
  }
  .oil-tips-cont {
    margin-top: 15px;
    margin-bottom: 35px;
    &:nth-child(4) {
      margin-bottom: 20px !important;
    }
    overflow: hidden;
    height: auto;
    .tit-nav {
      display: flex;
      justify-content: space-between;
      .tit {
        padding-left: 10px;
        width: auto;
        font-size: 28px;
        color: #666666;
        align-self: center;
      }
      .oilAmountTit {
        text-align: right;
      }
      .balanceTit {
        color: #000000;
        font-size: 32px;
      }
    }
    .oil-list {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: wrap;
      // margin-top: 25px;
      div {
        width: 21.8vw;
        height: 9.067vw;
        margin: 0.667vw;
        line-height: 9.067vw;
        background: url("~@/assets/images/gasstationmap/youpinB.png");
        background-size: 100% 100%;
        // @include pos();
        // text-align: center;
        // display: flex;
        // justify-content: center;
      }
      .activedOilType {
        // background: url("~@/assets/images/gasstationmap/youpinA.png");
        background: url("~@/assets/images/winter/btn.png");
        background-size: 100% 100%;
      }
      .balance {
        background: none;
        width: 400px;
        text-align: left;
        span {
          color: red;
        }
      }
    }
  }
  .oil-tips-cont-carno {
    margin-bottom: 2vw;
  }
  .orderOil {
    width: 700px !important;
    height: 80px !important;
    background: #ff9200;
    border-radius: 10px;
    color: #ffffff;
    letter-spacing: 2px;
  }
}
</style>