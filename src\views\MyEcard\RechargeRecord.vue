<template>
  <div class="recharge-record">
    <nav-bar title="充值记录" />
    <div class="tips">
      查询结果只显示本卡三个月内记录,如需更多查询记录,请xxxxxxxxxxxxxxxxxxxxxx
    </div>
    <van-cell-group>
      <div class="test" v-for="i in 6" :key="i">
        <div class="test-1">
          <span>订单号: 1200011111</span>
          <span id="money">￥ 222.22</span>
        </div>
        <div class="test-2">
          <span>2021-05-10 13:40:11</span>
          <van-button color="#C47742" round plain size="mini"
            >查看发票</van-button
          >
        </div>
      </div>
    </van-cell-group>
  </div>
</template>

<script>
import navBar from "@/components/navBar/NavHeader";

export default {
  props: {},
  components: { navBar },
  data() {
    return {
      activedIndex: 0,
      list: Array(5),
    };
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {},
};
</script>

<style scoped lang="scss">
.recharge-record {
  .tips {
    padding: 20px;
    text-align: left;
    margin-bottom: 30px;
    background-color: rgba(255, 255, 255, 100%) !important;
  }
  /deep/ .van-cell-group {
    background-color: rgba(239, 239, 239, 100%) !important;
    .test {
      height: auto;
      margin-bottom: 2px;
      padding: 10px;
      background-color: #fff;
      .test-1,
      .test-2 {
        height: 25px;
        display: flex;
        justify-content: space-between;
        #money {
          color: #c70d0a;
        }
      }
    }
  }
}
</style>