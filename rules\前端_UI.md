---
description: 前端UI设计规范与组件使用指南
globs: *.ts,*.vue,*.tsx,*.jsx,*.less
alwaysApply: true
---

### UI规则

#### 使用要求
开发前端UI时需严格遵循本规范，确保一致性和可维护性。

### 规则1 布局规范

- **整体布局结构**

  比如：左侧导航 + 主内容区

- **页面布局**

  比如：**标题区域**: 页面标题 + 装饰性色块

- **响应式设计**

  比如：使用Naive UI的Grid系统

### 规则2 色彩规范

- **主色调**

  ```less
  --primary-color: #4355ff;   
  ```

- **中性色**

  ```less
  --text-primary: #202020;         
  --text-secondary: #333333;       
  ```

### 规则3 字体规范

- **字体大小**

  ```less
  // 标题字体
  --font-size-h1: 24px;          
  ```

- **字体权重**

  ```less
  --font-weight-normal: 400;   
  ```

- **行高规范**

  ```less
  --line-height-base: 1.5; 
  ```

### 规则4 组件规范

- **基础组件尺寸**

  ```less
  // 圆角
  const borderRadius = {
    small: '2px',
    medium: '4px',     // 默认圆角
    large: '8px'
  };
  ```

- **Native UI组件定制**

  - Button: 统一内边距和字体大小
  - Input: 统一高度规范

### 规则5 间距规范

- **基础间距单位**

  ```less
  --space-xs: 4px;    
  --space-sm: 8px;   
  ```

- **页面间距规则**

  - **模块间距**: 16px (查询区域与表格区域)
  - **卡片内边距**: 24px

### 规则6 表单规范

- **表单布局**

  ```less
  // 查询表单规范
  interface QueryFormRules {
    layout: 'horizontal';         
      ...
  }
  ```

- **表单控件**

  - **输入框**: 高度32px，圆角4px
  - **选择器**: 与输入框保持一致的尺寸

- **表单验证**

  - 实时验证，错误提示明确
  - 必填项标红星号标识

### 规则7 表格规范

- **表格基础样式**

  ```less
  interface TableStandards {
    fontSize: '14px';            
    headerBackground: '#fafafa';            
         ...     
  }
  ```

- **表格功能规范**

  - **分页**: 默认每页10条，支持10/20/50/100条切换
  - **排序**: 支持列排序，显示排序图标

- **表格状态**

  - **加载状态**: 显示Loading效果
  - **空数据**: 显示"暂无数据"提示

### 规则8 按钮规范

- **按钮类型和用途**

  ```typescript
  interface ButtonTypes {
    primary: '主要操作按钮';        
    text: '文本按钮';  
      ...
  }
  ```

- **按钮尺寸**

  - **小按钮**: 高度28px，用于表格操作

- **按钮状态**

  - **常规态**: 默认样式
  - **悬停态**: 背景色加深

### 规则9 图标规范

- **图标库**
  - 主要使用 `@vicons/ionicons5`
  - 自定义图标存放在 `xxx/icons/`
- **图标使用原则**
  - 保持图标风格一致
  - 合理使用图标语义

### 规则10 交互规范

- **反馈机制**

  比如**成功操作**:

- **加载状态**

  比如**页面加载**

- **动画效果**

  比如**页面切换**