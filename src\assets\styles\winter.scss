// 冬奥会样式相关
#app.winter {
  .login-code .switch-btn {color: $sysAppColor;}
  .login-code .loginfooter .text {color: $sysAppColor;}
  .login-code .login-card .forgot-pwd {color: $sysAppColor;}
  .login-code .login-card .btn-wrap .van-button--disabled {
    background: #9cd4f5!important;
    border-color: #9cd4f5!important;
  }
  .login-code .header {height: 70vw;}
  .nav-header .content {background: $sysAppColor;}
  .login-code .van-button--warning {
    color: $sysAppColor;
    border-color: $sysAppColor;
  }
  .safe_notice .van-button {background: $sysAppColor;}
  .container .oilDetil .orderOil {background: linear-gradient(to bottom, #068cd6 , #0075ba);}
  .oilDetil .van-button--warning {
    color: $sysAppColor;
    border-color: $sysAppColor;
  }
  .inactive-text {color: $sysAppColor;}
  .order_pay_result .orderDetail .van-button--large {
    background: $sysAppColor;
    border-color: $sysAppColor;
  }
  .order_pay_result .orderDetail .van-cell.discount .van-cell__value {color: $sysAppColor;}
  .all-consuming-records .tips {
    color: $sysAppColor;
    background: #bce4fb;
  }
  .consuming-records {
    color: $sysAppColor;
    background: #bce4fb;
  }
  .apply_money .card_balance {color: $sysAppColor;}
  .apply_money .van-button--primary {
    background: $sysAppColor;
    border-color: $sysAppColor;
  }
  .open-new-card .van-button {
    background: $sysAppColor !important;
    border-color: $sysAppColor;
  }
  .close-account .van-cell-group .active {background: $sysAppColor;}
  .company-list p.actived {background: $sysAppColor;}
  .btnBc,.actived{
    background:$sysAppColor !important;
    border-color: $sysAppColor;
  }
  .staion_order{
    .order-box .activedOrder {
      border-color: $sysAppColor;
      color: $sysAppColor;
      .van-cell__label {color: $sysAppColor;}
      .van-cell__value {color: $sysAppColor;}
    }
    .pay-box .activedPay{
      .van-icon {color: $sysAppColor;}
    }
  } 
  .choose_card .actived {background: $sysAppColor;}
  .payment_code {background: $sysAppColor !important;}
  .van-tag--warning.van-tag--plain {color: $sysAppColor;}
  .container .model-oil .countDow_box {background: #057fc6;}
  .orderDetail .discount .van-cell__value { color: $sysAppColor; }
  .block .tit { color: $sysAppColor; }
  .oilList .list .list_go_detil { color: $sysAppColor; }
  .oilList .list .list_btn .van-button--warning {
    background: $sysAppColor;
    border-color: $sysAppColor;
  }
}