<!--
 * @Author: <PERSON><PERSON>
 * @Date: 2022-04-06 13:43:28
 * @LastEditors: Yongon
 * @LastEditTime: 2022-06-23 09:01:30
 * @Description: Loading组件
-->
<template>
  <div class="mask_loading" v-show="show" v-cloak>
    <div class="loading_container">
      <van-loading type="spinner" size="36px" />
      <p class="msg">{{ msg }}</p>
    </div>
  </div>
</template>

<script>
  export default {
    name: "loading",
    data() {
      return {
        show: false,
        msg: '',
        time: 10000
      }
    },
    props: {},
    computed() {},
    create() {

    },
    mounted() {
      // 设置延时关闭
      let _this = this
      this.$nextTick(() => {
        setTimeout(() => {
          _this.show = false
        }, _this.time)
      })
    },
    methods: {}
  }
</script>
<style scoped lang="scss">
  .mask_loading {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    top: 0;
    z-index: 99;
    text-align: center;
    .loading_container {
      box-sizing: border-box;
      padding-top: 60px;
      width: 260px;
      height: 240px;
      border-radius: 20px;
      background-color: rgba(0, 0, 0, .5);
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 100% auto;

      .msg {
        width: 100%;
        line-height: 50px;
        font-size: 24px;
        color: #fff;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

    }
  }
</style>