---
description: 前端状态管理规范
globs: *.ts,*.vue
alwaysApply: true
---

### 状态管理

#### 总体描述

本文档主要介绍前端项目中状态管理的相关内容，包括 Store 结构和使用规范，通过示例代码展示如何使用 Pinia 进行状态管理，为开发者提供状态管理的具体实现参考。

#### 应用范围
本规范适用于所有使用 Pinia 进行状态管理的前端项目，帮助开发人员正确地设计和使用状态管理 Store。

#### 使用要求
开发人员在进行前端状态管理开发时，需要按照本规范中的 Store 结构和使用规范进行开发。在定义 Store 时，要合理划分状态、获取器和动作，并确保代码的可读性和可维护性。在使用 Store 时，要遵循规范中的访问和调用方式。

#### 规则1 Store结构

代码参考如下：

```typescript
// stores/user.js
import { defineStore } from 'pinia'
import { ref, computed, reactive } from 'vue'
import { fetchUserInfo } from '@/api/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref(null)
  const isAuthenticated = ref(false)
  const preferences = reactive({ theme: 'light' })

  // 获取器
  const userName = computed(() => user.value?.name || 'Guest')

  // 动作
  const login = async (credentials) => {
    try {
      const response = await fetchUserInfo(credentials)
      user.value = response.data
      isAuthenticated.value = true
      return true
    } catch (error) {
      console.error('Login failed', error)
      return false
    }
  }

  const logout = () => {
    user.value = null
    isAuthenticated.value = false
  }

  return {
    user,
    isAuthenticated,
    preferences,
    userName,
    login,
    logout
  }
})
```

#### 规则2 Store使用规范

代码参考如下：

```typescript
<script setup>
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 访问状态
console.log(userStore.user)

// 调用动作
const handleLogin = async () => {
  const success = await userStore.login({
    username: 'test',
    password: '123456'
  })
  
  if (success) {
    // 登录成功
  }
}
</script>
```

反例

```typescript
// 违反Store结构规范，未正确划分状态、获取器和动作
import { defineStore } from 'pinia';

export const useWrongUserStore = defineStore('wrong-user', () => {
  const user = { name: 'John Doe' };

  const login = () => {
    console.log('Login');
  };

  return {
    user,
    login
  };
});

// 违反Store使用规范，未正确调用动作
<script setup>
import { useWrongUserStore } from '@/stores/wrong-user';

const wrongUserStore = useWrongUserStore();

// 错误的访问方式
console.log(wrongUserStore.user.name);

// 错误的调用方式
wrongUserStore.login();
</script>
```
