---
description: 中油智行车队卡司机端H5前端状态管理规范
globs: *.js,*.vue
alwaysApply: true
---

# 前端状态管理规范

## 📋 总体描述

本文档定义了中油智行车队卡司机端H5项目的前端状态管理规范，基于Vuex 3.x版本，涵盖状态管理架构、状态设计、数据流管理等核心内容，确保应用状态的统一管理和可维护性。

## 🎯 应用范围

- **全局状态**：跨组件共享的应用状态
- **路由缓存**：页面导航历史和缓存管理
- **用户信息**：用户登录状态和个人信息
- **业务数据**：需要持久化或共享的业务数据

## 📖 使用要求

1. **集中管理**：所有全局状态必须通过Vuex管理
2. **单一数据源**：每个状态只有一个可信数据源
3. **不可变性**：只能通过mutations修改状态
4. **异步操作**：所有异步操作必须在actions中处理
5. **模块化**：复杂状态可以考虑模块化管理

## 🏗️ 规则1：Vuex Store结构

### 基础Store配置

**应用范围**：整个应用的状态管理

**配置文件**：`src/store/index.js`

**标准Store结构**：

```javascript
// src/store/index.js
import Vue from "vue";
import Vuex from "vuex";

Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    // 路由导航历史
    navigations: [],

    // 当前路由类型 (forward/back)
    curRouterType: "",

    // 用户信息
    userInfo: {
      id: null,
      name: '',
      phone: '',
      isLoggedIn: false
    },

    // 应用配置
    appConfig: {
      theme: 'light',
      version: '1.0.0'
    },

    // 业务数据
    stationList: [],
    currentStation: null,
    orderInfo: null
  },

  mutations: {
    // 更新导航历史
    Update_navigations(state, payload) {
      state.navigations.push(payload);
    },

    // 删除其他路由路径
    Delete_otherRouterPath(state, payload) {
      state.navigations.splice(payload.index + 1);
    },

    // 更新当前路由类型
    Upadate_curRouterType(state, payload) {
      state.curRouterType = payload.type;
    },

    // 设置用户信息
    SET_USER_INFO(state, userInfo) {
      state.userInfo = { ...state.userInfo, ...userInfo };
    },

    // 清除用户信息
    CLEAR_USER_INFO(state) {
      state.userInfo = {
        id: null,
        name: '',
        phone: '',
        isLoggedIn: false
      };
    }
  },

  actions: {
    // 用户登录
    async login({ commit }, credentials) {
      try {
        const response = await this.$http.post('/Api/User/Login', credentials);
        if (response.data.Value) {
          const userInfo = {
            ...response.data.Data,
            isLoggedIn: true
          };
          commit('SET_USER_INFO', userInfo);
          return response.data;
        }
        throw new Error(response.data.Info);
      } catch (error) {
        console.error('登录失败:', error);
        throw error;
      }
    },

    // 用户登出
    logout({ commit }) {
      commit('CLEAR_USER_INFO');
      localStorage.removeItem('token');
    }
  },

  getters: {
    // 是否已登录
    isLoggedIn: state => state.userInfo.isLoggedIn,

    // 用户名称
    userName: state => state.userInfo.name,

    // 当前路由类型
    routerType: state => state.curRouterType
  }
});
```

## 📖 规则2：Vuex使用规范

### 2.1 在组件中访问状态

**应用范围**：所有Vue组件中的状态访问

**正确示例**：

```javascript
// 方式1：直接访问
export default {
  computed: {
    userInfo() {
      return this.$store.state.userInfo;
    },

    isLoggedIn() {
      return this.$store.getters.isLoggedIn;
    }
  }
};

// 方式2：使用辅助函数
import { mapState, mapGetters } from 'vuex';

export default {
  computed: {
    ...mapState(['userInfo', 'curRouterType']),
    ...mapGetters(['isLoggedIn', 'userName'])
  }
};
```

### 2.2 提交mutations和分发actions

**正确示例**：

```javascript
import { mapMutations, mapActions } from 'vuex';

export default {
  methods: {
    ...mapMutations(['SET_USER_INFO', 'CLEAR_USER_INFO']),
    ...mapActions(['login', 'logout']),

    // 处理登录
    async handleLogin() {
      try {
        await this.login({
          username: this.username,
          password: this.password
        });
        this.$toast.success('登录成功');
      } catch (error) {
        this.$toast.fail('登录失败');
      }
    }
  }
};
```

## 🚨 常见错误和避免方法

### 错误1：直接修改状态

**错误示例**：
```javascript
// ❌ 错误：直接修改state
this.$store.state.userInfo.name = 'newName';
```

**正确做法**：
```javascript
// ✅ 正确：通过mutation修改
this.$store.commit('SET_USER_INFO', { name: 'newName' });
```

### 错误2：在mutation中执行异步操作

**错误示例**：
```javascript
// ❌ 错误：在mutation中执行异步操作
mutations: {
  async SET_USER_INFO(state, userInfo) {
    const response = await api.getUserInfo();
    state.userInfo = response.data;
  }
}
```

**正确做法**：
```javascript
// ✅ 正确：在action中执行异步操作
actions: {
  async fetchUserInfo({ commit }) {
    const response = await api.getUserInfo();
    commit('SET_USER_INFO', response.data);
  }
}
```
