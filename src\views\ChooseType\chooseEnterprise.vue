<template>
  <div class="chooseEnterprise">
    <div class="choose_card">
      <p>请你选择至少一个企业激活,激活完成后,其他未激活企业可在'我的'功能里面再次进行激活</p>
      <van-cell-group>
        <van-cell v-for="(item, index) in list" :key="index" @click="checkListIndex(item)" :title="cellTitle(item)" :class="checkList.includes(item) ? 'actived' : ''"/>
      </van-cell-group>
      <van-button :disabled="isDisabled" type="warning" class="btn btnBc btnFixed" size="large" @click="openCards">
        {{ count == 0 ? "激活" : count + "s" }}
      </van-button>
    </div>

    <!-- 迁移验证码 -->
    <transfer ref="transferChooseEnterpriseRef"/>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import { storage } from "Utils/common/";
import { Toast } from "vant";
import filter from "@/filters/index.js";
import transfer from "@/components/transfer/index";
import {
  getUnActivatedList,
  getOpenCardList,
  doOpenCards,
  changeState,
  updateDefaultCompanyCode,
  getCompanyInfoByCardNo,
} from "./_service/";
import { isPhoneExist } from "../UserModules/_service/";
import { recordJsLog } from "@/utils/recordJsLog";

//更改倒计时时间
const TIME_COUNT = 59;
export default {
  name: "chooseEnterprise",
  components: {
    transfer,
  },
  props: {},
  data() {
    return {
      isActive: 1, // 1已激活   2已开卡，未激活
      list: [],
      checkList: [],
      activeNum: 0, // 激活次数,2次激活后仍然不成功,跳到我的界面
      setSdkNum: 0, // 设置默认卡次数
      isDisabled: false,

      // 电子卡卡号数组
      cardNos: [],
      activeCardNos: [], // 已激活未设置密码的卡号集合
      timer: null, // 定时器
      count: 0, //倒计时秒
      activeCardNoList: [], // 激活选择的卡片
      currentasnResult: false, // 判断当前是否激活
      isTransfer: false,
    };
  },
  watch: {
    isQianYi() {
      console.log("chooseEnteronSetDefaultCardprise watch, isQianYi: " + this.isQianYi);
      // if(this.isQianYi == 2 && this.isTransfer){
      // this.activateCards()
      // }
    },
  },
  created() {
    // isActive = 2 注册未激活跳转到激活页面设置默认卡
    // 区分跳转过来的场景 isActive  1已激活   2已开卡，未激活
    this.isActive = this.$route.query.isActive;
    console.log("chooseEnterprise created isActive:", this.isActive);
    this.isPhoneExist();
    if (this.$route.query.isActive == 1) {
      this.loadUnActivatedList();
    } else {
      this.loadOpenCardList();
    }
  },
  mounted() {
    window.onActivateCards = this.onActivateCards;
    window.onSetDefaultCard = this.onSetDefaultCard;
    window.onRegister = this.onRegister;
    window.onHasCards2Reapply = this.onHasCards2Reapply; //迁移
  },
  computed: {
    // 列表公司名字处理
    cellTitle() {
      return function(item) {
        var title = item.companyName + " - " + item.companyCode + " - " + item.name;
        // 过长显示...
        return title.length > 30 ? title.slice(0, 30) + "..." : title;
      };
    },
    companyCodeList: function() {
      var companyList = [];
      this.activeCardNoList = [];
      for (var i = 0; i < this.checkList.length; i++) {
        var ele = this.checkList[i];
        companyList.push(ele.companyCode);
        this.activeCardNoList.push(ele.cardNo);
      }
      return companyList;
    },
    ...mapState(["isQianYi"]),
  },
  methods: {
    ...mapMutations(["isQianYi_fn"]),

    /**
     * @description: 原生端SDK注册回调(userId会用于APP激活SDK，需监听回调onRegister)
     * @param {*} res
     * @return {*}
     */

    onRegister(res) {
      recordJsLog('onRegister', res);

      console.log("chooseEnterprise onRegister", JSON.stringify(res));
      if (res.error == 0) {
        // 调用原生迁移判断
        recordJsLog('hasCards2Reapply');

        this.$cppeiBridge("hasCards2Reapply", {});
      } else {
        // this.$dialog
        //   .alert({
        //     title: "SDK注册失败,请多次尝试，如果多次尝试失败请联系相关管理人员",
        //     message: res.error + "-" + res.message,
        //   })
        //   .then(() => {
        //     this.reloadLogin();
        //   });
        this.$dialog
          .alert({
            title: "温馨提示",
            message:`<h4 style="margin:-5px 0;text-align:left">SDK注册失败,请多次尝试，如果多次尝试失败请联系相关管理人员</h4>` +`<p>` +res.error + "-" +res.message +`</p>`,
          })
          .then(() => {
            this.reloadLogin();
          });
      }
    },

    /**
     * @description: onHasCards2Reapply 返回值为true 表示需要迁移
     * @param {*} obj
     * @return {*}
     */

    onHasCards2Reapply(obj) {
      recordJsLog('onHasCards2Reapply', obj);

      console.log("chooseEnterprise onHasCards2Reapply:" + JSON.stringify(obj));
      // error成功为0, 20000001-注册已失效需重新登录
      // error为0时原生端的register和判断是否迁移都没问题，这时直接通过result判断是否需要迁移。error不为0时通过message给出提示
      if (obj.error === 0) {
        if (obj.result === true) {
          this.isQianYi_fn(1);
          // 调用迁移判断需要迁移时更新状态为1
          recordJsLog('saveReapplyStatus', { reapplyStatus: 1 });

          this.$cppeiBridge("saveReapplyStatus", { reapplyStatus: 1 });
          console.log("chooseEnterprise 设置迁移状态isQianYi：1");
        } else {
          //  无错误，且待迁移判断是false，说明不需要迁移
          this.isQianYi_fn(2);
          recordJsLog('saveReapplyStatus', { reapplyStatus: 2 });

          this.$cppeiBridge("saveReapplyStatus", { reapplyStatus: 2 });
          console.log("chooseEnterprise 设置迁移状态isQianYi：2");
          // 不需要迁移的时候直接调用sdk设置密码
          this.activateCards();
        }
      } else if (obj.error == "20000001") {
        this.$Dialog
          .alert({
            title: "提示",
            message: obj.error + "-" + "注册已失效需重新登录",
          })
          .then(() => {
            this.reloadLogin();
          });
      } else if (obj.error == "-11") {
        console.log('onHasCards2Reapply 判断是否都激活 currentasnResult ====>', this.currentasnResult);
        if (this.currentasnResult) {
          this.$Dialog
            .alert({
              title: "提示",
              message: obj.error + "-" + obj.message,
            })
            .then(() => {
              this.reloadLogin();
            });
        } else {
          //  无错误，且待迁移判断是false，说明不需要迁移
          this.isQianYi_fn(2);
          recordJsLog('saveReapplyStatus', { reapplyStatus: 2 });
          
          this.$cppeiBridge("saveReapplyStatus", { reapplyStatus: 2 });
          console.log("chooseEnterprise 设置迁移状态isQianYi：2");
          this.activateCards();
        }
      } else {
        this.$Dialog
          .alert({
            title: "提示",
            message: obj.error + "-" + obj.message,
          })
          .then(() => {
            this.reloadLogin();
          });
      }
    },

    /**
     * @description: 设置默认卡回调
     * @param {*} res
     * @return {*}
     */

    onSetDefaultCard(params) {
      recordJsLog('onSetDefaultCard', params);
      console.log("chooseEnterprise 设置默认卡回调:" + JSON.stringify(params));

      if(params.route === 'chooseEnterprise'){
        if (params.error === 0) {
          this.$router.replace({
            path: "/gasstationmap",
          });
        } else {
          this.$Dialog
            .alert({
              title: "设置默认卡失败",
              message: params.error + "-" + params.message,
            })
            .then(() => {});
        }
      }
    },

    /**
     * @description: 注册卡sdk回调
     * @param {*} params
     * @return {*}
     */

    onActivateCards(params) {
      recordJsLog('onActivateCards', params);
      console.log( "chooseEnterprise 注册卡onActivateCards回调:" + JSON.stringify(params));

      if(params.route === 'chooseEnterprise'){
        params.successCards = params.successCards ? params.successCards : [];
        params.failedCards = params.failedCards ? params.failedCards : [];
        if (params.successCards.length > 0) {
          // 调用后台设置激活状态
          this.changeState(params.successCards);
          // 查询出第一张卡号对应的公司id
          this.queryCompanyCode(params.successCards[0]);

          if (params.failedCards.length > 0) {
            this.$Dialog.alert({
              title: "部分卡片激活失败,列表如下",
              message: filter.errorMsg(params.failedCards), // 修改错误提示信息格式 - add by yl - 2022.5.5
            });
          } else {
            this.$toast("设置支付密码成功");
          }
        } else {
          this.activeNum++;
          /**
          * SDK设置支付密码成功后但后台未更新激活状态意外退出，下次进到我的不会在显示该企业，
          * 但在首页切换企业会显示设置支付密码，调用SDK会提示卡片不是待激活状态(错误码确认)，该状态直接调用后台更新激活状态即可
          */
          const failedErrorList = params.failedCards.filter(item => item.error == '20000007').map(item => item.pan);
          const otherFailedCardsList = params.failedCards.filter(item => item.error != '20000007');
          if(failedErrorList && failedErrorList.length > 0){
            this.changeState(failedErrorList, true);
          }

          if(otherFailedCardsList && otherFailedCardsList.length > 0){
            this.$Dialog.alert({
              title: "全部激活失败",
              message: filter.errorMsg(otherFailedCardsList) + "请点击【激活】再次设置",
            }).then(()=>{});
          }
        }
      }    
    },

    /**
     * @description: 重新登录
     * @return {*}
     */

    reloadLogin() {
      this.isDisabled = false;
      this.$cppeiBridge("clearToken", {});
      storage.ss.remove("apptoken");
      storage.ss.clear();
      this.$router.replace({
        path: "/loginCenter",
      });
    },

    /**
     * @description: 验证手机号是否有权限
     * @return {*}
     */

    isPhoneExist() {
      // 验证是否有权限
      isPhoneExist({
        phone: storage.ss.get("phone"),
        type: 2,
      }).then((res) => {
          if (res.infoCode == 1) {
            // 判断是否都激活
            this.currentasnResult = res.data.data.every(
              (item) => item.currentasn != null
            );
            console.log("chooseEnterprise isPhoneExist currentasnResult:" + this.currentasnResult);
          }
        })
        .catch((err) => {});
    },

    /**
     * @description: 点击选择卡片
     * @param {*} item
     * @return {*}
     */

    checkListIndex(item) {
      if (this.checkList.includes(item)) {
        this.checkList.splice(this.checkList.indexOf(item), 1);
      } else {
        if (this.checkList.length < 5) {
          this.checkList.push(item);
        } else {
          Toast("最多可选择5个企业!");
        }
      }
      console.log('点击列表选中 checkList',JSON.stringify(this.checkList));
    },

    /**
     * @description: 未开通企业列表
     * @return {*}
     */

    loadUnActivatedList() {
      getUnActivatedList({
        curPage: 1,
        pageSize: 10,
        phone: storage.ss.get("phone"),
        type: this.$setting.APP_TYPE,
      })
        .then((res) => {
          this.list = res.data;
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 获取已开通未激活企业
     * @return {*}
     */

    loadOpenCardList() {
      getOpenCardList({
        phone: storage.ss.get("phone"),
        type: this.$setting.APP_TYPE,
      })
        .then((res) => {
          this.list = res.data;
          // for (let ele of res.data) {
          //   this.cardNos.push(ele.cardNo);
          // }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 开卡
     * @return {*}
     */

    openCards() {
      console.log("openCards:", JSON.stringify(this.companyCodeList));
      if (this.companyCodeList.length < 1) {
        Toast("至少选择1个企业!");
        return;
      }

      // 判断当前认证所属司机信息与激活司机信息是否一致
      const everyResult = this.checkList.every(
        (item) => item.idCard === storage.ss.get("idCard")
      );
      console.log("chooseEnterprise 判断当前认证所属司机信息与激活司机信息是否一致:",everyResult);
      if (!everyResult) {
        this.$Dialog.alert({
          title: "提示",
          message: "当前认证司机信息与激活司机信息不一致，请重新选择！",
        });
        return;
      }

      console.log("点击激活时检测开卡次数:", this.activeNum);
      // 失败-给出提示，3次或5次后弹出建议重新登录
      if (this.activeNum > 3) {
        this.logOutDialog();
        return;
      }

      // 倒计时60秒
      this.countdown();

      // isActive: 1  人脸认证页面 getActivatedList返回数据data = []
      if (this.isActive == 1) {
        console.log('已激活未设置密码activeCardNos:',JSON.stringify(this.activeCardNos));
        let successArr = [];
        let openCardList = this.resArr(this.checkList, this.activeCardNos);
        console.log('和重新选择的数组作比较过滤出未激活的司机卡数组openCardList ====>', JSON.stringify(openCardList));

        //  openCardList: 和重新选择的数组作比较过滤出未激活的司机卡数组
        if (openCardList.length == 0) {
          this.activeCardNos.forEach((item) => {
            successArr.push(item.cardAsn);
          });
          console.log('继续触发激活流程，调用原生，待传卡号：',  successArr);
          // this.activateCards()
          recordJsLog('saveInfo', { 
            token: storage.ss.get("apptoken"), 
            userId: storage.ss.get("userId")
          });

          this.$cppeiBridge("saveInfo", { 
            token: storage.ss.get("apptoken"), 
            userId: storage.ss.get("userId")
          });
          return

        } else {
          let arrResult = this.resArr1(this.checkList, openCardList);
          console.log('过滤出已激活的司机卡数组 arrResult ====>',  JSON.stringify(arrResult));

          let str = '';
          console.log('已经激活未设置密码的司机卡数组 activeCardNos ====>', JSON.stringify(this.activeCardNos))
          // arrResult长度为 0 代表重新选择了其他的企业
          if (arrResult.length == 0) {
            openCardList.forEach((item) => {
              successArr.push(item.companyCode);
            });
            console.log('代表选择了其他企业，继续触发激活流程，激活的companyCode为:', successArr);
            this.firstActivedCompanyOrDriver();

          } else {
            arrResult.forEach((item) => {
              str += item.companyName + " - " + item.companyCode + " - " + item.name + '\n';
            });
            this.$Dialog.alert({
              title: '提示',
              message: `您已经激活过企业（但未设置支付密码），信息如下:\n` + str + `您可选择上述已激活企业，或选择其他未激活企业，但不能混选！`
            });
            return
          }

        }

        // 先调用后台激活接口，开卡成功生成卡号，再调SDK设置支付密码，设置完成后才算激活成功
        // this.firstActivedCompanyOrDriver();
      
      } else {
        // 删除应用或换手机登录，还是会提示首次激活，此时会提示迁移，走【迁移流程-首次激活】
        // 11.11修改，也需要先注册走迁移流程在调用SDK激活
        // 调用原生保存
        recordJsLog('saveInfo', {
          token: storage.ss.get("apptoken"),
          userId: storage.ss.get("userId"),
        });

        this.$cppeiBridge("saveInfo", {
          token: storage.ss.get("apptoken"),
          userId: storage.ss.get("userId"),
        });
        // this.activateCards();
      }
    },

    /**
     * @description: 封装两个数组去重方法
     * @return {*}
     */
    resArr(arr1, arr2) {
      return arr1.filter((v) => arr2.every((val) => val.uniqueid != v.companyCode));
    },
    resArr1(arr1, arr2) {
      return arr1.filter((v) => arr2.every((val) => val.companyCode != v.companyCode));
    },

    /**
     * @description: 首次开通司机卡
     * @return {*}
     */

    firstActivedCompanyOrDriver() {
      this.cardNos = [];
      // this.activeCardNos = [];
      doOpenCards({
        authToken: storage.ss.get("authToken"),
        // companyCodeList: this.companyCodeList.join(","),
        companyCodeList: Array.isArray(this.companyCodeList)
          ? this.companyCodeList
          : this.companyCodeList.split(","),
        idCard: storage.ss.get("idCard"),
        phone: storage.ss.get("phone"),
        name: storage.ss.get("name"),
        type: this.$setting.APP_TYPE,
        userId: 1,
      })
        .then((res) => {
          if (res.infoCode === 1) {
            // 获取卡号
            this.activeCardNos.push(...res.data?.driverList);
            console.log('activateCompanyOrDriver activeCardNos ====>', JSON.stringify(this.activeCardNos));

            if (res.data?.driverList) {
              for (let e of res.data?.driverList) {
                this.cardNos.push(e.cardAsn);
              }
            }
            // 判断数据是否在数组中存在
            // this.activeCardNos = res.data?.driverList ?? [];
            // console.log('doOpenCards start:',JSON.stringify(this.activeCardNos));
            // if(this.activeCardNos.length == 0){
            //   this.activeCardNos = res.data?.driverList ?? [];

            // }else{
            //   let arr = [...this.activeCardNos]
            //   arr.forEach(item=>{
            //     res.data?.driverList.forEach(el=>{
            //       if(item.uniqueid != el.uniqueid){
            //         this.activeCardNos.push(el)
            //       }
            //     })
            //   })
            // }
            // console.log('doOpenCards end:',JSON.stringify(this.activeCardNos));

            

            // 后台设置默认公司,不设置sdk,保证下次直接跳过ocr（人脸认证识别）
            this.$http(
              "POST",
              "/user/v1/getActivatedList",
              {
                appid: 2,
                apptoken: storage.ss.get("apptoken"),
                phone: storage.ss.get("phone"),
                curPage: 1,
                pageSize: 99,
                type: 2, // 1企业端，2司机端
              },
              true,
              true,
              true
            ).then((res) => {
              if (res.infoCode == 1) {
                this.updateDefaultCompanyCode(this.companyCodeList[0], false);
              }
            });
            // sdk激活
            // this.activateCards();
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 查询出第一张卡号对应的公司id
     * @param {*} cardNo
     * @return {*}
     */

    queryCompanyCode(cardNo) {
      getCompanyInfoByCardNo({
        cardAsn: cardNo,
      })
        .then((res) => {
          // 注意,不能用res.data
          if (res?.data.companyCode) {
            // 设置默认公司&sdk设置默认卡
            this.updateDefaultCompanyCode(res?.data.companyCode, true);
          } else {
            Toast("企业不存在,无法设置默认卡");
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /**
     * @description: 设置默认公司&sdk设置默认卡
     * @param {*} companyCode
     * @param {*} isSetDef
     * @return {*}
     */

    updateDefaultCompanyCode(companyCode, isSetDef) {
      // 先设置默认卡
      updateDefaultCompanyCode({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        companyCode: companyCode,
        phone: storage.ss.get("phone"),
        type: this.$setting.APP_TYPE,
      })
        .then((res) => {
          if (res.infoCode === 1) {
            storage.ss.set("defaultCompanyCode", companyCode);
            storage.ss.set("apptoken", res.data.apptoken);
            storage.ss.set("userId", res.data.id);
            storage.ss.set("phone", res.data.phone);
            console.log('updateDefaultCompanyCode isSetDef:',isSetDef);

            // 调用sdk设置默认卡
            if (isSetDef) {
              this.setDef(res.data.cardNo);
              //! isSetDef为true时，是激活后的回调，只有这种情况下才保存token等信息
              // this.$cppeiBridge("saveInfo", { token: res.data.apptoken, userId: res.data.id});
            } else {
              console.log('触发saveInfo');

              // 调用原生保存
              recordJsLog('saveInfo', { token: storage.ss.get("apptoken"), userId: storage.ss.get("userId")});

              this.$cppeiBridge("saveInfo", { token: storage.ss.get("apptoken"), userId: storage.ss.get("userId")});
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    onHasCards2ReapplyCallback() {},

    /**
     * @description: 调用后台设置激活状态
     * @param {*} params
     * @param {*} isSetDefaultCard 对于激活回调 20000007 错误码是否需要设置默认卡
     * @return {*}
     */

    changeState(params, isSetDefaultCard = false) {
      changeState({
        phone: storage.ss.get("phone"),
        companyCodeList: [],
        cardNoList: params,
        type: this.$setting.APP_TYPE,
      })
        .then((res) => {
          if (res.infoCode !== 1) {
            Toast(res.info);
          }else{
            if(isSetDefaultCard){
              this.queryCompanyCode(params[0]);
            }
          }
        })
        .catch((err) => {
          Toast(err);
        });
    },

    /**
     * @description: 失败-给出提示，3次或5次后弹出建议重新登录
     * @return {*}
     */

    logOutDialog() {
      this.$Dialog
        .confirm({
          title: "提示",
          message: "电子卡多次激活失败，建议重新登录",
        })
        .then(() => {
          this.$cppeiBridge("clearToken", {});
          storage.ss.remove("apptoken");
          storage.ss.clear();
          this.$router.replace({
            path: "/loginCenter",
          });
        })
        .catch(() => {});
    },

    /**
     * @description: 调用sdk设置默认卡
     * @param {*} cardNo
     * @return {*}
     */

    setDef(cardNo) {
      if ("" === cardNo || null === cardNo) {
        // Toast("卡号为: 空");
      } else {
        // Toast("sdk设置默认卡,卡号为: " + cardNo);
        // sdk 设置默认卡
        recordJsLog('setDefaultCard', { 
          pan: cardNo,
          route: 'chooseEnterprise'
        });

        this.$cppeiBridge("setDefaultCard", {
          pan: cardNo,
          route: 'chooseEnterprise'
        });
      }
    },

    /**
     * @description: SDK激活，即批量设置移动支付密码
     * @return {*}
     */

    activateCards() {
      // this.countdown();
      let cardList = this.isActive == 1 ? this.cardNos : this.activeCardNoList;
      console.log("activateCards SDK激活设置移动支付密码 : " + cardList);

      if (cardList.length > 0) {
        recordJsLog('activateCards', { 
          pans: cardList,
          route: 'chooseEnterprise'
        });

        this.$cppeiBridge("activateCards", { 
          pans: cardList,
          route: 'chooseEnterprise'
        });
      } else {
        this.$Dialog.alert({
          title: "温馨提示",
          message: "无卡号,无法激活",
        });
      }
    },

    /**
     * @description: 倒计时60秒
     * @return {*}
     */

    countdown() {
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.isDisabled = true;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.isDisabled = false;
            clearInterval(this.timer); // 清除定时器
            this.timer = null;
          }
        }, 1000);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.choose_card {
  background: #ffffff;
  padding-top: 40px;
  height: 100vh;
  overflow: scroll;

  p {
    margin: 30px 2.5vw;
    text-align: left;
    padding: 30px 0 0 0;
  }

  /deep/.van-cell-group {
    width: 95vw;
    margin: 0 auto;
    // 控制中间滚动
    // overflow-y: scroll;
    @include useScrollByBrowser(0);
    height: 115vw;
  }

  /deep/ .van-cell {
    background: #a39f9f;
    margin: 10px 0;
    text-indent: 30px;
  }

  /deep/ .van-cell__title {
    text-align: left;
    color: #ffffff;
  }

  /deep/ .van-cell__label {
    text-align: left;
    color: #ffffff;
  }

  /deep/ .van-button {
    position: absolute;
    bottom: 70px;
    width: 95vw;
    left: 2.5vw;
  }
}
</style>
