<template>
  <div class="staion_order">
    <pay-prompt v-if="promptState" />
    <nav-bar :class="stopgoHome == 1 ? navbar : ''" title="订单选择" />
    <div class="staion_box">
      <div class="staion_name">{{ OilStationinfo.stationName }}</div>
      <van-cell-group class="order-box" title="订单选择">
        <van-cell
          v-for="(item, index) in OilGunTrade.saleItem"
          :key="index"
          :title="item.weight + 'L'"
          :label="oilProductName + '-' + oilGunNo + '号枪'"
          :value="'￥' + item.amount"
          center
          @click="checkTrade(index)"
          :class="index == orderNum ? 'activedOrder' : ''"
        >
          <template #right-icon>
            <van-icon name="success" class="success-icon" />
          </template>
        </van-cell>
      </van-cell-group>
      <van-cell-group class="pay-box" title="支付方式">
        <van-cell
          v-for="(item, index) in 1"
          :key="index"
          :label="'余额：￥' + drivercardinfo.balance / 100"
          center
          @click="payNum = index"
          :class="index == payNum ? 'activedPay' : ''"
        >
          <template #title>
            <div class="oil_card">
              <van-image
                width="1.5rem"
                height="1.5rem"
                fit="contain"
                :src="require('@/assets/images/OrderDetails/card-prefix.png')"
              />
              <span class="custom-title">加油卡({{ cardNosub }})</span>
              <!-- <van-tag :color="$setting.themeColor" text-color="#fff">切换油卡</van-tag> -->
            </div>
          </template>
          <template #right-icon>
            <van-icon name="checked" class="checked-icon" />
          </template>
        </van-cell>
      </van-cell-group>
    </div>
    <div class="order_pay">
      <span>实付：￥</span>
      <b>{{ checkTradeorder.amount }}</b>
      <van-button type="primary" size="small" @click="isDeviceFingerClick"
        >立即支付</van-button
      >
    </div>
  </div>
</template>
<script>
import navBar from "@/components/navBar/NavHeader";
import cppeiBridge from "Utils/bridge";
import { storage, mul, compareVersions } from "Utils/common/";
import moment from "moment";
import { recordJsLog } from "@/utils/recordJsLog";
import payPrompt from "./component/payprompt";
import { payRiskManagement } from "Utils/payRiskManagement";
import { payRiskManagementFinish } from "Utils/payRiskManagementFinish";
export default {
  components: { navBar, payPrompt },
  data() {
    return {
      OilStationinfo: {}, //当前油站所有信息
      OilGunTrade: [], //对应油枪所有订单信息
      drivercardinfo: {}, //用户信息，包含电子卡号
      oilGunNo: "", // 油枪号
      oilProductNaonConfirmPayTrademe: "", //油品名称
      checkTradeorder: {}, // 选中的订单
      orderNum: 0, // 选中订单的下标
      payNum: 0, // 选中支付方式的下标，目前就中支付方式：电子加油卡支付
      cardNosub: "",
      payOilguntradeMix: {}, // 室内下单获取支付数据
      loading: false,
      stopgoHome: 0,
      promptState: false, // 支付中显示隐藏
      oilProductName: "",
      timerNumber: 0, // 订单查询轮询次数
      timerMax: 3, // 订单查询轮询最大次数
      payTradeParams: {}, // 支付参数
      payRiskState: null, // 风控参数发生状态 2 开始  0 完成  1 失败
    };
  },
  created() {
    console.log(this.$route, "route");
    this.Init();
  },
  activated() {
    console.log("chooseOrder activated");
  },
  mounted() {
    window.onConfirmPayTrade = this.onConfirmPayTrade;
    window.onDeviceFinger = this.onDeviceFinger;
    // console.log(window.onConfirmPayTrade,'window.onConfirmPayTrade');
    // this.$cppeiBridge("confirmPayTrade", {
    //   order: JSON.stringify({ a: 1231 }),
    // });
  },
  methods: {
    async Init() {
      this.loading = false;
      this.OilStationinfo = await JSON.parse(this.$route.params.OilStationinfo);
      this.OilGunTrade = await JSON.parse(this.$route.params.OilGunTrade);
      this.oilGunNo = await this.$route.params.oilGunNo;
      this.oilProductName = await this.$route.params.oilProductName;
      this.checkTradeorder = await this.OilGunTrade.saleItem[this.orderNum];
      console.log(this.OilStationinfo, "this.OilStationinfo");
      console.log(this.OilGunTrade, "this.OilGunTrade");
      console.log(this.oilGunNo, "this.oilGunNo");
      console.log(this.oilProductName, "this.oilProductName");
      console.log(this.checkTradeorder, "this.checkTradeorder");
      this.getDriverInfo();
    },
    // 刷新油站列表
    refreshOilStation() {
      this.timerNumber = 0;
      const params = {
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        standardCode: this.OilStationinfo?.oilStationCode,
        // standardCode: "BA01",
        gunNo: this.oilGunNo,
        channel: "932",
      };
      this.$http(
        "POST",
        `/appcenter/trade/v1/getTradeOrderList`,
        params,
        true,
        true,
        true,
        false,
        true
      )
        .then((res) => {
          if (res.infoCode == 1) {
            this.OilGunTrade = res?.data;
            this.orderNum = 0;
            this.checkTradeorder = this.OilGunTrade?.saleItem[this.orderNum];
          }
        })
        .catch((err) => {
          this.$toast(err);
        });
    },
    // 用于获取电子卡号
    getDriverInfo() {
      const params = {
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        companyCode: storage.ss.get("defaultCompanyCode"),
        phone: storage.ss.get("phone"),
        userid: storage.ss.get("userId"),
      };
      this.$http(
        "POST",
        `/user/v1/getDriverInfo`,
        params,
        true,
        true,
        true,
        false,
        true
      )
        .then((res) => {
          if (res.infoCode == 1) {
            this.drivercardinfo = res?.data;
            this.cardNosub = this.drivercardinfo?.cardNo.substring(
              this.drivercardinfo?.cardNo.length - 4
            );
            this.drivercardinfo.moneyType != "0"
              ? (this.drivercardinfo.balance = this.drivercardinfo.shareBalance)
              : "";
            console.log(this.drivercardinfo, "this.drivercardinfo");
          }
        })
        .catch((err) => {
          this.$toast("未查询到电子卡");
        });
    },
    checkTrade(index) {
      this.orderNum = index;
      this.checkTradeorder = this.OilGunTrade?.saleItem[index];
    },
    /**
     * 判断是否通过设备
     *
     * @returns {boolean} 如果通过设备则返回true，否则返回false
     */
    isDeviceFingerClick() {
      if (compareVersions(storage.ss.get("deviceInfo").version, "1.1.2") >= 0) {
        this.payRiskState = 2;
        recordJsLog("deviceFinger", {});
        this.$cppeiBridge("deviceFinger", {});
      } else {
        this.orderPay()
      }
    },
    // 支付订单
    orderPay(deviceId = "") {
      if (this.drivercardinfo?.balance / 100 < this.checkTradeorder?.amount) {
        this.$toast("该卡余额不足，请联系管理员或充值");
        return false;
      }
      if (!this.checkTradeorder?.amount) return false;
      let params;
      const deviceInfo = storage.ss.get("deviceInfo");
      if (this.OilGunTrade.orderType == 4) {
        // 2.0
        params = {
          address:
            deviceInfo?.province +
            ";" +
            deviceInfo?.city +
            ";" +
            deviceInfo?.district,
          driverPhone: storage.ss.get("phone"),
          latitude: deviceInfo?.lat ?? "39.920248",
          longitude: deviceInfo?.lon ?? "116.407718",
          tradeType: "3",
          deviceId: deviceId,
          status: "",
          authInfo: this.OilGunTrade?.authInfo,
          appid: 2,
          apptoken: storage.ss.get("apptoken"),
          tradeMode: "10",
          plateNo: this.drivercardinfo?.licenseNum, //车牌号
          tradeNo: this.checkTradeorder?.tradeNo, // 选中订单的pos交易号
          gasStationNo: this.OilStationinfo?.oilStationCode, // 当前油站的油站编码
          gasStationName: this.OilStationinfo?.stationName, //油站名称
          oilGunNo: this.oilGunNo, // 油枪号
          promotionAmount: 0, // 可能不存在，促销金额
          afterPromotionAmount: 0, // 可能不存在，促销后金额
          billCreateIp: "", // IP地址  新增
          workstationId: this.OilGunTrade?.workstationId, // POS终端编号
          saleItem: this.checkTradeorder, // 选中订单的整个信息
          orderType: this.OilGunTrade?.orderType, // 渠道类型
          businessDate: this.OilGunTrade?.businessDate, // 交易日期
          // businessDate: moment().format('YYYYMMDD'), // 交易日期
          tradeTimeStamp: this.OilGunTrade?.tradeTimeStamp,
          // timeStamp: moment().format('YYYYMMDDHHmmss000'),
        };
      } else if (this.OilGunTrade.orderType == 3) {
        console.log(this.OilGunTrade.orderType, "进入了3.0");
        // 3.0
        params = {
          address:
            deviceInfo?.province +
            ";" +
            deviceInfo?.city +
            ";" +
            deviceInfo?.district,
          driverPhone: storage.ss.get("phone"),
          latitude: deviceInfo?.lat ?? "39.920248",
          longitude: deviceInfo?.lon ?? "116.407718",
          tradeType: "3",
          deviceId: deviceId,
          status: "",
          authInfo: this.OilGunTrade?.authInfo,
          appid: 2,
          apptoken: storage.ss.get("apptoken"),
          tradeMode: "100",
          channel: "932", //  渠道编码
          plateNo: this.drivercardinfo?.licenseNum, //车牌号
          tradeNo: this.checkTradeorder?.tradeNo, // 选中订单的pos交易号
          gasStationNo: this.OilStationinfo?.oilStationCode, // 当前油站的油站编码
          gasStationName: this.OilStationinfo?.stationName, //油站名称
          oilGunNo: this.oilGunNo, // 油枪号
          promotionAmount: 0, // 可能不存在，促销金额
          afterPromotionAmount: 0, // 可能不存在，促销后金额
          billCreateIp: "", // IP地址  新增
          workstationId: "POS99", // POS终端编号
          saleItem: this.checkTradeorder, // 选中订单的整个信息
          orderType: this.OilGunTrade?.orderType, // 渠道类型
          businessDate: "", // 交易日期
          // businessDate: moment().format('YYYYMMDD'), // 交易日期
          tradeTimeStamp: moment(this.OilGunTrade?.hangTime).format(
            "YYYYMMDDHHmmss000"
          ),
        };
      }
      console.log(params, "payOilGunTradeMix");
      this.$http(
        "POST",
        `/appcenter/trade/v1/payOilGunTradeMix`,
        params,
        true,
        true,
        true,
        false,
        true
      )
        .then((res) => {
          if (res.infoCode == 1) {
            this.payOilguntradeMix = res.data;
            let rposSn;
            if (this.OilGunTrade.orderType == 3) {
              console.log(123123);
              rposSn = res.data.saleItem.tradeNo.substring(4);
            } else if (this.OilGunTrade.orderType == 4) {
              rposSn = res.data.saleItem.tradeNo;
            }
            let payTradeParams = {
              orderNo: res.data.orderNo, // 订单号
              // SDK需求
              orgId: res.data?.stationCode, //"与orgCode相通",
              // SDK填充
              // actionCode: 30002,
              // SDK填充
              // signa: "签名数据",
              // SDK填充
              // oringinCipherPin: "密文密码",
              // SDK填充
              // reservedPara: "kms保留字段",
              // SDK填充
              // cpsId: "cpsId",
              mac: res.data?.mac, //"app数据校验mac",
              nozzle: res.data?.gunNo, //"油枪号",
              orgCode: res.data?.stationCode, // "机构编码",
              rposNo: "POS99", //"rpos编号",
              rposSn, // "rpos流水",
              rposTime: res.data?.rposTime, //"new Date(1537229989000)", //rpos时间
              BusinessDate: res.data?.businessDate || "", // 20190724,
              saleItems: [
                {
                  categoryCode: "", //"类别代码",
                  goodCode: res.data.saleItem.productCode, // "商品代码",
                  isOil: "true", // "是否油品交易",
                  itemName: res.data.saleItem.productName, //"商品名称",
                  payAmount: res.data.totalAmount, // "本次支付金额",
                  price: mul(res.data.saleItem.unitPrice, 100), // "商品单价",
                  promotionAmount: 0, // "促销金额",
                  quantity: mul(res.data.saleItem.weight, 100), // "商品数量",
                  rposSn, // "rpos流水",
                  sendAmount: res.data.totalAmount, //"Retalix发送的商品的金额",
                  sourceAmount: "", //"商品原始金额",
                  // "商品总计 如果有促销,则为促销后的金额,否则和sourceAmount相等",
                  totalAmount: res.data.totalAmount,
                },
              ],
              // "当笔交易的金额,如果是混合支付,则是金额,单一支付方式时等于totalAmount",
              srcTransactionAmount: res.data?.totalAmount || "",
              totalAmount: res.data?.totalAmount || "", // "此支付方式的总金额",
              transactionAmount: res.data?.totalAmount || "", //"当笔交易的金额",
              transactionBalance: "", //"混合支付时,表示交易还有多少需要支付",
              TransID: "", //"互联网要的交易流水号",
              voucher: "", // "电子卷信息",
              voucherAmount: 0, // "电子卷金额",
              voucherInfo: "", // "互联网完整报文",
              // SDK填充
              // signdata: "免密支付签名设计组成",
              // SDK填充
              // signature: "免密支付签名值",
            };
            recordJsLog("confirmPayTrade", payTradeParams);
            this.$cppeiBridge("confirmPayTrade", {
              pan: "123123",
              order: JSON.stringify(payTradeParams),
            });
            this.promptState = true;
            this.stopgoHome = 1; //进行到这里代码上阻止用户手动返回首页
          } else {
            this.$dialog
              .alert({
                title: "提示",
                message: res.info,
              })
              .then(() => {
                this.promptState = false;
                this.refreshOilStation();
              });
          }
        })
        .catch((err) => {});
    },
    // 订单支付sdk返回结果
    onConfirmPayTrade(obj) {
      console.log(obj, "chooseOrder onConfirmPayTrade");
      recordJsLog("onConfirmPayTrade", obj);
      if (obj.error == 0) {
        this.loopGetTradeOrderMix();
      } else if (obj.error === "71311001") {
        // console.log(this.payOilguntradeMix?.orderNo, 1, 3,"payRiskManagement--事后失败");
        this.$dialog
          .alert({
            title: "提示",
            message:
              "由于长时间未使用，移动支付安全组件已失效，需重新注册，否则无法使用支付相关功能",
            showCancelButton: true,
            messageAlign: "left",
          })
          .then(() => {
            recordJsLog("saveInfo");

            this.$cppeiBridge("saveInfo", {});
          })
          .catch(() => {
            // on cancel
          });
      } else {
        // 模拟软件商店审核测试人员进行上线审核
        if (this.$setting.AuditPhone === storage.ss.get("phone")) {
          this.$dialog
            .alert({
              title: "提示",
              message: "油卡移动支付未开通，请线下开通移动支付",
            })
            .then(() => {});
          this.clale();
          this.promptState = false;
        } else {
          if (obj.error == 71316002) return;
          if (
            obj.error == 71320000 ||
            obj.error == 71320001 ||
            obj.error == 71320002 ||
            obj.error == 71321000 ||
            obj.error == 71321001
          ) {
            // 网络错误 轮询调用封装接口getTradeOrderMix接口();
            this.loopGetTradeOrderMix();
            return;
          }
          this.clale();
          this.promptState = false;
          this.$Dialog
            .alert({
              title: "订单支付失败",
              message: obj.error + "-" + obj.message,
            })
            .then(() => {});
        }
      }
    },
    clale() {
      if (this.OilGunTrade.orderType == 3) {
        let params = {
          appid: 2,
          apptoken: storage.ss.get("apptoken"),
          standardCode: this.payOilguntradeMix.stationCode,
          gunNo: this.payOilguntradeMix.gunNo,
          channel: "932",
          tradeNo: this.payOilguntradeMix.saleItem.tradeNo,
          amount: this.payOilguntradeMix.saleItem.amount,
          handleNo: this.payOilguntradeMix.handleNo,
        };
        this.$http(
          "POST",
          `/appcenter/trade/v1/cancelPaymentOrder`,
          params,
          true,
          true,
          true,
          false,
          true
        )
          .then((res) => {
            this.promptState = false;
            this.refreshOilStation();
          })
          .catch((err) => {
            this.promptState = false;
            this.refreshOilStation();
          });
        return;
      }
      this.refreshOilStation();
    },
    // 轮询订单支付结果
    loopGetTradeOrderMix() {
      if (compareVersions(storage.ss.get("deviceInfo").version, "1.1.2") >= 0) {
        this.payRiskState = 1;
        recordJsLog("deviceFinger", {});
        this.$cppeiBridge("deviceFinger", {});
      } else {
        this.loading = true;
        this.timerNumber = this.timerNumber + 1;
        const deviceInfo = storage.ss.get("deviceInfo");
        const params = {
          address:
            deviceInfo?.province +
            ";" +
            deviceInfo?.city +
            ";" +
            deviceInfo?.district,
          driverPhone: storage.ss.get("phone"),
          latitude: deviceInfo?.lat ?? "39.920248",
          longitude: deviceInfo?.lon ?? "116.407718",
          tradeType: "3",
          deviceId: "",
          status: "0",
          appid: 2,
          apptoken: storage.ss.get("apptoken"),
          orderNo: this.payOilguntradeMix?.orderNo,
        };
        this.$http(
          "POST",
          `/appcenter/trade/v1/getTradeOrderMix`,
          params,
          true,
          true,
          true,
          false,
          false
        )
          .then((res) => {
            if (res.infoCode == 1) {
              this.loading = false;
              this.$router.replace({
                path: "/chooseOrder/result",
                query: {
                  tradeOrderMix: JSON.stringify(res.data),
                  addoiltype: 2,
                  OilStationinfo: JSON.stringify(this.OilStationinfo),
                },
              });
            } else if (res.infoCode == 4 || res.infoCode == 6) {
              if (this.timerNumber >= this.timerMax) {
                this.$Dialog
                  .alert({
                    title: "订单查询失败",
                    message: "请联系相关管理人员，查询订单状态",
                  })
                  .then(() => {
                    this.promptState = false;
                    this.refreshOilStation();
                  });
              } else {
                setTimeout(() => {
                  this.loopGetTradeOrderMix();
                }, 3000);
              }
            } else {
              console.log("123123");
              this.$Dialog
                .alert({
                  title: "订单查询失败",
                  message: res.info,
                })
                .then(() => {
                  this.promptState = false;
                  this.refreshOilStation();
                });
            }
          })
          .catch((err) => {
            this.$Dialog
              .alert({
                title: "订单查询失败",
                message: err,
              })
              .then(() => {
                this.promptState = false;
                this.refreshOilStation();
              });
          });
      }
    },
    // 指纹设备回调
    onDeviceFinger(obj) {
      recordJsLog("onDeviceFinger", obj);
      console.log("chooseOrder----onDeviceFinger", obj);
      console.log("chooseOrder----payRiskState", this.payRiskState);

      if (obj.error === "") {
        if (this.payRiskState == 2) {
          this.orderPay(obj.message);
        } else if (this.payRiskState == 1) {
          this.loading = true;
          this.timerNumber = this.timerNumber + 1;
          const deviceInfo = storage.ss.get("deviceInfo");
          const params = {
            address:
              deviceInfo?.province +
              ";" +
              deviceInfo?.city +
              ";" +
              deviceInfo?.district,
            driverPhone: storage.ss.get("phone"),
            latitude: deviceInfo?.lat ?? "39.920248",
            longitude: deviceInfo?.lon ?? "116.407718",
            tradeType: "3",
            deviceId: obj.message,
            status: "0",
            appid: 2,
            apptoken: storage.ss.get("apptoken"),
            orderNo: this.payOilguntradeMix?.orderNo,
          };
          this.$http(
            "POST",
            `/appcenter/trade/v1/getTradeOrderMix`,
            params,
            true,
            true,
            true,
            false,
            false
          )
            .then((res) => {
              if (res.infoCode == 1) {
                this.loading = false;
                this.$router.replace({
                  path: "/chooseOrder/result",
                  query: {
                    tradeOrderMix: JSON.stringify(res.data),
                    addoiltype: 2,
                    OilStationinfo: JSON.stringify(this.OilStationinfo),
                  },
                });
              } else if (res.infoCode == 4 || res.infoCode == 6) {
                if (this.timerNumber >= this.timerMax) {
                  this.$Dialog
                    .alert({
                      title: "订单查询失败",
                      message: "请联系相关管理人员，查询订单状态",
                    })
                    .then(() => {
                      this.promptState = false;
                      this.refreshOilStation();
                    });
                } else {
                  setTimeout(() => {
                    this.loopGetTradeOrderMix();
                  }, 3000);
                }
              } else {
                console.log("123123");
                this.$Dialog
                  .alert({
                    title: "订单查询失败",
                    message: res.info,
                  })
                  .then(() => {
                    this.promptState = false;
                    this.refreshOilStation();
                  });
              }
            })
            .catch((err) => {
              this.$Dialog
                .alert({
                  title: "订单查询失败",
                  message: err,
                })
                .then(() => {
                  this.promptState = false;
                  this.refreshOilStation();
                });
            });
        }
      } else {
        this.$Dialog
          .alert({
            title: "提示",
            message: obj.error + "-" + obj.message,
          })
          .then(() => {});
      }
    },
  },
  destroyed() {
    this.timerNumber = 0;
  },
};
</script>
<style lang="scss" scoped>
.staion_order {
  .navbar {
    pointer-events: none;
  }
  .staion_box {
    padding: 3vw;
    .staion_name ~ div {
      background: #fff;
      border-radius: 3vw;
      padding: 0 2vw 3vw;
    }
    .staion_name ~ div:last-child {
      margin-top: 5vw;
      padding-bottom: 5vw;
    }
    .staion_name {
      text-align: left;
      color: #989898;
      padding-bottom: 3vw;
    }
    .van-cell-group__title {
      text-align: left;
      color: #000;
    }
    .van-cell__title {
      text-align: left;
    }
    .van-cell__value {
      margin-right: 2vw;
    }
    /deep/ .order-box {
      // overflow-y: scroll;

      @include useScrollByBrowser(0);
      height: 80vw;
      .van-cell {
        border: 1px solid #b9b5b5;
        border-radius: 2vw;
        margin-bottom: 2vw;
      }
      .activedOrder {
        border: 1px solid #ff9200;
        color: #ff9200;
        .van-cell__label {
          color: #ff9200;
        }
        .van-cell__value {
          color: #ff9200;
        }
      }
      [class*="van-hairline"]:after {
        border: none;
      }
    }
    .pay-box {
      .oil_card {
        display: flex;
        align-items: center;
        .custom-title {
          margin: 0 2vw;
        }
      }
      .activedPay {
        .van-icon {
          color: #ff9200;
        }
      }
    }
  }
  .order_pay {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 3vw;
    padding-bottom: 3vw;
    .van-button--primary {
      background-color: #ff9200;
      border: 1px solid #ff9200;
      border-radius: 2vw;
      margin-left: 5vw;
      padding: 0 16px;
    }
  }
}
</style>
