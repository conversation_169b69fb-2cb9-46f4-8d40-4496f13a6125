<template>
  <div class="recharge">
    <nav-bar title="充值" />
    <div class="my_card_details flex_center white">
      <div class="card_left ml30 tac">
        <logo />
        <div class="m10">中国石油</div>
        <div id="icon" @click="inverse">
          <van-icon name="closed-eye" size="25" v-if="hide" />
          <van-icon name="eye-o" size="25" v-else />
        </div>
      </div>
      <div class="card_right ml30 tll">
        <div id="city">{{ formatItem.city }}</div>
        <div>{{ formatItem.cardNo }}</div>
        <div>
          <div class="flex_between vw50">
            <div>
              余额 (元)
              <div>{{ null === this.formatItem.balance || this.formatItem.balance == undefined ? "0.00" : this.formatItem.balance }}</div>
            </div>
            <div>
              总积分
              <div>{{ null === this.formatCredit || this.formatCredit == undefined ? "0" : this.formatCredit }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="pay_box">
      <div class="pay-mode">
        <div class="pay-mode-cs">选择充值方式:</div>
          <van-radio-group v-model="radio" @change="onChange">
            <van-cell-group>
              <van-cell title="支付宝支付" :icon="require('@/assets/images/PayInfo/alipay_big.png')" clickable data-name="1" @click="onClick">
                <van-radio slot="right-icon" name="1" />
              </van-cell>
              <!-- <van-cell title="微信" icon="wechat-pay" clickable data-name="2" @click="onClick">
                <van-radio slot="right-icon" name="2" />
              </van-cell>
              <van-cell title="昆仑银行" icon="gold-coin" clickable data-name="3" @click="onClick" >
                <van-radio slot="right-icon" name="3" />
              </van-cell> -->
            </van-cell-group>
          </van-radio-group>
      </div>
      <div class="recharge-amount">
        <div class="recharge-amount-cs">充值金额:</div>
        <div class="amount-list">
          <div
            v-for="(item, index) in amounts" :key="index"
            @click="choiceMoney(item,index)"
            :class="index == temp ? 'activedOilType' : ''"
          >
            {{ item }}
          </div>
        </div>
        <van-field v-model="amount" v-if="temp == 3" type="number" label="自定义金额" placeholder="请输入自定义金额" />
      </div>
      <div class="notice">为确保资金安全,超过5000元的资金请联系管理员充值</div>
      <div class="check_box">
        <van-checkbox v-model="checked" label-disabled  :checked-color="$setting.themeColor" icon-size="4.0vw">
          我已阅读并接受
          <span class="text" @click="goToAgreement">《中国石油昆仑加油卡客户网上充值协议》</span>
        </van-checkbox>
      </div>
    </div>
    <van-button type="primary" @click="submitPay" :disabled="btnDisabled" :color="$setting.themeColor" round>充值</van-button>
  </div>
</template>

<script>
import navBar from "@/components/navBar/NavHeader";
import { storage } from "Utils/common/";
import filter from "@/filters/index.js"; // 不能用{}

import { Toast } from "vant";
import { aliPay,GetOrderInfo,GetUserCardInfo } from "./_service";
import { recordJsLog } from "@/utils/recordJsLog";

export default {
  props: {},
  components: { navBar },
  data() {
    return {
      hide: false,
      item: storage.ss.get("driverInfo"),
      radio: "1", //1支付宝 2微信 3昆仑银行
      checked: true,  // 默认选中 是否勾选充值协议
      credit: null,
      // 默认可用
      btnDisabled: false,
      show: true,
      temp: 0, // 选中金额的焦点
      amounts: ["500", "1000", "1500", "其他"],
      amount: '500',
      orderStr: '', //充值后端返回的字符串
      orderNo: '',
      alipayTimer: null,
    };
  },
  computed: {
    formatItem: function () {
      // 不隐藏,直接return
      if (!this.hide) {
        return {
          cardNo: this.item.cardNo,
          city: this.item.city,
          balance: filter.formatMoney(this.item.balance / 100),
          shareBalance: this.item.shareBalance,
          phone: this.item.phone,
          companyCode: this.item.companyCode,
        };
        // return this.item;
      }
      // 隐藏,替换成*,再return
      var result = {
        cardNo: filter.formatBankCard(this.item.cardNo),
        city: this.item.city,
        balance: filter.formatDef(this.item.balance),
        shareBalance: filter.formatDef(this.item.shareBalance),
        phone: filter.formatDef(this.item.phone),
        companyCode: filter.formatDef(this.item.companyCode),
      };
      return result;
    },
    formatCredit: function () {
      // 不隐藏,直接return
      if (!this.hide) {
        return this.credit;
      }
      // 隐藏,替换成*,再return
      return filter.formatDef(this.credit);
    },
  },
  created() {},
  mounted() {
    window.onAliPay = this.onAliPay;
  },
  watch: {
    checked: function () {
      this.btnDisabled = !this.checked;
    },
  },
  methods: {
    // 取反 隐藏加油卡信息
    inverse() {
      this.hide = !this.hide;
    },
    goToAgreement () {
      this.$router.push({path: "/rechargeAgreement",});
    },
    onChange(event) {
      console.log(event);
    },
    onClick(event) {
      const { name } = event.currentTarget.dataset;
    },
    choiceMoney (item,index) {
      this.temp = index;
      this.amount = index == 3 ? '' : item;
    },
    submitPay() {
      if (this.radio*1 < 1) {
        Toast("请选择充值方式");
        return false
      }
      if (!this.amount || this.amount*1 < 0) {
        Toast("请选择或输入金额");
        return false
      }
      if (this.amount && this.amount*1 < 1) {
        Toast("充值金额不能小于1元");
        return false
      }
      aliPay({
        amount: this.amount,
        appid: 2, 
        apptoken: storage.ss.get("apptoken"),
        cardNum: this.item.cardNo,
        ifInvoice: "0",
        ip: "*************",
        phone: this.item.phone,
        companyId: this.item.companyCode,
      }).then(res => {
        if(res.data && res.infoCode == 1) {
          this.orderStr = res.data.orderStr; //decodeURIComponent(res.data); // decodeURIComponent()url解码 orderId
          this.orderNo = res.data.orderId;
          Toast("充值订单已提交，即将跳转支付宝页面");
          setTimeout(() => {
            recordJsLog('aliPay', {orderStr:`${this.orderStr}`});

            this.$cppeiBridge("aliPay", {orderStr:`${this.orderStr}`});
          }, 500);
        } else {
          Toast(res.info);
        }   
      }).catch(err => {
        Toast(err);
      })
    },
    // 原生充值结果回调
    onAliPay (params) {
      recordJsLog('onAliPay', params);

      if (params.error == 9000) {
        this.$Dialog.alert({
          title: "支付结果",
          message: "支付成功，稍后即将到账",
        }).then(() => {
          this.alipayTimer = setInterval(() => {
            this.GetOrderInfo();
          }, 10000);
        });
      } else {
        Toast("支付失败");
      }
    },
    GetOrderInfo() {
      GetOrderInfo({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
        orderNo: this.orderNo,
      }).then(res => {
        // 返回结果中的res?.data?.status 为1就是待付款，为2就是充值成功，3是支付失败
        if(res.infoCode == 1) {
          if(res?.data?.status == 2) {
            clearInterval(this.alipayTimer);
            this.alipayTimer = -1;
            Toast("充值已到账，可查看余额变化");
            this.GetUserCardInfo();
            this.$router.push({path: '/my/ecard/recharge/rechargeResult', query: {recharge: res.data}})
          } else if(res?.data?.status == 3) {
            Toast("卡系统充值失败");
            clearInterval(this.alipayTimer);
            this.alipayTimer = -1;
          }
        }
      }).catch(err => {})
    },
    // 加油卡信息
    GetUserCardInfo() {
      return GetUserCardInfo({
        appid: 2,
        apptoken: storage.ss.get("apptoken"),
      }).then((res) => {
        if (res.infoCode == 1) {
          this.formatItem = res.data;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.recharge {
  .my_card_details {
    width: 92vw;
    height: 285px;
    border-radius: 20px;
    background-image: url("../../assets/images/common/ecard_bg.png"); // 背景图片
    background-size: cover;
    padding: 20px 0;
    @include margin(20px, 0, 20px 0);
    .card_right {
      flex: 1;
      line-height: 38px;
      div {
        padding: 6px 0;
      }
    }
  }
  /deep/ .van-notice-bar {
    height: 34px;
  }
  .pay_box {
    background-color: #fff;
    width: 92vw;
    margin: 6vw auto 0;
    border-radius: 20px;
    padding: 4vw 0px 8vw;
  }
  .pay-mode {
    .pay-mode-cs {
      padding: 4vw;
      text-align: left;
      // font-size: 16px;
    }
    .van-cell {
      text-align: left;
      padding: 3vw 4vw;
      height: 14vw;
      align-items: center;
      .van-cell__left-icon {
        height: 30px;
        line-height: 30px;
        .van-icon__image {
          width: 30px;
          height: 30px;
        }
      }
    }
    /deep/ .van-radio__icon--checked {
      .van-icon {
        background-color: #ff9200 ;
        border-color: #ff9200;
      }
    } 
  }
  .check_box {
    padding: 0 20px;
    /deep/ .van-checkbox__label {
      text-align: left;
      .text {
        color: #ff9200;
      }
    }
  }
  .recharge-amount {
    .recharge-amount-cs {
      padding: 20px 16px;
      text-align: left;
      margin-left: 7px;
    }
    .amount-list {
      padding: 0 20px 20px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      div {
        width: 156px;
        height: 68px;
        line-height: 68px;
        background: url("~@/assets/images/gasstationmap/youpinB.png");
        background-size: 100% 100%;
      }
      .activedOilType {
        background: url("~@/assets/images/gasstationmap/youpinA.png");
        background-size: 100% 100%;
      }
    }
  }
  .notice {
    padding: 4vw 2vw;
    text-align: left;
  }
  /deep/ .van-button {
    width: 92vw;
    height: 44px;
    margin: 20px 0;
  }
}
</style>