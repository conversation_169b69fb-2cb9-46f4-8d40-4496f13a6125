<!--
 * @Author: Yongon
 * @LastEditors: ltw <EMAIL>
 * @Description: 加油
-->
<template>
  <div class="pay-content">
    <div class="placeholder" :style="{ height: placeholderHeigth }">
      <div
        class="content"
        :style="{ height: contentHeight, paddingTop: topSafeArea }"
      ></div>
    </div>
    <div class="bg-box">
      <div class="img-box">
        <img src="@/assets/images/common/wait.png" />
      </div>
      <div class="title">支付中</div>
      <div class="content">几秒后将自动返回支付结果，请不要离开页面</div>
    </div>
  </div>
</template>

<script>
import { storage } from "Utils/common/";
export default {
  props: {},
  data() {
    return {};
  },
  computed: {
    topSafeArea() {
      return Number(storage.ss.get("statusHeight") ?? 30) + "px";
    },
    placeholderHeigth() {
      return Number((storage.ss.get("statusHeight") ?? 30) + 46) + "px";
    },
    contentHeight() {
      return "46px";
    },
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.pay-content {
  background: #f1f1f1;
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 2;
}

.bg-box {
  background: #fff;
  padding: 40px 0;
  box-shadow: 2px 2px 1px 1px #c0baba;
  .img-box {
    text-align: center;

    img {
      width: 150px;
      height: 150px;
    }
  }

  .title {
    margin-top: 40px;
    text-align: center;
    font-size: 38px;
    font-weight: 600;
  }

  .content {
    margin-top: 35px;
    text-align: center;
    font-size: 24px;
    color: #6e6a6a;
  }
}
</style>
